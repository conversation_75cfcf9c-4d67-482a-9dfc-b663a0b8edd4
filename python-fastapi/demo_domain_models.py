#!/usr/bin/env python3
"""
Demonstration script for the domain models.

This script shows how to use the Wishlist and WishlistItem domain models
with their business methods and factories.
"""

from app.models.domain import (
    Wishlist, 
    WishlistItem, 
    WishlistFactory, 
    WishlistItemFactory,
    create_test_data
)


def main():
    print("=== Domain Models Demonstration ===\n")
    
    # 1. Create a wishlist using the factory method
    print("1. Creating a new wishlist using factory method:")
    wishlist = Wishlist.create_new(
        user_id="demo_user_123",
        name="My Baby Registry",
        is_default=True
    )
    print(f"   Created wishlist: {wishlist.name}")
    print(f"   Wishlist ID: {wishlist.wishlist_id}")
    print(f"   Share hash: {wishlist.share_hash}")
    print(f"   Is default: {wishlist.is_default}")
    print(f"   Is empty: {wishlist.is_empty()}")
    print()
    
    # 2. Add items to the wishlist
    print("2. Adding items to the wishlist:")
    item1 = wishlist.add_item("STROLLER_001", "Premium baby stroller for walks")
    print(f"   Added item: {item1.product_id} with notes: {item1.notes}")
    
    item2 = wishlist.add_item("CRIB_002", "Convertible crib for nursery")
    print(f"   Added item: {item2.product_id} with notes: {item2.notes}")
    
    item3 = wishlist.add_item("TOYS_003")  # No notes
    print(f"   Added item: {item3.product_id} (no notes)")
    
    print(f"   Total items: {wishlist.get_item_count()}")
    print(f"   Product IDs: {wishlist.get_product_ids()}")
    print()
    
    # 3. Demonstrate business methods
    print("3. Using business methods:")
    
    # Check if item exists
    has_stroller = wishlist.has_item("STROLLER_001")
    print(f"   Has stroller: {has_stroller}")
    
    # Update item notes
    updated = wishlist.update_item_notes("TOYS_003", "Educational toys for development")
    print(f"   Updated toy notes: {updated}")
    
    # Get specific item
    toy_item = wishlist.get_item("TOYS_003")
    print(f"   Toy item notes: {toy_item.notes}")
    
    # Remove an item
    removed = wishlist.remove_item("CRIB_002")
    print(f"   Removed crib: {removed}")
    print(f"   Items after removal: {wishlist.get_item_count()}")
    print()
    
    # 4. Update wishlist properties
    print("4. Updating wishlist properties:")
    wishlist.update_name("Updated Baby Registry")
    print(f"   New name: {wishlist.name}")
    
    wishlist.update_privacy(True)
    print(f"   Is public: {wishlist.is_public}")
    
    old_hash = wishlist.share_hash
    new_hash = wishlist.regenerate_share_hash()
    print(f"   Regenerated share hash: {old_hash != new_hash}")
    print()
    
    # 5. Using factories for testing
    print("5. Using factories for test data:")
    
    # Create a default wishlist
    default_wishlist = WishlistFactory.create_default_wishlist(
        user_id="test_user_456",
        item_count=5
    )
    print(f"   Default wishlist: {default_wishlist.name}")
    print(f"   Items: {default_wishlist.get_item_count()}")
    print(f"   Is default: {default_wishlist.is_default}")
    
    # Create a public wishlist
    public_wishlist = WishlistFactory.create_public_wishlist(
        user_id="test_user_789"
    )
    print(f"   Public wishlist: {public_wishlist.name}")
    print(f"   Items: {public_wishlist.get_item_count()}")
    print(f"   Is public: {public_wishlist.is_public}")
    
    # Create multiple wishlists for a user
    user_wishlists = WishlistFactory.create_user_wishlists(
        user_id="test_user_999",
        count=3
    )
    print(f"   Created {len(user_wishlists)} wishlists for user")
    default_count = sum(1 for w in user_wishlists if w.is_default)
    print(f"   Default wishlists: {default_count}")
    print()
    
    # 6. Create test data for multiple users
    print("6. Creating comprehensive test data:")
    test_data = create_test_data(user_count=3)
    
    for user_id, wishlists in test_data.items():
        print(f"   User {user_id}: {len(wishlists)} wishlists")
        total_items = sum(w.get_item_count() for w in wishlists)
        print(f"     Total items across all wishlists: {total_items}")
    
    print("\n=== Demonstration Complete ===")


if __name__ == "__main__":
    main()