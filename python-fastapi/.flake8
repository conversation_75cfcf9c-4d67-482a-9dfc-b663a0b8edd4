[flake8]
max-line-length = 88
extend-ignore = 
    E203,  # whitespace before ':'
    W503,  # line break before binary operator
    E501,  # line too long (handled by black)
    F401,  # imported but unused (handled by isort)
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    .mypy_cache,
    .pytest_cache,
    htmlcov,
    migrations
per-file-ignores =
    __init__.py:F401
    tests/*:S101,S106
max-complexity = 10
import-order-style = google
application-import-names = app,tests