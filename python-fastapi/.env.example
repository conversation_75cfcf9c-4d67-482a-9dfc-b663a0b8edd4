# Environment Configuration
ENVIRONMENT=development
DEBUG=true

# Application
APP_NAME="Mumzworld Wishlist Service"
VERSION=2.0.0

# API Configuration
API_V1_PREFIX=/api/v1
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Database Configuration
DYNAMODB_ENDPOINT=http://localhost:8001
DYNAMODB_REGION=us-east-1
AWS_ACCESS_KEY_ID=test
AWS_SECRET_ACCESS_KEY=test

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_MAX_CONNECTIONS=100

# Algolia Configuration
ALGOLIA_APP_ID=your_algolia_app_id
ALGOLIA_API_KEY=your_algolia_api_key
ALGOLIA_TIMEOUT=5

# CloudFront Configuration
CLOUDFRONT_DISTRIBUTION_ID=your_distribution_id
CLOUDFRONT_KEY_PAIR_ID=your_key_pair_id

# Performance Configuration
MAX_CONCURRENT_REQUESTS=1000
REQUEST_TIMEOUT=30
CACHE_TTL_DEFAULT=300
CACHE_TTL_PRODUCTS=3600

# Monitoring Configuration
ENABLE_METRICS=true
ENABLE_TRACING=true
LOG_LEVEL=INFO

# Rate Limiting
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60