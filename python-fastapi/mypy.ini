[mypy]
python_version = 3.11
check_untyped_defs = True
disallow_any_generics = True
disallow_incomplete_defs = True
disallow_untyped_defs = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_return_any = True
strict_equality = True
show_error_codes = True
show_column_numbers = True
pretty = True

# Per-module options
[mypy-tests.*]
disallow_untyped_defs = False

[mypy-aioboto3.*]
ignore_missing_imports = True

[mypy-botocore.*]
ignore_missing_imports = True

[mypy-celery.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-prometheus_client.*]
ignore_missing_imports = True

[mypy-structlog.*]
ignore_missing_imports = True