{"python.defaultInterpreterPath": "./.venv/bin/python", "python.formatting.provider": "black", "python.formatting.blackArgs": ["--line-length=88"], "python.linting.enabled": true, "python.linting.flake8Enabled": true, "python.linting.mypyEnabled": true, "python.linting.pylintEnabled": false, "python.sortImports.args": ["--profile=black"], "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "files.exclude": {"**/__pycache__": true, "**/*.pyc": true, ".mypy_cache": true, ".pytest_cache": true, "htmlcov": true}, "python.testing.pytestEnabled": true, "python.testing.pytestArgs": ["tests"], "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "files.associations": {"*.yaml": "yaml", "*.yml": "yaml", "Dockerfile*": "dockerfile", ".dockerignore": "ignore"}}