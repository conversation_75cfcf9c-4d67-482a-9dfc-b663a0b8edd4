{"version": "0.2.0", "configurations": [{"name": "FastAPI Debug", "type": "python", "request": "launch", "program": "${workspaceFolder}/app/main.py", "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "ENVIRONMENT": "development", "DEBUG": "true"}, "args": [], "jinja": true, "justMyCode": false}, {"name": "FastAPI Uvicorn", "type": "python", "request": "launch", "module": "u<PERSON><PERSON>", "args": ["app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"], "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}", "ENVIRONMENT": "development", "DEBUG": "true"}, "jinja": true, "justMyCode": false}, {"name": "Pytest Debug", "type": "python", "request": "launch", "module": "pytest", "args": ["${workspaceFolder}/tests", "-v", "--tb=short"], "console": "integratedTerminal", "env": {"PYTHONPATH": "${workspaceFolder}"}, "jinja": true, "justMyCode": false}]}