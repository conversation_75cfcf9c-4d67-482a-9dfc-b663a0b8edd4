<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CopilotDiffPersistence">
    <option name="pendingDiffs">
      <map>
        <entry key="$PROJECT_DIR$/app/api/error_handlers.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/app/api/error_handlers.py" />
              <option name="originalContent" value="&quot;&quot;&quot;&#10;Global error handlers for the FastAPI application.&#10;&#10;This module provides centralized error handling with consistent response formats,&#10;proper logging, and correlation ID tracking.&#10;&quot;&quot;&quot;&#10;import logging&#10;from typing import Union&#10;from datetime import datetime, timezone&#10;from fastapi import Request, HTTPException&#10;from fastapi.responses import JSONResponse&#10;from fastapi.exceptions import RequestValidationError&#10;from pydantic import ValidationError&#10;from starlette.exceptions import HTTPException as StarletteHTTPException&#10;&#10;from app.schemas.base import ErrorResponseSchema&#10;from app.core.exceptions import (&#10;    WishlistServiceException,&#10;    WishlistNotFoundError,&#10;    ValidationError as CustomValidationError,&#10;    ExternalServiceError&#10;)&#10;from app.utils.helpers import generate_correlation_id&#10;&#10;logger = logging.getLogger(__name__)&#10;&#10;&#10;async def http_exception_handler(request: Request, exc: HTTPException) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle HTTP exceptions with consistent error response format.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    # Log the error&#10;    logger.warning(&#10;        f&quot;HTTP exception: {exc.status_code} - {exc.detail}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;status_code&quot;: exc.status_code,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;detail&quot;: exc.detail&#10;        }&#10;    )&#10;&#10;    # If detail is already a dict (from our custom exceptions), use it directly&#10;    if isinstance(exc.detail, dict):&#10;        return JSONResponse(&#10;            status_code=exc.status_code,&#10;            content=exc.detail&#10;        )&#10;&#10;    # Otherwise, create a standard error response&#10;&#10;    error_response = ErrorResponseSchema(&#10;        code=f&quot;HTTP_{exc.status_code}&quot;,&#10;        message=str(exc.detail),&#10;        correlation_id=correlation_id&#10;    )&#10;&#10;    return JSONResponse(&#10;        status_code=exc.status_code,&#10;        content={&#10;            &quot;error&quot;: error_response.model_dump(),&#10;            &quot;request_id&quot;: correlation_id,&#10;            &quot;timestamp&quot;: error_response.timestamp&#10;        }&#10;    )&#10;&#10;&#10;async def validation_exception_handler(request: Request, exc: RequestValidationError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle Pydantic validation errors with detailed field information.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    # Extract validation errors&#10;    validation_errors = []&#10;    for error in exc.errors():&#10;        field_path = &quot; -&gt; &quot;.join(str(loc) for loc in error[&quot;loc&quot;])&#10;        validation_errors.append({&#10;            &quot;field&quot;: field_path,&#10;            &quot;message&quot;: error[&quot;msg&quot;],&#10;            &quot;type&quot;: error[&quot;type&quot;],&#10;            &quot;input&quot;: error.get(&quot;input&quot;)&#10;        })&#10;&#10;    # Log validation error&#10;    logger.warning(&#10;        f&quot;Validation error: {len(validation_errors)} field(s) failed validation&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;validation_errors&quot;: validation_errors&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;VALIDATION_ERROR&quot;,&#10;            &quot;message&quot;: &quot;Request validation failed&quot;,&#10;            &quot;details&quot;: validation_errors,&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=422,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def wishlist_not_found_handler(request: Request, exc: WishlistNotFoundError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle wishlist not found errors.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    logger.info(&#10;        f&quot;Wishlist not found: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error&quot;: str(exc)&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;WISHLIST_NOT_FOUND&quot;,&#10;            &quot;message&quot;: str(exc),&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=404,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def custom_validation_error_handler(request: Request, exc: CustomValidationError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle custom validation errors.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    logger.warning(&#10;        f&quot;Custom validation error: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error&quot;: str(exc)&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;VALIDATION_ERROR&quot;,&#10;            &quot;message&quot;: str(exc),&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=400,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def external_service_error_handler(request: Request, exc: ExternalServiceError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle external service errors.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    logger.error(&#10;        f&quot;External service error: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error&quot;: str(exc)&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;EXTERNAL_SERVICE_ERROR&quot;,&#10;            &quot;message&quot;: &quot;External service temporarily unavailable&quot;,&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: ErrorResponseSchema().timestamp&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=503,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def generic_exception_handler(request: Request, exc: Exception) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle unexpected exceptions with proper logging and safe error responses.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    # Log the full exception details&#10;    logger.error(&#10;        f&quot;Unhandled exception: {type(exc).__name__}: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error_type&quot;: type(exc).__name__,&#10;            &quot;error&quot;: str(exc)&#10;        },&#10;        exc_info=True  # Include stack trace&#10;    )&#10;&#10;    # Return a safe error response without exposing internal details&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;INTERNAL_SERVER_ERROR&quot;,&#10;            &quot;message&quot;: &quot;An unexpected error occurred. Please try again later.&quot;,&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=500,&#10;        content=error_response&#10;    )&#10;&#10;&#10;def register_error_handlers(app):&#10;    &quot;&quot;&quot;&#10;    Register all error handlers with the FastAPI application.&#10;    &quot;&quot;&quot;&#10;    # HTTP exceptions&#10;    app.add_exception_handler(HTTPException, http_exception_handler)&#10;    app.add_exception_handler(StarletteHTTPException, http_exception_handler)&#10;&#10;    # Validation errors&#10;    app.add_exception_handler(RequestValidationError, validation_exception_handler)&#10;    app.add_exception_handler(ValidationError, validation_exception_handler)&#10;&#10;    # Custom application exceptions&#10;    app.add_exception_handler(WishlistNotFoundError, wishlist_not_found_handler)&#10;    app.add_exception_handler(CustomValidationError, custom_validation_error_handler)&#10;    app.add_exception_handler(ExternalServiceError, external_service_error_handler)&#10;&#10;    # Generic exception handler (catch-all)&#10;    app.add_exception_handler(Exception, generic_exception_handler)&#10;" />
              <option name="updatedContent" value="&quot;&quot;&quot;&#10;Global error handlers for the FastAPI application.&#10;&#10;This module provides centralized error handling with consistent response formats,&#10;proper logging, and correlation ID tracking.&#10;&quot;&quot;&quot;&#10;import logging&#10;from typing import Union&#10;from datetime import datetime, timezone&#10;from fastapi import Request, HTTPException&#10;from fastapi.responses import JSONResponse&#10;from fastapi.exceptions import RequestValidationError&#10;from pydantic import ValidationError&#10;from starlette.exceptions import HTTPException as StarletteHTTPException&#10;&#10;from app.schemas.base import ErrorResponseSchema&#10;from app.core.exceptions import (&#10;    WishlistServiceException,&#10;    WishlistNotFoundError,&#10;    ValidationError as CustomValidationError,&#10;    ExternalServiceError&#10;)&#10;from app.utils.helpers import generate_correlation_id&#10;&#10;logger = logging.getLogger(__name__)&#10;&#10;&#10;async def http_exception_handler(request: Request, exc: HTTPException) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle HTTP exceptions with consistent error response format.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    # Log the error&#10;    logger.warning(&#10;        f&quot;HTTP exception: {exc.status_code} - {exc.detail}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;status_code&quot;: exc.status_code,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;detail&quot;: exc.detail&#10;        }&#10;    )&#10;&#10;    # If detail is already a dict (from our custom exceptions), use it directly&#10;    if isinstance(exc.detail, dict):&#10;        return JSONResponse(&#10;            status_code=exc.status_code,&#10;            content=exc.detail&#10;        )&#10;&#10;    # Otherwise, create a standard error response&#10;&#10;    error_response = ErrorResponseSchema(&#10;        code=f&quot;HTTP_{exc.status_code}&quot;,&#10;        message=str(exc.detail),&#10;        correlation_id=correlation_id&#10;    )&#10;&#10;    return JSONResponse(&#10;        status_code=exc.status_code,&#10;        content={&#10;            &quot;error&quot;: error_response.model_dump(),&#10;            &quot;request_id&quot;: correlation_id,&#10;            &quot;timestamp&quot;: error_response.timestamp&#10;        }&#10;    )&#10;&#10;&#10;async def validation_exception_handler(request: Request, exc: RequestValidationError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle Pydantic validation errors with detailed field information.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    # Extract validation errors&#10;    validation_errors = []&#10;    for error in exc.errors():&#10;        field_path = &quot; -&gt; &quot;.join(str(loc) for loc in error[&quot;loc&quot;])&#10;        validation_errors.append({&#10;            &quot;field&quot;: field_path,&#10;            &quot;message&quot;: error[&quot;msg&quot;],&#10;            &quot;type&quot;: error[&quot;type&quot;],&#10;            &quot;input&quot;: error.get(&quot;input&quot;)&#10;        })&#10;&#10;    # Log validation error&#10;    logger.warning(&#10;        f&quot;Validation error: {len(validation_errors)} field(s) failed validation&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;validation_errors&quot;: validation_errors&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;VALIDATION_ERROR&quot;,&#10;            &quot;message&quot;: &quot;Request validation failed&quot;,&#10;            &quot;details&quot;: validation_errors,&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=422,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def wishlist_not_found_handler(request: Request, exc: WishlistNotFoundError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle wishlist not found errors.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    logger.info(&#10;        f&quot;Wishlist not found: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error&quot;: str(exc)&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;WISHLIST_NOT_FOUND&quot;,&#10;            &quot;message&quot;: str(exc),&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=404,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def custom_validation_error_handler(request: Request, exc: CustomValidationError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle custom validation errors.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    logger.warning(&#10;        f&quot;Custom validation error: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error&quot;: str(exc)&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;VALIDATION_ERROR&quot;,&#10;            &quot;message&quot;: str(exc),&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=400,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def external_service_error_handler(request: Request, exc: ExternalServiceError) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle external service errors.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    logger.error(&#10;        f&quot;External service error: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error&quot;: str(exc)&#10;        }&#10;    )&#10;&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;EXTERNAL_SERVICE_ERROR&quot;,&#10;            &quot;message&quot;: &quot;External service temporarily unavailable&quot;,&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=503,&#10;        content=error_response&#10;    )&#10;&#10;&#10;async def generic_exception_handler(request: Request, exc: Exception) -&gt; JSONResponse:&#10;    &quot;&quot;&quot;&#10;    Handle unexpected exceptions with proper logging and safe error responses.&#10;    &quot;&quot;&quot;&#10;    correlation_id = getattr(request.state, &quot;request_id&quot;, generate_correlation_id())&#10;&#10;    # Log the full exception details&#10;    logger.error(&#10;        f&quot;Unhandled exception: {type(exc).__name__}: {exc}&quot;,&#10;        extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;path&quot;: str(request.url.path),&#10;            &quot;method&quot;: request.method,&#10;            &quot;error_type&quot;: type(exc).__name__,&#10;            &quot;error&quot;: str(exc)&#10;        },&#10;        exc_info=True  # Include stack trace&#10;    )&#10;&#10;    # Return a safe error response without exposing internal details&#10;    error_response = {&#10;        &quot;error&quot;: {&#10;            &quot;code&quot;: &quot;INTERNAL_SERVER_ERROR&quot;,&#10;            &quot;message&quot;: &quot;An unexpected error occurred. Please try again later.&quot;,&#10;            &quot;correlation_id&quot;: correlation_id&#10;        },&#10;        &quot;request_id&quot;: correlation_id,&#10;        &quot;timestamp&quot;: datetime.now(timezone.utc).isoformat()&#10;    }&#10;&#10;    return JSONResponse(&#10;        status_code=500,&#10;        content=error_response&#10;    )&#10;&#10;&#10;def register_error_handlers(app):&#10;    &quot;&quot;&quot;&#10;    Register all error handlers with the FastAPI application.&#10;    &quot;&quot;&quot;&#10;    # HTTP exceptions&#10;    app.add_exception_handler(HTTPException, http_exception_handler)&#10;    app.add_exception_handler(StarletteHTTPException, http_exception_handler)&#10;&#10;    # Validation errors&#10;    app.add_exception_handler(RequestValidationError, validation_exception_handler)&#10;    app.add_exception_handler(ValidationError, validation_exception_handler)&#10;&#10;    # Custom application exceptions&#10;    app.add_exception_handler(WishlistNotFoundError, wishlist_not_found_handler)&#10;    app.add_exception_handler(CustomValidationError, custom_validation_error_handler)&#10;    app.add_exception_handler(ExternalServiceError, external_service_error_handler)&#10;&#10;    # Generic exception handler (catch-all)&#10;    app.add_exception_handler(Exception, generic_exception_handler)" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/app/models/domain/wishlist.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/app/models/domain/wishlist.py" />
              <option name="originalContent" value="&quot;&quot;&quot;&#10;Domain models for the Wishlist service.&#10;&#10;This module contains the core business entities with their business logic,&#10;validation rules, and common operations.&#10;&quot;&quot;&quot;&#10;&#10;from dataclasses import dataclass, field&#10;from datetime import datetime&#10;from typing import List, Optional, Dict, Any&#10;from uuid import uuid4&#10;import secrets&#10;import string&#10;&#10;&#10;@dataclass&#10;class WishlistItem:&#10;    &quot;&quot;&quot;&#10;    Domain model representing a single item in a wishlist.&#10;    &#10;    Contains product information and metadata about when it was added.&#10;    &quot;&quot;&quot;&#10;    product_id: str&#10;    notes: Optional[str] = None&#10;    added_at: Optional[datetime] = None&#10;    product: Optional[Dict[str, Any]] = field(default=None, repr=False)  # Enriched product data&#10;    &#10;    def __post_init__(self):&#10;        &quot;&quot;&quot;Validate and set defaults after initialization.&quot;&quot;&quot;&#10;        if not self.product_id or not self.product_id.strip():&#10;            raise ValueError(&quot;product_id cannot be empty&quot;)&#10;        &#10;        if self.added_at is None:&#10;            self.added_at = datetime.utcnow()&#10;        &#10;        # Validate notes length&#10;        if self.notes and len(self.notes) &gt; 500:&#10;            raise ValueError(&quot;notes cannot exceed 500 characters&quot;)&#10;    &#10;    def update_notes(self, notes: Optional[str]) -&gt; None:&#10;        &quot;&quot;&quot;Update the notes for this item.&quot;&quot;&quot;&#10;        if notes and len(notes) &gt; 500:&#10;            raise ValueError(&quot;notes cannot exceed 500 characters&quot;)&#10;        self.notes = notes&#10;    &#10;    def has_product_data(self) -&gt; bool:&#10;        &quot;&quot;&quot;Check if this item has enriched product data.&quot;&quot;&quot;&#10;        return self.product is not None and bool(self.product)&#10;    &#10;    def get_product_title(self) -&gt; Optional[str]:&#10;        &quot;&quot;&quot;Get the product title from enriched data.&quot;&quot;&quot;&#10;        if self.has_product_data():&#10;            return self.product.get('title') or self.product.get('name')&#10;        return None&#10;&#10;&#10;@dataclass&#10;class Wishlist:&#10;    &quot;&quot;&quot;&#10;    Domain model representing a user's wishlist.&#10;    &#10;    Contains business logic for managing wishlist items, privacy settings,&#10;    and common operations like adding/removing items.&#10;    &quot;&quot;&quot;&#10;    user_id: str&#10;    wishlist_id: str&#10;    name: str&#10;    is_default: bool&#10;    is_public: bool&#10;    share_hash: str&#10;    items: List[WishlistItem]&#10;    created_at: datetime&#10;    updated_at: datetime&#10;    &#10;    def __post_init__(self):&#10;        &quot;&quot;&quot;Validate wishlist data after initialization.&quot;&quot;&quot;&#10;        if not self.user_id or not self.user_id.strip():&#10;            raise ValueError(&quot;user_id cannot be empty&quot;)&#10;        &#10;        if not self.wishlist_id or not self.wishlist_id.strip():&#10;            raise ValueError(&quot;wishlist_id cannot be empty&quot;)&#10;        &#10;        if not self.name or not self.name.strip():&#10;            raise ValueError(&quot;name cannot be empty&quot;)&#10;        &#10;        if len(self.name) &gt; 255:&#10;            raise ValueError(&quot;name cannot exceed 255 characters&quot;)&#10;        &#10;        if not self.share_hash or len(self.share_hash) &lt; 16:&#10;            raise ValueError(&quot;share_hash must be at least 16 characters&quot;)&#10;        &#10;        # Ensure items is a list&#10;        if self.items is None:&#10;            self.items = []&#10;    &#10;    def add_item(self, product_id: str, notes: Optional[str] = None) -&gt; WishlistItem:&#10;        &quot;&quot;&quot;&#10;        Add or update an item in the wishlist.&#10;        &#10;        Args:&#10;            product_id: The ID of the product to add&#10;            notes: Optional notes about the item&#10;            &#10;        Returns:&#10;            The WishlistItem that was added or updated&#10;            &#10;        Raises:&#10;            ValueError: If product_id is invalid&#10;        &quot;&quot;&quot;&#10;        if not product_id or not product_id.strip():&#10;            raise ValueError(&quot;product_id cannot be empty&quot;)&#10;        &#10;        # Remove existing item if present&#10;        self.items = [item for item in self.items if item.product_id != product_id]&#10;        &#10;        # Create and add new item&#10;        new_item = WishlistItem(&#10;            product_id=product_id,&#10;            notes=notes,&#10;            added_at=datetime.utcnow()&#10;        )&#10;        self.items.append(new_item)&#10;        self.updated_at = datetime.utcnow()&#10;        &#10;        return new_item&#10;    &#10;    def remove_item(self, product_id: str) -&gt; bool:&#10;        &quot;&quot;&quot;&#10;        Remove an item from the wishlist.&#10;        &#10;        Args:&#10;            product_id: The ID of the product to remove&#10;            &#10;        Returns:&#10;            True if the item was removed, False if it wasn't found&#10;        &quot;&quot;&quot;&#10;        if not product_id:&#10;            return False&#10;        &#10;        original_count = len(self.items)&#10;        self.items = [item for item in self.items if item.product_id != product_id]&#10;        &#10;        if len(self.items) &lt; original_count:&#10;            self.updated_at = datetime.utcnow()&#10;            return True&#10;        return False&#10;    &#10;    def has_item(self, product_id: str) -&gt; bool:&#10;        &quot;&quot;&quot;Check if the wishlist contains a specific product.&quot;&quot;&quot;&#10;        return any(item.product_id == product_id for item in self.items)&#10;    &#10;    def get_item(self, product_id: str) -&gt; Optional[WishlistItem]:&#10;        &quot;&quot;&quot;Get a specific item from the wishlist.&quot;&quot;&quot;&#10;        for item in self.items:&#10;            if item.product_id == product_id:&#10;                return item&#10;        return None&#10;    &#10;    def update_item_notes(self, product_id: str, notes: Optional[str]) -&gt; bool:&#10;        &quot;&quot;&quot;&#10;        Update notes for a specific item.&#10;        &#10;        Args:&#10;            product_id: The ID of the product to update&#10;            notes: New notes for the item&#10;            &#10;        Returns:&#10;            True if the item was found and updated, False otherwise&#10;        &quot;&quot;&quot;&#10;        item = self.get_item(product_id)&#10;        if item:&#10;            item.update_notes(notes)&#10;            self.updated_at = datetime.utcnow()&#10;            return True&#10;        return False&#10;    &#10;    def clear_items(self) -&gt; int:&#10;        &quot;&quot;&quot;&#10;        Remove all items from the wishlist.&#10;        &#10;        Returns:&#10;            The number of items that were removed&#10;        &quot;&quot;&quot;&#10;        count = len(self.items)&#10;        self.items = []&#10;        if count &gt; 0:&#10;            self.updated_at = datetime.utcnow()&#10;        return count&#10;    &#10;    def get_item_count(self) -&gt; int:&#10;        &quot;&quot;&quot;Get the total number of items in the wishlist.&quot;&quot;&quot;&#10;        return len(self.items)&#10;    &#10;    def update_privacy(self, is_public: bool) -&gt; None:&#10;        &quot;&quot;&quot;Update the privacy status of the wishlist.&quot;&quot;&quot;&#10;        if self.is_public != is_public:&#10;            self.is_public = is_public&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def regenerate_share_hash(self) -&gt; str:&#10;        &quot;&quot;&quot;&#10;        Generate a new share hash for the wishlist.&#10;        &#10;        Returns:&#10;            The new share hash&#10;        &quot;&quot;&quot;&#10;        self.share_hash = self._generate_share_hash()&#10;        self.updated_at = datetime.utcnow()&#10;        return self.share_hash&#10;    &#10;    def update_name(self, name: str) -&gt; None:&#10;        &quot;&quot;&quot;&#10;        Update the wishlist name.&#10;        &#10;        Args:&#10;            name: New name for the wishlist&#10;            &#10;        Raises:&#10;            ValueError: If name is invalid&#10;        &quot;&quot;&quot;&#10;        if not name or not name.strip():&#10;            raise ValueError(&quot;name cannot be empty&quot;)&#10;        &#10;        if len(name) &gt; 255:&#10;            raise ValueError(&quot;name cannot exceed 255 characters&quot;)&#10;        &#10;        if self.name != name:&#10;            self.name = name.strip()&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def set_as_default(self) -&gt; None:&#10;        &quot;&quot;&quot;Mark this wishlist as the default for the user.&quot;&quot;&quot;&#10;        if not self.is_default:&#10;            self.is_default = True&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def unset_as_default(self) -&gt; None:&#10;        &quot;&quot;&quot;Remove the default status from this wishlist.&quot;&quot;&quot;&#10;        if self.is_default:&#10;            self.is_default = False&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def get_product_ids(self) -&gt; List[str]:&#10;        &quot;&quot;&quot;Get a list of all product IDs in this wishlist.&quot;&quot;&quot;&#10;        return [item.product_id for item in self.items]&#10;    &#10;    def is_empty(self) -&gt; bool:&#10;        &quot;&quot;&quot;Check if the wishlist has no items.&quot;&quot;&quot;&#10;        return len(self.items) == 0&#10;    &#10;    def get_items_added_since(self, since: datetime) -&gt; List[WishlistItem]:&#10;        &quot;&quot;&quot;Get items that were added after a specific datetime.&quot;&quot;&quot;&#10;        return [&#10;            item for item in self.items &#10;            if item.added_at and item.added_at &gt; since&#10;        ]&#10;    &#10;    @staticmethod&#10;    def _generate_share_hash() -&gt; str:&#10;        &quot;&quot;&quot;Generate a secure random share hash.&quot;&quot;&quot;&#10;        alphabet = string.ascii_letters + string.digits&#10;        return ''.join(secrets.choice(alphabet) for _ in range(32))&#10;    &#10;    @classmethod&#10;    def create_new(&#10;        cls,&#10;        user_id: str,&#10;        name: str,&#10;        is_default: bool = False,&#10;        is_public: bool = False&#10;    ) -&gt; 'Wishlist':&#10;        &quot;&quot;&quot;&#10;        Factory method to create a new wishlist with default values.&#10;        &#10;        Args:&#10;            user_id: The ID of the user who owns the wishlist&#10;            name: The name of the wishlist&#10;            is_default: Whether this should be the default wishlist&#10;            is_public: Whether this wishlist should be public&#10;            &#10;        Returns:&#10;            A new Wishlist instance&#10;            &#10;        Raises:&#10;            ValueError: If any parameters are invalid&#10;        &quot;&quot;&quot;&#10;        now = datetime.utcnow()&#10;        &#10;        return cls(&#10;            user_id=user_id,&#10;            wishlist_id=str(uuid4()),&#10;            name=name,&#10;            is_default=is_default,&#10;            is_public=is_public,&#10;            share_hash=cls._generate_share_hash(),&#10;            items=[],&#10;            created_at=now,&#10;            updated_at=now&#10;        )" />
              <option name="updatedContent" value="&quot;&quot;&quot;&#10;Domain models for the Wishlist service.&#10;&#10;This module contains the core business entities with their business logic,&#10;validation rules, and common operations.&#10;&quot;&quot;&quot;&#10;&#10;from dataclasses import dataclass, field&#10;from datetime import datetime&#10;from typing import List, Optional, Dict, Any&#10;from uuid import uuid4&#10;import secrets&#10;import string&#10;&#10;&#10;@dataclass&#10;class WishlistItem:&#10;    &quot;&quot;&quot;&#10;    Domain model representing a single item in a wishlist.&#10;    &#10;    Contains product information and metadata about when it was added.&#10;    &quot;&quot;&quot;&#10;    product_id: str&#10;    notes: Optional[str] = None&#10;    added_at: Optional[datetime] = None&#10;    product: Optional[Dict[str, Any]] = field(default=None, repr=False)  # Enriched product data&#10;    &#10;    def __post_init__(self):&#10;        &quot;&quot;&quot;Validate and set defaults after initialization.&quot;&quot;&quot;&#10;        if not self.product_id or not self.product_id.strip():&#10;            raise ValueError(&quot;product_id cannot be empty&quot;)&#10;        &#10;        if self.added_at is None:&#10;            self.added_at = datetime.utcnow()&#10;        &#10;        # Validate notes length&#10;        if self.notes and len(self.notes) &gt; 500:&#10;            raise ValueError(&quot;notes cannot exceed 500 characters&quot;)&#10;    &#10;    def update_notes(self, notes: Optional[str]) -&gt; None:&#10;        &quot;&quot;&quot;Update the notes for this item.&quot;&quot;&quot;&#10;        if notes and len(notes) &gt; 500:&#10;            raise ValueError(&quot;notes cannot exceed 500 characters&quot;)&#10;        self.notes = notes&#10;    &#10;    def has_product_data(self) -&gt; bool:&#10;        &quot;&quot;&quot;Check if this item has enriched product data.&quot;&quot;&quot;&#10;        return self.product is not None and bool(self.product)&#10;    &#10;    def get_product_title(self) -&gt; Optional[str]:&#10;        &quot;&quot;&quot;Get the product title from enriched data.&quot;&quot;&quot;&#10;        if self.has_product_data():&#10;            return self.product.get('title') or self.product.get('name')&#10;        return None&#10;&#10;&#10;@dataclass&#10;class Wishlist:&#10;    &quot;&quot;&quot;&#10;    Domain model representing a user's wishlist.&#10;    &#10;    Contains business logic for managing wishlist items, privacy settings,&#10;    and common operations like adding/removing items.&#10;    &quot;&quot;&quot;&#10;    user_id: str&#10;    wishlist_id: str&#10;    name: str&#10;    is_default: bool&#10;    is_public: bool&#10;    share_hash: str&#10;    items: List[WishlistItem]&#10;    created_at: datetime&#10;    updated_at: datetime&#10;    &#10;    def __post_init__(self):&#10;        &quot;&quot;&quot;Validate wishlist data after initialization.&quot;&quot;&quot;&#10;        if not self.user_id or not self.user_id.strip():&#10;            raise ValueError(&quot;user_id cannot be empty&quot;)&#10;        &#10;        if not self.wishlist_id or not self.wishlist_id.strip():&#10;            raise ValueError(&quot;wishlist_id cannot be empty&quot;)&#10;        &#10;        if not self.name or not self.name.strip():&#10;            raise ValueError(&quot;name cannot be empty&quot;)&#10;        &#10;        if len(self.name) &gt; 255:&#10;            raise ValueError(&quot;name cannot exceed 255 characters&quot;)&#10;        &#10;        if not self.share_hash or len(self.share_hash) &lt; 16:&#10;            raise ValueError(&quot;share_hash must be at least 16 characters&quot;)&#10;        &#10;        # Ensure items is a list&#10;        if self.items is None:&#10;            self.items = []&#10;    &#10;    def add_item(self, product_id: str, notes: Optional[str] = None) -&gt; WishlistItem:&#10;        &quot;&quot;&quot;&#10;        Add or update an item in the wishlist.&#10;        &#10;        Args:&#10;            product_id: The ID of the product to add&#10;            notes: Optional notes about the item&#10;            &#10;        Returns:&#10;            The WishlistItem that was added or updated&#10;            &#10;        Raises:&#10;            ValueError: If product_id is invalid&#10;        &quot;&quot;&quot;&#10;        if not product_id or not product_id.strip():&#10;            raise ValueError(&quot;product_id cannot be empty&quot;)&#10;        &#10;        # Remove existing item if present&#10;        self.items = [item for item in self.items if item.product_id != product_id]&#10;        &#10;        # Create and add new item&#10;        new_item = WishlistItem(&#10;            product_id=product_id,&#10;            notes=notes,&#10;            added_at=datetime.utcnow()&#10;        )&#10;        self.items.append(new_item)&#10;        self.updated_at = datetime.utcnow()&#10;        &#10;        return new_item&#10;    &#10;    def remove_item(self, product_id: str) -&gt; bool:&#10;        &quot;&quot;&quot;&#10;        Remove an item from the wishlist.&#10;        &#10;        Args:&#10;            product_id: The ID of the product to remove&#10;            &#10;        Returns:&#10;            True if the item was removed, False if it wasn't found&#10;        &quot;&quot;&quot;&#10;        if not product_id:&#10;            return False&#10;        &#10;        original_count = len(self.items)&#10;        self.items = [item for item in self.items if item.product_id != product_id]&#10;        &#10;        if len(self.items) &lt; original_count:&#10;            self.updated_at = datetime.utcnow()&#10;            return True&#10;        return False&#10;    &#10;    def has_item(self, product_id: str) -&gt; bool:&#10;        &quot;&quot;&quot;Check if the wishlist contains a specific product.&quot;&quot;&quot;&#10;        return any(item.product_id == product_id for item in self.items)&#10;    &#10;    def get_item(self, product_id: str) -&gt; Optional[WishlistItem]:&#10;        &quot;&quot;&quot;Get a specific item from the wishlist.&quot;&quot;&quot;&#10;        for item in self.items:&#10;            if item.product_id == product_id:&#10;                return item&#10;        return None&#10;    &#10;    def update_item_notes(self, product_id: str, notes: Optional[str]) -&gt; bool:&#10;        &quot;&quot;&quot;&#10;        Update notes for a specific item.&#10;        &#10;        Args:&#10;            product_id: The ID of the product to update&#10;            notes: New notes for the item&#10;            &#10;        Returns:&#10;            True if the item was found and updated, False otherwise&#10;        &quot;&quot;&quot;&#10;        item = self.get_item(product_id)&#10;        if item:&#10;            item.update_notes(notes)&#10;            self.updated_at = datetime.utcnow()&#10;            return True&#10;        return False&#10;    &#10;    def clear_items(self) -&gt; int:&#10;        &quot;&quot;&quot;&#10;        Remove all items from the wishlist.&#10;        &#10;        Returns:&#10;            The number of items that were removed&#10;        &quot;&quot;&quot;&#10;        count = len(self.items)&#10;        self.items = []&#10;        if count &gt; 0:&#10;            self.updated_at = datetime.utcnow()&#10;        return count&#10;    &#10;    def get_item_count(self) -&gt; int:&#10;        &quot;&quot;&quot;Get the total number of items in the wishlist.&quot;&quot;&quot;&#10;        return len(self.items)&#10;    &#10;    def update_privacy(self, is_public: bool) -&gt; None:&#10;        &quot;&quot;&quot;Update the privacy status of the wishlist.&quot;&quot;&quot;&#10;        if self.is_public != is_public:&#10;            self.is_public = is_public&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def regenerate_share_hash(self) -&gt; str:&#10;        &quot;&quot;&quot;&#10;        Generate a new share hash for the wishlist.&#10;        &#10;        Returns:&#10;            The new share hash&#10;        &quot;&quot;&quot;&#10;        self.share_hash = self._generate_share_hash()&#10;        self.updated_at = datetime.utcnow()&#10;        return self.share_hash&#10;    &#10;    def update_name(self, name: str) -&gt; None:&#10;        &quot;&quot;&quot;&#10;        Update the wishlist name.&#10;        &#10;        Args:&#10;            name: New name for the wishlist&#10;            &#10;        Raises:&#10;            ValueError: If name is invalid&#10;        &quot;&quot;&quot;&#10;        if not name or not name.strip():&#10;            raise ValueError(&quot;name cannot be empty&quot;)&#10;        &#10;        if len(name) &gt; 255:&#10;            raise ValueError(&quot;name cannot exceed 255 characters&quot;)&#10;        &#10;        if self.name != name:&#10;            self.name = name.strip()&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def set_as_default(self) -&gt; None:&#10;        &quot;&quot;&quot;Mark this wishlist as the default for the user.&quot;&quot;&quot;&#10;        if not self.is_default:&#10;            self.is_default = True&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def unset_as_default(self) -&gt; None:&#10;        &quot;&quot;&quot;Remove the default status from this wishlist.&quot;&quot;&quot;&#10;        if self.is_default:&#10;            self.is_default = False&#10;            self.updated_at = datetime.utcnow()&#10;    &#10;    def get_product_ids(self) -&gt; List[str]:&#10;        &quot;&quot;&quot;Get a list of all product IDs in this wishlist.&quot;&quot;&quot;&#10;        return [item.product_id for item in self.items]&#10;    &#10;    def is_empty(self) -&gt; bool:&#10;        &quot;&quot;&quot;Check if the wishlist has no items.&quot;&quot;&quot;&#10;        return len(self.items) == 0&#10;    &#10;    def get_items_added_since(self, since: datetime) -&gt; List[WishlistItem]:&#10;        &quot;&quot;&quot;Get items that were added after a specific datetime.&quot;&quot;&quot;&#10;        return [&#10;            item for item in self.items &#10;            if item.added_at and item.added_at &gt; since&#10;        ]&#10;    &#10;    @staticmethod&#10;    def _generate_share_hash() -&gt; str:&#10;        &quot;&quot;&quot;Generate a secure random share hash.&quot;&quot;&quot;&#10;        alphabet = string.ascii_letters + string.digits&#10;        return ''.join(secrets.choice(alphabet) for _ in range(16))&#10;    &#10;    @classmethod&#10;    def create_new(&#10;        cls,&#10;        user_id: str,&#10;        name: str,&#10;        is_default: bool = False,&#10;        is_public: bool = False&#10;    ) -&gt; 'Wishlist':&#10;        &quot;&quot;&quot;&#10;        Factory method to create a new wishlist with default values.&#10;        &#10;        Args:&#10;            user_id: The ID of the user who owns the wishlist&#10;            name: The name of the wishlist&#10;            is_default: Whether this should be the default wishlist&#10;            is_public: Whether this wishlist should be public&#10;            &#10;        Returns:&#10;            A new Wishlist instance&#10;            &#10;        Raises:&#10;            ValueError: If any parameters are invalid&#10;        &quot;&quot;&quot;&#10;        now = datetime.utcnow()&#10;        &#10;        return cls(&#10;            user_id=user_id,&#10;            wishlist_id=str(uuid4()),&#10;            name=name,&#10;            is_default=is_default,&#10;            is_public=is_public,&#10;            share_hash=cls._generate_share_hash(),&#10;            items=[],&#10;            created_at=now,&#10;            updated_at=now&#10;        )" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/app/schemas/base.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/app/schemas/base.py" />
              <option name="originalContent" value="&quot;&quot;&quot;&#10;Base schemas and common validation utilities.&#10;&#10;This module provides base classes and common validators that are shared&#10;across different schema types to promote code reuse and consistency.&#10;&quot;&quot;&quot;&#10;&#10;from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict&#10;from typing import Optional, Dict, Any, List&#10;from datetime import datetime, timezone&#10;from enum import Enum&#10;import re&#10;&#10;&#10;class LanguageCode(str, Enum):&#10;    &quot;&quot;&quot;Supported language codes.&quot;&quot;&quot;&#10;    ENGLISH = &quot;en&quot;&#10;    ARABIC = &quot;ar&quot;&#10;&#10;&#10;class CountryCode(str, Enum):&#10;    &quot;&quot;&quot;Supported country codes.&quot;&quot;&quot;&#10;    UAE = &quot;ae&quot;&#10;    SAUDI_ARABIA = &quot;sa&quot;&#10;    KUWAIT = &quot;kw&quot;&#10;    BAHRAIN = &quot;bh&quot;&#10;    OMAN = &quot;om&quot;&#10;    QATAR = &quot;qa&quot;&#10;&#10;&#10;class BaseSchema(BaseModel):&#10;    &quot;&quot;&quot;&#10;    Base schema class with common configuration.&#10;&#10;    Provides consistent configuration for all schemas including&#10;    JSON encoding settings and validation behavior.&#10;    &quot;&quot;&quot;&#10;&#10;    model_config = ConfigDict(&#10;        # Use enum values instead of names in JSON&#10;        use_enum_values=True,&#10;        # Validate assignment to fields&#10;        validate_assignment=True,&#10;        # Allow population by field name or alias&#10;        populate_by_name=True,&#10;        # Generate schema with examples&#10;        json_schema_extra={&#10;            &quot;examples&quot;: []&#10;        }&#10;    )&#10;&#10;&#10;class BaseRequestSchema(BaseSchema):&#10;    &quot;&quot;&quot;&#10;    Base class for all request schemas.&#10;&#10;    Provides common validation and configuration for API requests.&#10;    &quot;&quot;&quot;&#10;&#10;    # Common query parameters&#10;    country: Optional[CountryCode] = Field(&#10;        default=CountryCode.UAE,&#10;        description=&quot;Country code for localization&quot;&#10;    )&#10;    language: Optional[LanguageCode] = Field(&#10;        default=LanguageCode.ENGLISH,&#10;        description=&quot;Language code for localization&quot;&#10;    )&#10;&#10;    @field_validator('country', mode='before')&#10;    @classmethod&#10;    def validate_country(cls, v):&#10;        &quot;&quot;&quot;Validate and normalize country code.&quot;&quot;&quot;&#10;        if v is None:&#10;            return CountryCode.UAE&#10;&#10;        if isinstance(v, str):&#10;            v = v.lower().strip()&#10;            # Map common variations&#10;            country_mapping = {&#10;                'uae': 'ae',&#10;                'united_arab_emirates': 'ae',&#10;                'saudi': 'sa',&#10;                'ksa': 'sa',&#10;                'saudi_arabia': 'sa',&#10;            }&#10;            v = country_mapping.get(v, v)&#10;&#10;        try:&#10;            return CountryCode(v)&#10;        except ValueError:&#10;            # Default to UAE for invalid codes&#10;            return CountryCode.UAE&#10;&#10;    @field_validator('language', mode='before')&#10;    @classmethod&#10;    def validate_language(cls, v):&#10;        &quot;&quot;&quot;Validate and normalize language code.&quot;&quot;&quot;&#10;        if v is None:&#10;            return LanguageCode.ENGLISH&#10;&#10;        if isinstance(v, str):&#10;            v = v.lower().strip()&#10;            # Map common variations&#10;            language_mapping = {&#10;                'eng': 'en',&#10;                'english': 'en',&#10;                'ara': 'ar',&#10;                'arabic': 'ar',&#10;            }&#10;            v = language_mapping.get(v, v)&#10;&#10;        try:&#10;            return LanguageCode(v)&#10;        except ValueError:&#10;            # Default to English for invalid codes&#10;            return LanguageCode.ENGLISH&#10;&#10;&#10;class BaseResponseSchema(BaseSchema):&#10;    &quot;&quot;&quot;&#10;    Base class for all response schemas.&#10;&#10;    Provides common fields and configuration for API responses.&#10;    &quot;&quot;&quot;&#10;    pass&#10;&#10;&#10;class PaginationSchema(BaseSchema):&#10;    &quot;&quot;&quot;Schema for pagination parameters.&quot;&quot;&quot;&#10;&#10;    page: int = Field(&#10;        default=1,&#10;        ge=1,&#10;        description=&quot;Page number (1-based)&quot;&#10;    )&#10;    page_size: int = Field(&#10;        default=20,&#10;        ge=1,&#10;        le=100,&#10;        description=&quot;Number of items per page (max 100)&quot;&#10;    )&#10;&#10;    @property&#10;    def offset(self) -&gt; int:&#10;        &quot;&quot;&quot;Calculate offset for database queries.&quot;&quot;&quot;&#10;        return (self.page - 1) * self.page_size&#10;&#10;&#10;class PaginatedResponseSchema(BaseResponseSchema):&#10;    &quot;&quot;&quot;Base schema for paginated responses.&quot;&quot;&quot;&#10;&#10;    total: int = Field(&#10;        description=&quot;Total number of items&quot;&#10;    )&#10;    page: int = Field(&#10;        description=&quot;Current page number&quot;&#10;    )&#10;    page_size: int = Field(&#10;        description=&quot;Number of items per page&quot;&#10;    )&#10;    total_pages: int = Field(&#10;        description=&quot;Total number of pages&quot;&#10;    )&#10;    has_next: bool = Field(&#10;        description=&quot;Whether there are more pages&quot;&#10;    )&#10;    has_previous: bool = Field(&#10;        description=&quot;Whether there are previous pages&quot;&#10;    )&#10;&#10;&#10;class ErrorDetailSchema(BaseSchema):&#10;    &quot;&quot;&quot;Schema for detailed error information.&quot;&quot;&quot;&#10;&#10;    code: str = Field(&#10;        description=&quot;Error code for programmatic handling&quot;&#10;    )&#10;    message: str = Field(&#10;        description=&quot;Human-readable error message&quot;&#10;    )&#10;    field: Optional[str] = Field(&#10;        default=None,&#10;        description=&quot;Field name if this is a validation error&quot;&#10;    )&#10;    details: Optional[Dict[str, Any]] = Field(&#10;        default=None,&#10;        description=&quot;Additional error details&quot;&#10;    )&#10;&#10;&#10;class ErrorResponseSchema(BaseResponseSchema):&#10;    &quot;&quot;&quot;Schema for error responses.&quot;&quot;&quot;&#10;&#10;    code: str = Field(&#10;        description=&quot;Error code for programmatic handling&quot;&#10;    )&#10;    message: str = Field(&#10;        description=&quot;Human-readable error message&quot;&#10;    )&#10;    correlation_id: Optional[str] = Field(&#10;        default=None,&#10;        description=&quot;Correlation ID for request tracing&quot;&#10;    )&#10;    details: Optional[Dict[str, Any]] = Field(&#10;        default=None,&#10;        description=&quot;Additional error details&quot;&#10;    )&#10;    field_errors: Optional[List[Dict[str, Any]]] = Field(&#10;        default=None,&#10;        description=&quot;Field-specific validation errors&quot;&#10;    )&#10;    timestamp: datetime = Field(&#10;        default_factory=datetime.now(timezone.utc).isoformat(),&#10;        description=&quot;Error timestamp&quot;&#10;    )&#10;&#10;&#10;class SuccessResponseSchema(BaseResponseSchema):&#10;    &quot;&quot;&quot;Schema for success responses with message.&quot;&quot;&quot;&#10;&#10;    message: str = Field(&#10;        description=&quot;Success message&quot;&#10;    )&#10;    correlation_id: Optional[str] = Field(&#10;        default=None,&#10;        description=&quot;Correlation ID for request tracing&quot;&#10;    )&#10;    timestamp: datetime = Field(&#10;        default_factory=datetime.now(timezone.utc),&#10;        description=&quot;Response timestamp&quot;&#10;    )&#10;&#10;&#10;# Custom validators&#10;def validate_user_id(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate user ID format.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;user_id cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    # Basic format validation (adjust based on your user ID format)&#10;    if len(v) &lt; 3:&#10;        raise ValueError(&quot;user_id must be at least 3 characters long&quot;)&#10;&#10;    if len(v) &gt; 50:&#10;        raise ValueError(&quot;user_id cannot exceed 50 characters&quot;)&#10;&#10;    # Allow alphanumeric, hyphens, and underscores&#10;    if not re.match(r'^[a-zA-Z0-9_-]+$', v):&#10;        raise ValueError(&quot;user_id contains invalid characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_product_id(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate product ID format.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;product_id cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    # Basic format validation&#10;    if len(v) &lt; 1:&#10;        raise ValueError(&quot;product_id cannot be empty&quot;)&#10;&#10;    if len(v) &gt; 100:&#10;        raise ValueError(&quot;product_id cannot exceed 100 characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_wishlist_name(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate wishlist name.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;name cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    if len(v) &gt; 255:&#10;        raise ValueError(&quot;name cannot exceed 255 characters&quot;)&#10;&#10;    # Check for potentially harmful content&#10;    if any(char in v for char in ['&lt;', '&gt;', '&amp;', '&quot;', &quot;'&quot;]):&#10;        raise ValueError(&quot;name contains invalid characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_notes(v: Optional[str]) -&gt; Optional[str]:&#10;    &quot;&quot;&quot;Validate item notes.&quot;&quot;&quot;&#10;    if v is None:&#10;        return None&#10;&#10;    v = v.strip()&#10;&#10;    if not v:&#10;        return None&#10;&#10;    if len(v) &gt; 500:&#10;        raise ValueError(&quot;notes cannot exceed 500 characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_share_hash(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate share hash format.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;share_hash cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    if len(v) &lt; 16:&#10;        raise ValueError(&quot;share_hash must be at least 16 characters long&quot;)&#10;&#10;    if len(v) &gt; 64:&#10;        raise ValueError(&quot;share_hash cannot exceed 64 characters&quot;)&#10;&#10;    # Should be alphanumeric&#10;    if not re.match(r'^[a-zA-Z0-9]+$', v):&#10;        raise ValueError(&quot;share_hash contains invalid characters&quot;)&#10;&#10;    return v&#10;" />
              <option name="updatedContent" value="&quot;&quot;&quot;&#10;Base schemas and common validation utilities.&#10;&#10;This module provides base classes and common validators that are shared&#10;across different schema types to promote code reuse and consistency.&#10;&quot;&quot;&quot;&#10;&#10;from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict&#10;from typing import Optional, Dict, Any, List&#10;from datetime import datetime, timezone&#10;from enum import Enum&#10;import re&#10;&#10;&#10;class LanguageCode(str, Enum):&#10;    &quot;&quot;&quot;Supported language codes.&quot;&quot;&quot;&#10;    ENGLISH = &quot;en&quot;&#10;    ARABIC = &quot;ar&quot;&#10;&#10;&#10;class CountryCode(str, Enum):&#10;    &quot;&quot;&quot;Supported country codes.&quot;&quot;&quot;&#10;    UAE = &quot;ae&quot;&#10;    SAUDI_ARABIA = &quot;sa&quot;&#10;    KUWAIT = &quot;kw&quot;&#10;    BAHRAIN = &quot;bh&quot;&#10;    OMAN = &quot;om&quot;&#10;    QATAR = &quot;qa&quot;&#10;&#10;&#10;class BaseSchema(BaseModel):&#10;    &quot;&quot;&quot;&#10;    Base schema class with common configuration.&#10;&#10;    Provides consistent configuration for all schemas including&#10;    JSON encoding settings and validation behavior.&#10;    &quot;&quot;&quot;&#10;&#10;    model_config = ConfigDict(&#10;        # Use enum values instead of names in JSON&#10;        use_enum_values=True,&#10;        # Validate assignment to fields&#10;        validate_assignment=True,&#10;        # Allow population by field name or alias&#10;        populate_by_name=True,&#10;        # Generate schema with examples&#10;        json_schema_extra={&#10;            &quot;examples&quot;: []&#10;        }&#10;    )&#10;&#10;&#10;class BaseRequestSchema(BaseSchema):&#10;    &quot;&quot;&quot;&#10;    Base class for all request schemas.&#10;&#10;    Provides common validation and configuration for API requests.&#10;    &quot;&quot;&quot;&#10;&#10;    # Common query parameters&#10;    country: Optional[CountryCode] = Field(&#10;        default=CountryCode.UAE,&#10;        description=&quot;Country code for localization&quot;&#10;    )&#10;    language: Optional[LanguageCode] = Field(&#10;        default=LanguageCode.ENGLISH,&#10;        description=&quot;Language code for localization&quot;&#10;    )&#10;&#10;    @field_validator('country', mode='before')&#10;    @classmethod&#10;    def validate_country(cls, v):&#10;        &quot;&quot;&quot;Validate and normalize country code.&quot;&quot;&quot;&#10;        if v is None:&#10;            return CountryCode.UAE&#10;&#10;        if isinstance(v, str):&#10;            v = v.lower().strip()&#10;            # Map common variations&#10;            country_mapping = {&#10;                'uae': 'ae',&#10;                'united_arab_emirates': 'ae',&#10;                'saudi': 'sa',&#10;                'ksa': 'sa',&#10;                'saudi_arabia': 'sa',&#10;            }&#10;            v = country_mapping.get(v, v)&#10;&#10;        try:&#10;            return CountryCode(v)&#10;        except ValueError:&#10;            # Default to UAE for invalid codes&#10;            return CountryCode.UAE&#10;&#10;    @field_validator('language', mode='before')&#10;    @classmethod&#10;    def validate_language(cls, v):&#10;        &quot;&quot;&quot;Validate and normalize language code.&quot;&quot;&quot;&#10;        if v is None:&#10;            return LanguageCode.ENGLISH&#10;&#10;        if isinstance(v, str):&#10;            v = v.lower().strip()&#10;            # Map common variations&#10;            language_mapping = {&#10;                'eng': 'en',&#10;                'english': 'en',&#10;                'ara': 'ar',&#10;                'arabic': 'ar',&#10;            }&#10;            v = language_mapping.get(v, v)&#10;&#10;        try:&#10;            return LanguageCode(v)&#10;        except ValueError:&#10;            # Default to English for invalid codes&#10;            return LanguageCode.ENGLISH&#10;&#10;&#10;class BaseResponseSchema(BaseSchema):&#10;    &quot;&quot;&quot;&#10;    Base class for all response schemas.&#10;&#10;    Provides common fields and configuration for API responses.&#10;    &quot;&quot;&quot;&#10;    pass&#10;&#10;&#10;class PaginationSchema(BaseSchema):&#10;    &quot;&quot;&quot;Schema for pagination parameters.&quot;&quot;&quot;&#10;&#10;    page: int = Field(&#10;        default=1,&#10;        ge=1,&#10;        description=&quot;Page number (1-based)&quot;&#10;    )&#10;    page_size: int = Field(&#10;        default=20,&#10;        ge=1,&#10;        le=100,&#10;        description=&quot;Number of items per page (max 100)&quot;&#10;    )&#10;&#10;    @property&#10;    def offset(self) -&gt; int:&#10;        &quot;&quot;&quot;Calculate offset for database queries.&quot;&quot;&quot;&#10;        return (self.page - 1) * self.page_size&#10;&#10;&#10;class PaginatedResponseSchema(BaseResponseSchema):&#10;    &quot;&quot;&quot;Base schema for paginated responses.&quot;&quot;&quot;&#10;&#10;    total: int = Field(&#10;        description=&quot;Total number of items&quot;&#10;    )&#10;    page: int = Field(&#10;        description=&quot;Current page number&quot;&#10;    )&#10;    page_size: int = Field(&#10;        description=&quot;Number of items per page&quot;&#10;    )&#10;    total_pages: int = Field(&#10;        description=&quot;Total number of pages&quot;&#10;    )&#10;    has_next: bool = Field(&#10;        description=&quot;Whether there are more pages&quot;&#10;    )&#10;    has_previous: bool = Field(&#10;        description=&quot;Whether there are previous pages&quot;&#10;    )&#10;&#10;&#10;class ErrorDetailSchema(BaseSchema):&#10;    &quot;&quot;&quot;Schema for detailed error information.&quot;&quot;&quot;&#10;&#10;    code: str = Field(&#10;        description=&quot;Error code for programmatic handling&quot;&#10;    )&#10;    message: str = Field(&#10;        description=&quot;Human-readable error message&quot;&#10;    )&#10;    field: Optional[str] = Field(&#10;        default=None,&#10;        description=&quot;Field name if this is a validation error&quot;&#10;    )&#10;    details: Optional[Dict[str, Any]] = Field(&#10;        default=None,&#10;        description=&quot;Additional error details&quot;&#10;    )&#10;&#10;&#10;class ErrorResponseSchema(BaseResponseSchema):&#10;    &quot;&quot;&quot;Schema for error responses.&quot;&quot;&quot;&#10;&#10;    code: str = Field(&#10;        description=&quot;Error code for programmatic handling&quot;&#10;    )&#10;    message: str = Field(&#10;        description=&quot;Human-readable error message&quot;&#10;    )&#10;    correlation_id: Optional[str] = Field(&#10;        default=None,&#10;        description=&quot;Correlation ID for request tracing&quot;&#10;    )&#10;    details: Optional[Dict[str, Any]] = Field(&#10;        default=None,&#10;        description=&quot;Additional error details&quot;&#10;    )&#10;    field_errors: Optional[List[Dict[str, Any]]] = Field(&#10;        default=None,&#10;        description=&quot;Field-specific validation errors&quot;&#10;    )&#10;    timestamp: str = Field(&#10;        default_factory=lambda: datetime.now(timezone.utc).isoformat(),&#10;        description=&quot;Error timestamp&quot;&#10;    )&#10;&#10;&#10;class SuccessResponseSchema(BaseResponseSchema):&#10;    &quot;&quot;&quot;Schema for success responses with message.&quot;&quot;&quot;&#10;&#10;    message: str = Field(&#10;        description=&quot;Success message&quot;&#10;    )&#10;    correlation_id: Optional[str] = Field(&#10;        default=None,&#10;        description=&quot;Correlation ID for request tracing&quot;&#10;    )&#10;    timestamp: datetime = Field(&#10;        default_factory=datetime.now(timezone.utc),&#10;        description=&quot;Response timestamp&quot;&#10;    )&#10;&#10;&#10;# Custom validators&#10;def validate_user_id(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate user ID format.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;user_id cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    # Basic format validation (adjust based on your user ID format)&#10;    if len(v) &lt; 3:&#10;        raise ValueError(&quot;user_id must be at least 3 characters long&quot;)&#10;&#10;    if len(v) &gt; 50:&#10;        raise ValueError(&quot;user_id cannot exceed 50 characters&quot;)&#10;&#10;    # Allow alphanumeric, hyphens, and underscores&#10;    if not re.match(r'^[a-zA-Z0-9_-]+$', v):&#10;        raise ValueError(&quot;user_id contains invalid characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_product_id(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate product ID format.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;product_id cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    # Basic format validation&#10;    if len(v) &lt; 1:&#10;        raise ValueError(&quot;product_id cannot be empty&quot;)&#10;&#10;    if len(v) &gt; 100:&#10;        raise ValueError(&quot;product_id cannot exceed 100 characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_wishlist_name(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate wishlist name.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;name cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    if len(v) &gt; 255:&#10;        raise ValueError(&quot;name cannot exceed 255 characters&quot;)&#10;&#10;    # Check for potentially harmful content&#10;    if any(char in v for char in ['&lt;', '&gt;', '&amp;', '&quot;', &quot;'&quot;]):&#10;        raise ValueError(&quot;name contains invalid characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_notes(v: Optional[str]) -&gt; Optional[str]:&#10;    &quot;&quot;&quot;Validate item notes.&quot;&quot;&quot;&#10;    if v is None:&#10;        return None&#10;&#10;    v = v.strip()&#10;&#10;    if not v:&#10;        return None&#10;&#10;    if len(v) &gt; 500:&#10;        raise ValueError(&quot;notes cannot exceed 500 characters&quot;)&#10;&#10;    return v&#10;&#10;&#10;def validate_share_hash(v: str) -&gt; str:&#10;    &quot;&quot;&quot;Validate share hash format.&quot;&quot;&quot;&#10;    if not v or not v.strip():&#10;        raise ValueError(&quot;share_hash cannot be empty&quot;)&#10;&#10;    v = v.strip()&#10;&#10;    if len(v) &lt; 16:&#10;        raise ValueError(&quot;share_hash must be at least 16 characters long&quot;)&#10;&#10;    if len(v) &gt; 64:&#10;        raise ValueError(&quot;share_hash cannot exceed 64 characters&quot;)&#10;&#10;    # Should be alphanumeric&#10;    if not re.match(r'^[a-zA-Z0-9]+$', v):&#10;        raise ValueError(&quot;share_hash contains invalid characters&quot;)&#10;&#10;    return v" />
            </PendingDiffInfo>
          </value>
        </entry>
        <entry key="$PROJECT_DIR$/app/services/wishlist_service.py">
          <value>
            <PendingDiffInfo>
              <option name="filePath" value="$PROJECT_DIR$/app/services/wishlist_service.py" />
              <option name="originalContent" value="&quot;&quot;&quot;&#10;Core wishlist business logic service.&#10;&#10;This service implements all wishlist operations including CRUD operations,&#10;product enrichment, caching, and background job dispatching.&#10;&quot;&quot;&quot;&#10;import asyncio&#10;import logging&#10;from datetime import datetime&#10;from typing import List, Optional, Dict, Any&#10;from uuid import uuid4&#10;&#10;from app.models.domain.wishlist import Wishlist, WishlistItem&#10;from app.repositories.wishlist_repo import WishlistRepository&#10;from app.repositories.cache_repo import CacheRepository&#10;from app.services.algolia_service import AlgoliaService&#10;from app.services.cloudfront_service import CloudFrontService&#10;from app.services.cache_management_service import CacheManagementService&#10;from app.schemas.requests.wishlist import (&#10;    CreateWishlistRequest,&#10;    UpdateWishlistRequest&#10;)&#10;from app.schemas.requests.item import (&#10;    AddItemRequest,&#10;    BulkAddItemsRequest,&#10;    BulkRemoveItemsRequest,&#10;    MoveItemsRequest,&#10;    UpdateItemRequest&#10;)&#10;from app.core.exceptions import (&#10;    WishlistNotFoundError,&#10;    ValidationError,&#10;    ExternalServiceError&#10;)&#10;from app.core.metrics import metrics&#10;from app.utils.helpers import generate_correlation_id&#10;&#10;logger = logging.getLogger(__name__)&#10;&#10;&#10;class WishlistService:&#10;    &quot;&quot;&quot;&#10;    Core business logic service for wishlist operations.&#10;&#10;    Handles all wishlist CRUD operations, product enrichment,&#10;    caching strategies, and background job dispatching.&#10;    &quot;&quot;&quot;&#10;&#10;    def __init__(&#10;        self,&#10;        wishlist_repo: WishlistRepository,&#10;        cache_repo: CacheRepository,&#10;        algolia_service: AlgoliaService,&#10;        cloudfront_service: CloudFrontService,&#10;        cache_management_service: Optional[CacheManagementService] = None,&#10;    ):&#10;        self.wishlist_repo = wishlist_repo&#10;        self.cache_repo = cache_repo&#10;        self.algolia_service = algolia_service&#10;        self.cloudfront_service = cloudfront_service&#10;        self.cache_management_service = cache_management_service&#10;&#10;    async def create_wishlist(&#10;        self,&#10;        user_id: str,&#10;        request: CreateWishlistRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Create a new wishlist for a user.&#10;&#10;        Args:&#10;            user_id: The ID of the user creating the wishlist&#10;            request: The wishlist creation request data&#10;&#10;        Returns:&#10;            The created wishlist&#10;&#10;        Raises:&#10;            ValidationError: If the request data is invalid&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Creating wishlist for user {user_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_name&quot;: request.name&#10;        })&#10;&#10;        with metrics.timer(&quot;wishlist_create_duration&quot;):&#10;            try:&#10;                # Handle default wishlist logic&#10;                if request.is_default:&#10;                    await self._unset_default_wishlist(user_id)&#10;&#10;                # Create new wishlist using domain model factory&#10;                wishlist = Wishlist.create_new(&#10;                    user_id=user_id,&#10;                    name=request.name,&#10;                    is_default=request.is_default,&#10;                    is_public=False  # New wishlists are private by default&#10;                )&#10;&#10;                # Save to database&#10;                created_wishlist = await self.wishlist_repo.create(wishlist)&#10;&#10;                # Invalidate user's wishlist cache&#10;                await self._invalidate_user_cache(user_id)&#10;&#10;                # Dispatch background notification (fire and forget)&#10;                asyncio.create_task(&#10;                    self._dispatch_wishlist_created_notification(created_wishlist)&#10;                )&#10;&#10;                metrics.counter(&quot;wishlist_created&quot;).inc()&#10;                logger.info(f&quot;Successfully created wishlist {created_wishlist.wishlist_id}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;wishlist_id&quot;: created_wishlist.wishlist_id&#10;                })&#10;&#10;                return created_wishlist&#10;&#10;            except Exception as e:&#10;                metrics.counter(&quot;wishlist_create_error&quot;).inc()&#10;                logger.error(f&quot;Failed to create wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def get_user_wishlists(&#10;        self,&#10;        user_id: str,&#10;        country: str = &quot;ae&quot;,&#10;        language: str = &quot;en&quot;,&#10;        include_products: bool = True&#10;    ) -&gt; List[Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Get all wishlists for a user with optional product enrichment.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            country: Country code for product localization&#10;            language: Language code for product localization&#10;            include_products: Whether to enrich items with product data&#10;&#10;        Returns:&#10;            List of user's wishlists&#10;        &quot;&quot;&quot;&#10;        import pdb; pdb.set_trace()&#10;        correlation_id = generate_correlation_id()&#10;        cache_key = f&quot;user_wishlists:{user_id}:{country}:{language}:{include_products}&quot;&#10;&#10;        logger.info(f&quot;Getting wishlists for user {user_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;country&quot;: country,&#10;            &quot;language&quot;: language&#10;        })&#10;&#10;        # Try cache first&#10;        try:&#10;            cached_result = await self.cache_repo.get(cache_key)&#10;            if cached_result:&#10;                metrics.counter(&quot;cache_hit&quot;, {&quot;type&quot;: &quot;user_wishlists&quot;}).inc()&#10;                logger.debug(f&quot;Cache hit for user wishlists&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;cache_key&quot;: cache_key&#10;                })&#10;                return cached_result&#10;        except Exception as e:&#10;            logger.warning(f&quot;Cache lookup failed: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id&#10;            })&#10;&#10;&#10;        with metrics.timer(&quot;get_user_wishlists_duration&quot;):&#10;            try:&#10;                # Get from database&#10;                wishlists = await self.wishlist_repo.get_by_user_id(user_id)&#10;&#10;                # Enrich with product details if requested&#10;                if include_products and wishlists:&#10;                    enriched_wishlists = await self._enrich_wishlists_with_products(&#10;                        wishlists, country, language&#10;                    )&#10;                else:&#10;                    enriched_wishlists = wishlists&#10;&#10;                # Cache the result&#10;                try:&#10;                    await self.cache_repo.set(&#10;                        cache_key,&#10;                        enriched_wishlists,&#10;                        ttl=300  # 5 minutes&#10;                    )&#10;                except Exception as e:&#10;                    logger.warning(f&quot;Failed to cache result: {e}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                metrics.counter(&quot;cache_miss&quot;, {&quot;type&quot;: &quot;user_wishlists&quot;}).inc()&#10;                logger.info(f&quot;Retrieved {len(enriched_wishlists)} wishlists for user&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;wishlist_count&quot;: len(enriched_wishlists)&#10;                })&#10;&#10;                return enriched_wishlists&#10;&#10;            except Exception as e:&#10;                metrics.counter(&quot;get_user_wishlists_error&quot;).inc()&#10;                logger.error(f&quot;Failed to get user wishlists: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def get_wishlist_by_id(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        country: str = &quot;ae&quot;,&#10;        language: str = &quot;en&quot;,&#10;        include_products: bool = True&#10;    ) -&gt; Optional[Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Get a specific wishlist by ID.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            country: Country code for product localization&#10;            language: Language code for product localization&#10;            include_products: Whether to enrich items with product data&#10;&#10;        Returns:&#10;            The wishlist if found, None otherwise&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        cache_key = f&quot;wishlist:{user_id}:{wishlist_id}:{country}:{language}:{include_products}&quot;&#10;&#10;        logger.info(f&quot;Getting wishlist {wishlist_id} for user {user_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id&#10;        })&#10;&#10;        # Try cache first&#10;        try:&#10;            cached_result = await self.cache_repo.get(cache_key)&#10;            if cached_result:&#10;                metrics.counter(&quot;cache_hit&quot;, {&quot;type&quot;: &quot;wishlist&quot;}).inc()&#10;                return cached_result&#10;        except Exception as e:&#10;            logger.warning(f&quot;Cache lookup failed: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id&#10;            })&#10;&#10;        try:&#10;            # Get from database&#10;            wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;            if not wishlist:&#10;                logger.info(f&quot;Wishlist {wishlist_id} not found&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;                return None&#10;&#10;            # Enrich with product details if requested&#10;            if include_products:&#10;                enriched_wishlist = await self._enrich_wishlist_with_products(&#10;                    wishlist, country, language&#10;                )&#10;            else:&#10;                enriched_wishlist = wishlist&#10;&#10;            # Cache the result&#10;            try:&#10;                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=300)&#10;            except Exception as e:&#10;                logger.warning(f&quot;Failed to cache result: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;            metrics.counter(&quot;cache_miss&quot;, {&quot;type&quot;: &quot;wishlist&quot;}).inc()&#10;            return enriched_wishlist&#10;&#10;        except Exception as e:&#10;            metrics.counter(&quot;get_wishlist_error&quot;).inc()&#10;            logger.error(f&quot;Failed to get wishlist: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;user_id&quot;: user_id,&#10;                &quot;wishlist_id&quot;: wishlist_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            raise&#10;&#10;    async def update_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: UpdateWishlistRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Update a wishlist's properties.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist to update&#10;            request: The update request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Updating wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id&#10;        })&#10;&#10;        with metrics.timer(&quot;wishlist_update_duration&quot;):&#10;            try:&#10;                # Get existing wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Handle default wishlist logic&#10;                if request.is_default is not None and request.is_default and not wishlist.is_default:&#10;                    await self._unset_default_wishlist(user_id)&#10;&#10;                # Update fields&#10;                if request.name is not None:&#10;                    wishlist.name = request.name&#10;                if request.is_default is not None:&#10;                    wishlist.is_default = request.is_default&#10;&#10;                wishlist.updated_at = datetime.utcnow()&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                metrics.counter(&quot;wishlist_updated&quot;).inc()&#10;                logger.info(f&quot;Successfully updated wishlist {wishlist_id}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;wishlist_update_error&quot;).inc()&#10;                logger.error(f&quot;Failed to update wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def delete_wishlist(self, user_id: str, wishlist_id: str) -&gt; bool:&#10;        &quot;&quot;&quot;&#10;        Delete a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist to delete&#10;&#10;        Returns:&#10;            True if deleted successfully&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Deleting wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id&#10;        })&#10;&#10;        with metrics.timer(&quot;wishlist_delete_duration&quot;):&#10;            try:&#10;                # Verify wishlist exists&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Delete from database&#10;                success = await self.wishlist_repo.delete(user_id, wishlist_id)&#10;&#10;                if success:&#10;                    # Invalidate cache&#10;                    await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                    # Dispatch background notification&#10;                    asyncio.create_task(&#10;                        self._dispatch_wishlist_deleted_notification(wishlist)&#10;                    )&#10;&#10;                    metrics.counter(&quot;wishlist_deleted&quot;).inc()&#10;                    logger.info(f&quot;Successfully deleted wishlist {wishlist_id}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                return success&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;wishlist_delete_error&quot;).inc()&#10;                logger.error(f&quot;Failed to delete wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def add_item_to_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: AddItemRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Add an item to a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The add item request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Adding item {request.product_id} to wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_id&quot;: request.product_id&#10;        })&#10;&#10;        with metrics.timer(&quot;add_item_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Verify product exists in Algolia (optional validation)&#10;                try:&#10;                    product_exists = await self.algolia_service.product_exists(request.product_id)&#10;                    if not product_exists:&#10;                        logger.warning(f&quot;Product {request.product_id} not found in Algolia&quot;, extra={&#10;                            &quot;correlation_id&quot;: correlation_id,&#10;                            &quot;product_id&quot;: request.product_id&#10;                        })&#10;                        # Continue anyway - product might be temporarily unavailable&#10;                except Exception as e:&#10;                    logger.warning(f&quot;Failed to verify product existence: {e}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                # Add item using domain model method&#10;                added_item = wishlist.add_item(request.product_id, request.notes)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_item_added_notification(updated_wishlist, added_item)&#10;                )&#10;&#10;                metrics.counter(&quot;item_added&quot;).inc()&#10;                logger.info(f&quot;Successfully added item to wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;item_count&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;add_item_error&quot;).inc()&#10;                logger.error(f&quot;Failed to add item to wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;product_id&quot;: request.product_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def remove_item_from_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        product_id: str&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Remove an item from a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            product_id: The ID of the product to remove&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If the item doesn't exist in the wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Removing item {product_id} from wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_id&quot;: product_id&#10;        })&#10;&#10;        with metrics.timer(&quot;remove_item_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Remove item using domain model method&#10;                removed = wishlist.remove_item(product_id)&#10;                if not removed:&#10;                    raise ValidationError(f&quot;Item {product_id} not found in wishlist&quot;)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_item_removed_notification(updated_wishlist, product_id)&#10;                )&#10;&#10;                metrics.counter(&quot;item_removed&quot;).inc()&#10;                logger.info(f&quot;Successfully removed item from wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;item_count&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;remove_item_error&quot;).inc()&#10;                logger.error(f&quot;Failed to remove item from wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;product_id&quot;: product_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def get_shared_wishlist(&#10;        self,&#10;        share_hash: str,&#10;        country: str = &quot;ae&quot;,&#10;        language: str = &quot;en&quot;&#10;    ) -&gt; Optional[Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Get a shared wishlist by its share hash.&#10;&#10;        Args:&#10;            share_hash: The share hash of the wishlist&#10;            country: Country code for product localization&#10;            language: Language code for product localization&#10;&#10;        Returns:&#10;            The shared wishlist if found and public, None otherwise&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        cache_key = f&quot;shared_wishlist:{share_hash}:{country}:{language}&quot;&#10;&#10;        logger.info(f&quot;Getting shared wishlist {share_hash}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;share_hash&quot;: share_hash&#10;        })&#10;&#10;        # Try cache first&#10;        try:&#10;            cached_result = await self.cache_repo.get(cache_key)&#10;            if cached_result:&#10;                metrics.counter(&quot;cache_hit&quot;, {&quot;type&quot;: &quot;shared_wishlist&quot;}).inc()&#10;                return cached_result&#10;        except Exception as e:&#10;            logger.warning(f&quot;Cache lookup failed: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id&#10;            })&#10;&#10;        try:&#10;            # Get from database using share hash&#10;            wishlist = await self.wishlist_repo.get_by_share_hash(share_hash)&#10;            if not wishlist or not wishlist.is_public:&#10;                logger.info(f&quot;Shared wishlist not found or not public&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;share_hash&quot;: share_hash&#10;                })&#10;                return None&#10;&#10;            # Enrich with product details&#10;            enriched_wishlist = await self._enrich_wishlist_with_products(&#10;                wishlist, country, language&#10;            )&#10;&#10;            # Cache the result (longer TTL for shared wishlists)&#10;            try:&#10;                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=1800)  # 30 minutes&#10;            except Exception as e:&#10;                logger.warning(f&quot;Failed to cache result: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;            metrics.counter(&quot;cache_miss&quot;, {&quot;type&quot;: &quot;shared_wishlist&quot;}).inc()&#10;            return enriched_wishlist&#10;&#10;        except Exception as e:&#10;            metrics.counter(&quot;get_shared_wishlist_error&quot;).inc()&#10;            logger.error(f&quot;Failed to get shared wishlist: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;share_hash&quot;: share_hash,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            raise&#10;&#10;    # Helper methods&#10;&#10;    async def _enrich_wishlists_with_products(&#10;        self,&#10;        wishlists: List[Wishlist],&#10;        country: str,&#10;        language: str&#10;    ) -&gt; List[Wishlist]:&#10;        &quot;&quot;&quot;Enrich multiple wishlists with product data concurrently.&quot;&quot;&quot;&#10;        if not wishlists:&#10;            return wishlists&#10;&#10;        tasks = [&#10;            self._enrich_wishlist_with_products(wishlist, country, language)&#10;            for wishlist in wishlists&#10;        ]&#10;        return await asyncio.gather(*tasks, return_exceptions=False)&#10;&#10;    async def _enrich_wishlist_with_products(&#10;        self,&#10;        wishlist: Wishlist,&#10;        country: str,&#10;        language: str&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Enrich a single wishlist with product data using advanced caching and fallback strategies.&#10;&#10;        Features:&#10;        - Concurrent product fetching&#10;        - Cache warming for frequently accessed products&#10;        - Fallback to cached data on service failure&#10;        - Graceful degradation with partial results&#10;        &quot;&quot;&quot;&#10;        if not wishlist.items:&#10;            return wishlist&#10;&#10;        correlation_id = generate_correlation_id()&#10;        product_ids = [item.product_id for item in wishlist.items]&#10;&#10;        logger.debug(f&quot;Enriching wishlist {wishlist.wishlist_id} with {len(product_ids)} products&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;            &quot;product_count&quot;: len(product_ids)&#10;        })&#10;&#10;        try:&#10;            with metrics.timer(&quot;product_enrichment_duration&quot;):&#10;                # Get product data from Algolia with batching&#10;                products = await self.algolia_service.get_products_by_ids(&#10;                    product_ids, country, language&#10;                )&#10;&#10;                # Enrich items with product data&#10;                enriched_count = 0&#10;                missing_products = []&#10;&#10;                for item in wishlist.items:&#10;                    if item.product_id in products:&#10;                        item.product = products[item.product_id]&#10;                        enriched_count += 1&#10;                    else:&#10;                        missing_products.append(item.product_id)&#10;                        # Try to get cached product data as fallback&#10;                        if self.cache_repo:&#10;                            fallback_product = await self._get_fallback_product_data(&#10;                                item.product_id, country, language&#10;                            )&#10;                            if fallback_product:&#10;                                item.product = fallback_product&#10;                                enriched_count += 1&#10;                                logger.info(f&quot;Used fallback data for product {item.product_id}&quot;)&#10;&#10;                # Log enrichment results&#10;                if missing_products:&#10;                    logger.warning(f&quot;Products not found during enrichment: {missing_products}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id,&#10;                        &quot;missing_count&quot;: len(missing_products)&#10;                    })&#10;&#10;                metrics.counter(&quot;products_enriched&quot;).inc(enriched_count)&#10;                metrics.counter(&quot;products_missing&quot;).inc(len(missing_products))&#10;&#10;                logger.debug(f&quot;Enriched {enriched_count}/{len(product_ids)} products&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;enriched_count&quot;: enriched_count,&#10;                    &quot;total_count&quot;: len(product_ids)&#10;                })&#10;&#10;                # Trigger cache warming for frequently accessed products (fire and forget)&#10;                if enriched_count &gt; 0:&#10;                    asyncio.create_task(&#10;                        self._warm_product_cache(product_ids, country, language)&#10;                    )&#10;&#10;                return wishlist&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to enrich wishlist with products: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            metrics.counter(&quot;product_enrichment_error&quot;).inc()&#10;&#10;            # Try to use cached data as complete fallback&#10;            try:&#10;                cached_products = await self._get_cached_products_batch(product_ids, country, language)&#10;                if cached_products:&#10;                    for item in wishlist.items:&#10;                        if item.product_id in cached_products:&#10;                            item.product = cached_products[item.product_id]&#10;&#10;                    logger.info(f&quot;Used cached fallback data for {len(cached_products)} products&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;                    metrics.counter(&quot;product_enrichment_fallback_success&quot;).inc()&#10;            except Exception as fallback_error:&#10;                logger.error(f&quot;Fallback enrichment also failed: {fallback_error}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;                metrics.counter(&quot;product_enrichment_fallback_error&quot;).inc()&#10;&#10;            # Return wishlist without product enrichment rather than failing&#10;            return wishlist&#10;&#10;    async def _unset_default_wishlist(self, user_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Unset any existing default wishlist for a user.&quot;&quot;&quot;&#10;        try:&#10;            current_default = await self.wishlist_repo.get_default_wishlist(user_id)&#10;            if current_default:&#10;                current_default.is_default = False&#10;                current_default.updated_at = datetime.utcnow()&#10;                await self.wishlist_repo.update(current_default)&#10;                logger.info(f&quot;Unset default wishlist {current_default.wishlist_id} for user {user_id}&quot;)&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to unset default wishlist: {e}&quot;)&#10;            # Don't raise - this is not critical for the main operation&#10;&#10;    async def _invalidate_user_cache(self, user_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Invalidate all cache entries for a user using intelligent cache management.&quot;&quot;&quot;&#10;        try:&#10;            if self.cache_management_service:&#10;                # Use intelligent cache invalidation&#10;                await self.cache_management_service.invalidate_cache_intelligently(&#10;                    trigger_event=&quot;user:cache:invalidate&quot;,&#10;                    context={&quot;user_id&quot;: user_id}&#10;                )&#10;            else:&#10;                # Fallback to direct pattern deletion&#10;                patterns = [&#10;                    f&quot;user_wishlists:{user_id}:*&quot;,&#10;                    f&quot;wishlist:{user_id}:*&quot;&#10;                ]&#10;                for pattern in patterns:&#10;                    await self.cache_repo.delete_pattern(pattern)&#10;&#10;            logger.debug(f&quot;Invalidated cache for user {user_id}&quot;)&#10;        except Exception as e:&#10;            logger.warning(f&quot;Failed to invalidate user cache: {e}&quot;)&#10;&#10;    async def _invalidate_wishlist_cache(self, user_id: str, wishlist_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Invalidate cache entries for a specific wishlist using intelligent cache management.&quot;&quot;&quot;&#10;        try:&#10;            if self.cache_management_service:&#10;                # Use intelligent cache invalidation&#10;                await self.cache_management_service.invalidate_cache_intelligently(&#10;                    trigger_event=&quot;wishlist:updated&quot;,&#10;                    context={&#10;                        &quot;user_id&quot;: user_id,&#10;                        &quot;wishlist_id&quot;: wishlist_id&#10;                    }&#10;                )&#10;            else:&#10;                # Fallback to direct pattern deletion&#10;                patterns = [&#10;                    f&quot;wishlist:{user_id}:{wishlist_id}:*&quot;,&#10;                    f&quot;user_wishlists:{user_id}:*&quot;&#10;                ]&#10;                for pattern in patterns:&#10;                    await self.cache_repo.delete_pattern(pattern)&#10;&#10;            logger.debug(f&quot;Invalidated cache for wishlist {wishlist_id}&quot;)&#10;        except Exception as e:&#10;            logger.warning(f&quot;Failed to invalidate wishlist cache: {e}&quot;)&#10;&#10;    # Background notification methods (fire and forget)&#10;&#10;    async def _dispatch_wishlist_created_notification(self, wishlist: Wishlist) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for wishlist creation.&quot;&quot;&quot;&#10;        try:&#10;            # This would typically queue a Celery task&#10;            # For now, we'll just log the event&#10;            logger.info(f&quot;Wishlist created notification&quot;, extra={&#10;                &quot;event&quot;: &quot;wishlist_created&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;wishlist_name&quot;: wishlist.name&#10;            })&#10;&#10;            # Trigger CloudFront cache invalidation if needed&#10;            if wishlist.is_public:&#10;                await self.cloudfront_service.invalidate_cache([&#10;                    f&quot;/api/v1/wishlists/shared/{wishlist.share_hash}&quot;&#10;                ])&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch wishlist created notification: {e}&quot;)&#10;&#10;    async def _dispatch_wishlist_deleted_notification(self, wishlist: Wishlist) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for wishlist deletion.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Wishlist deleted notification&quot;, extra={&#10;                &quot;event&quot;: &quot;wishlist_deleted&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id&#10;            })&#10;&#10;            # Trigger CloudFront cache invalidation&#10;            if wishlist.is_public:&#10;                await self.cloudfront_service.invalidate_cache([&#10;                    f&quot;/api/v1/wishlists/shared/{wishlist.share_hash}&quot;&#10;                ])&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch wishlist deleted notification: {e}&quot;)&#10;&#10;    async def _dispatch_item_added_notification(self, wishlist: Wishlist, item: WishlistItem) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item addition.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Item added notification&quot;, extra={&#10;                &quot;event&quot;: &quot;item_added&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;product_id&quot;: item.product_id&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch item added notification: {e}&quot;)&#10;&#10;    async def _dispatch_item_removed_notification(self, wishlist: Wishlist, product_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item removal.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Item removed notification&quot;, extra={&#10;                &quot;event&quot;: &quot;item_removed&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;product_id&quot;: product_id&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch item removed notification: {e}&quot;)&#10;&#10;    # Bulk Operations (Subtask 6.2)&#10;&#10;    async def bulk_add_items_to_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: BulkAddItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Add multiple items to a wishlist in a single operation.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The bulk add request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Bulk adding {len(request.items)} items to wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;item_count&quot;: len(request.items)&#10;        })&#10;&#10;        with metrics.timer(&quot;bulk_add_items_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Verify products exist in Algolia (batch operation)&#10;                product_ids = [item.product_id for item in request.items]&#10;                try:&#10;                    existing_products = await self.algolia_service.batch_check_products_exist(product_ids)&#10;                    missing_products = [pid for pid in product_ids if not existing_products.get(pid, False)]&#10;                    if missing_products:&#10;                        logger.warning(f&quot;Some products not found in Algolia: {missing_products}&quot;, extra={&#10;                            &quot;correlation_id&quot;: correlation_id,&#10;                            &quot;missing_products&quot;: missing_products&#10;                        })&#10;                except Exception as e:&#10;                    logger.warning(f&quot;Failed to verify product existence: {e}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                # Add items using domain model methods&#10;                added_items = []&#10;                skipped_items = []&#10;&#10;                for item_data in request.items:&#10;                    # Check if item already exists and skip_duplicates is True&#10;                    if request.skip_duplicates and wishlist.has_item(item_data.product_id):&#10;                        skipped_items.append(item_data.product_id)&#10;                        continue&#10;&#10;                    added_item = wishlist.add_item(item_data.product_id, item_data.notes)&#10;                    added_items.append(added_item)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_bulk_items_added_notification(&#10;                        updated_wishlist, added_items, skipped_items&#10;                    )&#10;                )&#10;&#10;                metrics.counter(&quot;bulk_items_added&quot;).inc()&#10;                metrics.counter(&quot;items_added_total&quot;).inc(len(added_items))&#10;                logger.info(f&quot;Successfully bulk added items to wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;added_count&quot;: len(added_items),&#10;                    &quot;skipped_count&quot;: len(skipped_items),&#10;                    &quot;total_items&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;bulk_add_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to bulk add items to wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def bulk_remove_items_from_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: BulkRemoveItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Remove multiple items from a wishlist in a single operation.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The bulk remove request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If no items were found to remove and ignore_missing is False&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Bulk removing {len(request.product_ids)} items from wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_ids&quot;: request.product_ids&#10;        })&#10;&#10;        with metrics.timer(&quot;bulk_remove_items_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Remove items using domain model methods&#10;                removed_items = []&#10;                missing_items = []&#10;&#10;                for product_id in request.product_ids:&#10;                    removed = wishlist.remove_item(product_id)&#10;                    if removed:&#10;                        removed_items.append(product_id)&#10;                    else:&#10;                        missing_items.append(product_id)&#10;&#10;                # Check if any items were removed&#10;                if not removed_items and not request.ignore_missing:&#10;                    raise ValidationError(&quot;No items found to remove from wishlist&quot;)&#10;&#10;                # Save to database if any items were removed&#10;                if removed_items:&#10;                    updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                    # Invalidate cache&#10;                    await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                    # Dispatch background notification&#10;                    asyncio.create_task(&#10;                        self._dispatch_bulk_items_removed_notification(&#10;                            updated_wishlist, removed_items, missing_items&#10;                        )&#10;                    )&#10;                else:&#10;                    updated_wishlist = wishlist&#10;&#10;                metrics.counter(&quot;bulk_items_removed&quot;).inc()&#10;                metrics.counter(&quot;items_removed_total&quot;).inc(len(removed_items))&#10;                logger.info(f&quot;Successfully bulk removed items from wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;removed_count&quot;: len(removed_items),&#10;                    &quot;missing_count&quot;: len(missing_items),&#10;                    &quot;total_items&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;bulk_remove_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to bulk remove items from wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def move_items_between_wishlists(&#10;        self,&#10;        user_id: str,&#10;        request: MoveItemsRequest&#10;    ) -&gt; Dict[str, Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Move or copy items between wishlists.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            request: The move items request data&#10;&#10;        Returns:&#10;            Dictionary with 'source' and 'target' wishlists&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If either wishlist doesn't exist&#10;            ValidationError: If items don't exist in source wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Moving {len(request.product_ids)} items between wishlists&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;source_wishlist_id&quot;: request.source_wishlist_id,&#10;            &quot;target_wishlist_id&quot;: request.target_wishlist_id,&#10;            &quot;copy_mode&quot;: request.copy_instead_of_move&#10;        })&#10;&#10;        with metrics.timer(&quot;move_items_duration&quot;):&#10;            try:&#10;                # Get both wishlists&#10;                source_wishlist = await self.wishlist_repo.get_by_id(user_id, request.source_wishlist_id)&#10;                if not source_wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Source wishlist {request.source_wishlist_id} not found&quot;)&#10;&#10;                target_wishlist = await self.wishlist_repo.get_by_id(user_id, request.target_wishlist_id)&#10;                if not target_wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Target wishlist {request.target_wishlist_id} not found&quot;)&#10;&#10;                # Find items to move/copy&#10;                items_to_move = []&#10;                missing_items = []&#10;&#10;                for product_id in request.product_ids:&#10;                    item = source_wishlist.get_item(product_id)&#10;                    if item:&#10;                        items_to_move.append(item)&#10;                    else:&#10;                        missing_items.append(product_id)&#10;&#10;                if not items_to_move:&#10;                    raise ValidationError(&quot;No items found in source wishlist to move&quot;)&#10;&#10;                # Add items to target wishlist&#10;                for item in items_to_move:&#10;                    target_wishlist.add_item(item.product_id, item.notes)&#10;&#10;                # Remove items from source wishlist if moving (not copying)&#10;                if not request.copy_instead_of_move:&#10;                    for item in items_to_move:&#10;                        source_wishlist.remove_item(item.product_id)&#10;&#10;                # Save both wishlists&#10;                updated_source = await self.wishlist_repo.update(source_wishlist)&#10;                updated_target = await self.wishlist_repo.update(target_wishlist)&#10;&#10;                # Invalidate cache for both wishlists&#10;                await self._invalidate_wishlist_cache(user_id, request.source_wishlist_id)&#10;                await self._invalidate_wishlist_cache(user_id, request.target_wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_items_moved_notification(&#10;                        updated_source, updated_target, items_to_move, request.copy_instead_of_move&#10;                    )&#10;                )&#10;&#10;                operation = &quot;copied&quot; if request.copy_instead_of_move else &quot;moved&quot;&#10;                metrics.counter(f&quot;items_{operation}&quot;).inc()&#10;                metrics.counter(f&quot;items_{operation}_total&quot;).inc(len(items_to_move))&#10;&#10;                logger.info(f&quot;Successfully {operation} items between wishlists&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;moved_count&quot;: len(items_to_move),&#10;                    &quot;missing_count&quot;: len(missing_items)&#10;                })&#10;&#10;                return {&#10;                    &quot;source&quot;: updated_source,&#10;                    &quot;target&quot;: updated_target&#10;                }&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;move_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to move items between wishlists: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def update_item_in_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        product_id: str,&#10;        request: UpdateItemRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Update an existing item in a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            product_id: The ID of the product to update&#10;            request: The update request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If the item doesn't exist in the wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Updating item {product_id} in wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_id&quot;: product_id&#10;        })&#10;&#10;        with metrics.timer(&quot;update_item_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Update item using domain model method&#10;                updated = wishlist.update_item_notes(product_id, request.notes)&#10;                if not updated:&#10;                    raise ValidationError(f&quot;Item {product_id} not found in wishlist&quot;)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_item_updated_notification(updated_wishlist, product_id)&#10;                )&#10;&#10;                metrics.counter(&quot;item_updated&quot;).inc()&#10;                logger.info(f&quot;Successfully updated item in wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;update_item_error&quot;).inc()&#10;                logger.error(f&quot;Failed to update item in wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;product_id&quot;: product_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def reorder_wishlist_items(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        ordered_product_ids: List[str]&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Reorder items in a wishlist based on provided product ID sequence.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            ordered_product_ids: List of product IDs in desired order&#10;&#10;        Returns:&#10;            The updated wishlist with reordered items&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If product IDs don't match wishlist items&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Reordering items in wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;item_count&quot;: len(ordered_product_ids)&#10;        })&#10;&#10;        with metrics.timer(&quot;reorder_items_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Validate that all product IDs exist in the wishlist&#10;                existing_product_ids = set(wishlist.get_product_ids())&#10;                provided_product_ids = set(ordered_product_ids)&#10;&#10;                if existing_product_ids != provided_product_ids:&#10;                    missing_ids = existing_product_ids - provided_product_ids&#10;                    extra_ids = provided_product_ids - existing_product_ids&#10;                    error_msg = &quot;Product ID mismatch: &quot;&#10;                    if missing_ids:&#10;                        error_msg += f&quot;missing {list(missing_ids)}, &quot;&#10;                    if extra_ids:&#10;                        error_msg += f&quot;extra {list(extra_ids)}&quot;&#10;                    raise ValidationError(error_msg.rstrip(&quot;, &quot;))&#10;&#10;                # Create a mapping of product_id to item&#10;                item_map = {item.product_id: item for item in wishlist.items}&#10;&#10;                # Reorder items based on provided sequence&#10;                reordered_items = [item_map[product_id] for product_id in ordered_product_ids]&#10;                wishlist.items = reordered_items&#10;                wishlist.updated_at = datetime.utcnow()&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_items_reordered_notification(updated_wishlist)&#10;                )&#10;&#10;                metrics.counter(&quot;items_reordered&quot;).inc()&#10;                logger.info(f&quot;Successfully reordered items in wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;reorder_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to reorder items in wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    # Additional background notification methods&#10;&#10;    async def _dispatch_bulk_items_added_notification(&#10;        self,&#10;        wishlist: Wishlist,&#10;        added_items: List[WishlistItem],&#10;        skipped_items: List[str]&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for bulk item addition.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Bulk items added notification&quot;, extra={&#10;                &quot;event&quot;: &quot;bulk_items_added&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;added_count&quot;: len(added_items),&#10;                &quot;skipped_count&quot;: len(skipped_items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch bulk items added notification: {e}&quot;)&#10;&#10;    async def _dispatch_bulk_items_removed_notification(&#10;        self,&#10;        wishlist: Wishlist,&#10;        removed_items: List[str],&#10;        missing_items: List[str]&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for bulk item removal.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Bulk items removed notification&quot;, extra={&#10;                &quot;event&quot;: &quot;bulk_items_removed&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;removed_count&quot;: len(removed_items),&#10;                &quot;missing_count&quot;: len(missing_items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch bulk items removed notification: {e}&quot;)&#10;&#10;    async def _dispatch_items_moved_notification(&#10;        self,&#10;        source_wishlist: Wishlist,&#10;        target_wishlist: Wishlist,&#10;        moved_items: List[WishlistItem],&#10;        is_copy: bool&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item movement.&quot;&quot;&quot;&#10;        try:&#10;            operation = &quot;copied&quot; if is_copy else &quot;moved&quot;&#10;            logger.info(f&quot;Items {operation} notification&quot;, extra={&#10;                &quot;event&quot;: f&quot;items_{operation}&quot;,&#10;                &quot;user_id&quot;: source_wishlist.user_id,&#10;                &quot;source_wishlist_id&quot;: source_wishlist.wishlist_id,&#10;                &quot;target_wishlist_id&quot;: target_wishlist.wishlist_id,&#10;                &quot;item_count&quot;: len(moved_items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch items moved notification: {e}&quot;)&#10;&#10;    async def _dispatch_item_updated_notification(self, wishlist: Wishlist, product_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item update.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Item updated notification&quot;, extra={&#10;                &quot;event&quot;: &quot;item_updated&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;product_id&quot;: product_id&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch item updated notification: {e}&quot;)&#10;&#10;    async def _dispatch_items_reordered_notification(self, wishlist: Wishlist) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item reordering.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Items reordered notification&quot;, extra={&#10;                &quot;event&quot;: &quot;items_reordered&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;item_count&quot;: len(wishlist.items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch items reordered notification: {e}&quot;)&#10;&#10;    # Product Enrichment Helper Methods (Subtask 6.3)&#10;&#10;    async def _get_fallback_product_data(&#10;        self,&#10;        product_id: str,&#10;        country: str,&#10;        language: str&#10;    ) -&gt; Optional[Dict[str, Any]]:&#10;        &quot;&quot;&quot;&#10;        Get fallback product data from cache with multiple fallback strategies.&#10;        &quot;&quot;&quot;&#10;        if not self.cache_repo:&#10;            return None&#10;&#10;        try:&#10;            # Try exact cache key first&#10;            cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;            cached_data = await self.cache_repo.get(cache_key)&#10;            if cached_data:&#10;                return cached_data&#10;&#10;            # Try fallback to English if not already English&#10;            if language != 'en':&#10;                fallback_key = f&quot;algolia:product:{product_id}:{country}:en&quot;&#10;                cached_data = await self.cache_repo.get(fallback_key)&#10;                if cached_data:&#10;                    logger.debug(f&quot;Using English fallback for product {product_id}&quot;)&#10;                    return cached_data&#10;&#10;            # Try fallback to UAE if different country&#10;            if country != 'ae':&#10;                fallback_key = f&quot;algolia:product:{product_id}:ae:{language}&quot;&#10;                cached_data = await self.cache_repo.get(fallback_key)&#10;                if cached_data:&#10;                    logger.debug(f&quot;Using UAE fallback for product {product_id}&quot;)&#10;                    return cached_data&#10;&#10;            # Try UAE + English as final fallback&#10;            if country != 'ae' or language != 'en':&#10;                fallback_key = f&quot;algolia:product:{product_id}:ae:en&quot;&#10;                cached_data = await self.cache_repo.get(fallback_key)&#10;                if cached_data:&#10;                    logger.debug(f&quot;Using UAE+English fallback for product {product_id}&quot;)&#10;                    return cached_data&#10;&#10;            return None&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting fallback product data for {product_id}: {e}&quot;)&#10;            return None&#10;&#10;    async def _get_cached_products_batch(&#10;        self,&#10;        product_ids: List[str],&#10;        country: str,&#10;        language: str&#10;    ) -&gt; Dict[str, Dict[str, Any]]:&#10;        &quot;&quot;&quot;&#10;        Get multiple products from cache with fallback strategies.&#10;        &quot;&quot;&quot;&#10;        if not self.cache_repo or not product_ids:&#10;            return {}&#10;&#10;        try:&#10;            cached_products = {}&#10;&#10;            # Create tasks for concurrent cache lookups&#10;            cache_tasks = []&#10;            for product_id in product_ids:&#10;                cache_tasks.append(&#10;                    self._get_fallback_product_data(product_id, country, language)&#10;                )&#10;&#10;            # Execute all cache lookups concurrently&#10;            results = await asyncio.gather(*cache_tasks, return_exceptions=True)&#10;&#10;            # Process results&#10;            for product_id, result in zip(product_ids, results):&#10;                if isinstance(result, dict) and result:&#10;                    cached_products[product_id] = result&#10;                elif isinstance(result, Exception):&#10;                    logger.warning(f&quot;Cache lookup failed for product {product_id}: {result}&quot;)&#10;&#10;            logger.debug(f&quot;Retrieved {len(cached_products)}/{len(product_ids)} products from cache&quot;)&#10;            return cached_products&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting cached products batch: {e}&quot;)&#10;            return {}&#10;&#10;    async def _warm_product_cache(&#10;        self,&#10;        product_ids: List[str],&#10;        country: str,&#10;        language: str&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;&#10;        Warm the cache for frequently accessed products (background task).&#10;&#10;        This method runs in the background to pre-fetch and cache products&#10;        that are likely to be accessed again soon.&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_repo or not product_ids:&#10;                return&#10;&#10;            # Check which products are not in cache&#10;            uncached_products = []&#10;            for product_id in product_ids:&#10;                cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                if not await self.cache_repo.exists(cache_key):&#10;                    uncached_products.append(product_id)&#10;&#10;            if not uncached_products:&#10;                logger.debug(&quot;All products already cached, no warming needed&quot;)&#10;                return&#10;&#10;            # Limit cache warming to avoid overwhelming the system&#10;            max_warm_products = 20&#10;            if len(uncached_products) &gt; max_warm_products:&#10;                uncached_products = uncached_products[:max_warm_products]&#10;&#10;            logger.info(f&quot;Warming cache for {len(uncached_products)} products&quot;)&#10;&#10;            # Fetch products from Algolia&#10;            warmed_products = await self.algolia_service.get_products_by_ids(&#10;                uncached_products, country, language&#10;            )&#10;&#10;            # Cache the results with extended TTL for warmed cache&#10;            cache_tasks = []&#10;            extended_ttl = self.settings.CACHE_TTL_PRODUCTS * 2  # Double TTL for warmed cache&#10;&#10;            for product_id, product_data in warmed_products.items():&#10;                cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                cache_tasks.append(&#10;                    self.cache_repo.set(cache_key, product_data, ttl=extended_ttl)&#10;                )&#10;&#10;            if cache_tasks:&#10;                await asyncio.gather(*cache_tasks, return_exceptions=True)&#10;                metrics.counter(&quot;cache_warmed_products&quot;).inc(len(cache_tasks))&#10;                logger.info(f&quot;Successfully warmed cache for {len(cache_tasks)} products&quot;)&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error warming product cache: {e}&quot;)&#10;            metrics.counter(&quot;cache_warming_error&quot;).inc()&#10;&#10;    async def prefetch_popular_products(&#10;        self,&#10;        country: str = 'ae',&#10;        language: str = 'en',&#10;        limit: int = 100&#10;    ) -&gt; int:&#10;        &quot;&quot;&quot;&#10;        Prefetch popular products to warm the cache proactively.&#10;&#10;        This method can be called periodically to cache frequently accessed products.&#10;&#10;        Args:&#10;            country: Country code for localization&#10;            language: Language code for localization&#10;            limit: Maximum number of products to prefetch&#10;&#10;        Returns:&#10;            Number of products successfully cached&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Prefetching popular products&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;country&quot;: country,&#10;            &quot;language&quot;: language,&#10;            &quot;limit&quot;: limit&#10;        })&#10;&#10;        try:&#10;            with metrics.timer(&quot;prefetch_popular_products_duration&quot;):&#10;                # Get popular product IDs from recent wishlists&#10;                # This is a simplified implementation - in production you might want to&#10;                # track product popularity metrics or use analytics data&#10;                popular_product_ids = await self._get_popular_product_ids(limit)&#10;&#10;                if not popular_product_ids:&#10;                    logger.info(&quot;No popular products found to prefetch&quot;)&#10;                    return 0&#10;&#10;                # Check which products are not already cached&#10;                uncached_products = []&#10;                for product_id in popular_product_ids:&#10;                    cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                    if not await self.cache_repo.exists(cache_key):&#10;                        uncached_products.append(product_id)&#10;&#10;                if not uncached_products:&#10;                    logger.info(&quot;All popular products already cached&quot;)&#10;                    return 0&#10;&#10;                # Fetch and cache products&#10;                products = await self.algolia_service.get_products_by_ids(&#10;                    uncached_products, country, language&#10;                )&#10;&#10;                # Cache with extended TTL&#10;                cache_tasks = []&#10;                extended_ttl = self.settings.CACHE_TTL_PRODUCTS * 3  # Triple TTL for popular products&#10;&#10;                for product_id, product_data in products.items():&#10;                    cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                    cache_tasks.append(&#10;                        self.cache_repo.set(cache_key, product_data, ttl=extended_ttl)&#10;                    )&#10;&#10;                if cache_tasks:&#10;                    await asyncio.gather(*cache_tasks, return_exceptions=True)&#10;&#10;                cached_count = len(cache_tasks)&#10;                metrics.counter(&quot;popular_products_prefetched&quot;).inc(cached_count)&#10;                logger.info(f&quot;Successfully prefetched {cached_count} popular products&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;cached_count&quot;: cached_count&#10;                })&#10;&#10;                return cached_count&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error prefetching popular products: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            metrics.counter(&quot;prefetch_popular_products_error&quot;).inc()&#10;            return 0&#10;&#10;    async def _get_popular_product_ids(self, limit: int) -&gt; List[str]:&#10;        &quot;&quot;&quot;&#10;        Get popular product IDs based on wishlist data.&#10;&#10;        This is a simplified implementation that gets products from recent wishlists.&#10;        In production, you might want to use analytics data or maintain popularity metrics.&#10;        &quot;&quot;&quot;&#10;        try:&#10;            # This is a placeholder implementation&#10;            # In a real system, you would:&#10;            # 1. Query analytics data for popular products&#10;            # 2. Use machine learning to predict popular products&#10;            # 3. Maintain popularity counters in Redis&#10;            # 4. Use business intelligence data&#10;&#10;            # For now, we'll return an empty list&#10;            # The actual implementation would depend on your analytics infrastructure&#10;            return []&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting popular product IDs: {e}&quot;)&#10;            return []&#10;&#10;    # Cache Management and Statistics Methods (Subtask 6.4)&#10;&#10;    async def get_cache_statistics(self) -&gt; Dict[str, Any]:&#10;        &quot;&quot;&quot;&#10;        Get comprehensive cache statistics for wishlist operations.&#10;&#10;        Returns:&#10;            Dictionary containing cache performance metrics&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_management_service:&#10;                logger.warning(&quot;Cache management service not available&quot;)&#10;                return {&quot;error&quot;: &quot;Cache management service not available&quot;}&#10;&#10;            # Get cache statistics from cache management service&#10;            stats = await self.cache_management_service.get_cache_statistics()&#10;&#10;            # Add wishlist-specific metrics&#10;            wishlist_stats = {&#10;                &quot;cache_statistics&quot;: stats,&#10;                &quot;service_metrics&quot;: {&#10;                    &quot;total_cache_operations&quot;: metrics.get_counter_value(&quot;cache_operations_total&quot;),&#10;                    &quot;cache_hit_rate&quot;: self._calculate_cache_hit_rate(),&#10;                    &quot;avg_response_time_ms&quot;: metrics.get_histogram_value(&quot;wishlist_operation_duration&quot;),&#10;                    &quot;last_updated&quot;: datetime.utcnow().isoformat()&#10;                }&#10;            }&#10;&#10;            return wishlist_stats&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting cache statistics: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def optimize_cache_performance(self) -&gt; Dict[str, Any]:&#10;        &quot;&quot;&quot;&#10;        Analyze and optimize cache performance for wishlist operations.&#10;&#10;        Returns:&#10;            Dictionary with optimization results and recommendations&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_management_service:&#10;                return {&quot;error&quot;: &quot;Cache management service not available&quot;}&#10;&#10;            # Run cache optimization&#10;            optimization_results = await self.cache_management_service.optimize_cache_performance()&#10;&#10;            # Add wishlist-specific optimizations&#10;            wishlist_optimizations = await self._perform_wishlist_cache_optimizations()&#10;            optimization_results[&quot;wishlist_optimizations&quot;] = wishlist_optimizations&#10;&#10;            return optimization_results&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error optimizing cache performance: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def warm_wishlist_caches(&#10;        self,&#10;        strategy: str = &quot;popular&quot;,&#10;        limit: int = 100&#10;    ) -&gt; Dict[str, int]:&#10;        &quot;&quot;&quot;&#10;        Warm wishlist caches using strategic approaches.&#10;&#10;        Args:&#10;            strategy: Warming strategy (&quot;popular&quot;, &quot;recent&quot;, &quot;predictive&quot;)&#10;            limit: Maximum number of items to warm&#10;&#10;        Returns:&#10;            Dictionary with warming results by cache type&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Starting wishlist cache warming&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;strategy&quot;: strategy,&#10;            &quot;limit&quot;: limit&#10;        })&#10;&#10;        try:&#10;            warming_results = {}&#10;&#10;            # Warm product cache&#10;            if self.cache_management_service:&#10;                from app.services.cache_management_service import CachePartition&#10;                product_count = await self.cache_management_service.warm_cache_strategically(&#10;                    CachePartition.PRODUCTS, strategy, limit&#10;                )&#10;                warming_results[&quot;products&quot;] = product_count&#10;&#10;                # Warm wishlist cache&#10;                wishlist_count = await self.cache_management_service.warm_cache_strategically(&#10;                    CachePartition.WISHLISTS, strategy, limit&#10;                )&#10;                warming_results[&quot;wishlists&quot;] = wishlist_count&#10;&#10;            # Warm frequently accessed user wishlists&#10;            user_warming_count = await self._warm_user_wishlist_cache(strategy, limit)&#10;            warming_results[&quot;user_wishlists&quot;] = user_warming_count&#10;&#10;            total_warmed = sum(warming_results.values())&#10;            logger.info(f&quot;Cache warming completed&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;total_warmed&quot;: total_warmed,&#10;                &quot;results&quot;: warming_results&#10;            })&#10;&#10;            return warming_results&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error warming wishlist caches: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def invalidate_related_caches(&#10;        self,&#10;        event_type: str,&#10;        context: Dict[str, Any]&#10;    ) -&gt; Dict[str, int]:&#10;        &quot;&quot;&quot;&#10;        Intelligently invalidate related caches based on event type.&#10;&#10;        Args:&#10;            event_type: Type of event that triggered invalidation&#10;            context: Context data for the event&#10;&#10;        Returns:&#10;            Dictionary with invalidation results&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_management_service:&#10;                # Fallback to basic invalidation&#10;                return await self._basic_cache_invalidation(event_type, context)&#10;&#10;            # Use intelligent cache invalidation&#10;            return await self.cache_management_service.invalidate_cache_intelligently(&#10;                trigger_event=event_type,&#10;                context=context&#10;            )&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error invalidating related caches: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    # Helper methods for cache management&#10;&#10;    def _calculate_cache_hit_rate(self) -&gt; float:&#10;        &quot;&quot;&quot;Calculate overall cache hit rate for wishlist operations.&quot;&quot;&quot;&#10;        try:&#10;            hit_count = metrics.get_counter_value(&quot;cache_hit_total&quot;) or 0&#10;            miss_count = metrics.get_counter_value(&quot;cache_miss_total&quot;) or 0&#10;            total_requests = hit_count + miss_count&#10;&#10;            if total_requests == 0:&#10;                return 0.0&#10;&#10;            return hit_count / total_requests&#10;        except Exception:&#10;            return 0.0&#10;&#10;    async def _perform_wishlist_cache_optimizations(self) -&gt; Dict[str, Any]:&#10;        &quot;&quot;&quot;Perform wishlist-specific cache optimizations.&quot;&quot;&quot;&#10;        optimizations = {&#10;            &quot;actions_taken&quot;: [],&#10;            &quot;recommendations&quot;: []&#10;        }&#10;&#10;        try:&#10;            # Check cache hit rates and recommend optimizations&#10;            hit_rate = self._calculate_cache_hit_rate()&#10;&#10;            if hit_rate &lt; 0.7:  # Less than 70% hit rate&#10;                optimizations[&quot;recommendations&quot;].append(&#10;                    f&quot;Consider implementing cache warming for frequently accessed wishlists (current hit rate: {hit_rate:.2%})&quot;&#10;                )&#10;&#10;            # Check for cache key patterns that might need optimization&#10;            # This would analyze actual cache usage patterns in production&#10;            optimizations[&quot;recommendations&quot;].append(&#10;                &quot;Consider implementing cache key compression for large wishlist objects&quot;&#10;            )&#10;&#10;            return optimizations&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error performing wishlist cache optimizations: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def _warm_user_wishlist_cache(self, strategy: str, limit: int) -&gt; int:&#10;        &quot;&quot;&quot;Warm cache for user wishlists based on strategy.&quot;&quot;&quot;&#10;        try:&#10;            # This is a placeholder implementation&#10;            # In production, you would:&#10;            # 1. Identify frequently accessed users&#10;            # 2. Pre-fetch their wishlists&#10;            # 3. Cache the results with appropriate TTL&#10;&#10;            warmed_count = 0&#10;&#10;            if strategy == &quot;popular&quot;:&#10;                # Get popular users (placeholder)&#10;                popular_users = []  # Would come from analytics&#10;&#10;                for user_id in popular_users[:limit]:&#10;                    try:&#10;                        # Pre-fetch and cache user wishlists&#10;                        wishlists = await self.get_user_wishlists(user_id)&#10;                        if wishlists:&#10;                            warmed_count += 1&#10;                    except Exception as e:&#10;                        logger.warning(f&quot;Failed to warm cache for user {user_id}: {e}&quot;)&#10;&#10;            return warmed_count&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error warming user wishlist cache: {e}&quot;)&#10;            return 0&#10;&#10;    async def _basic_cache_invalidation(&#10;        self,&#10;        event_type: str,&#10;        context: Dict[str, Any]&#10;    ) -&gt; Dict[str, int]:&#10;        &quot;&quot;&quot;Basic cache invalidation fallback when cache management service is unavailable.&quot;&quot;&quot;&#10;        results = {}&#10;&#10;        try:&#10;            user_id = context.get(&quot;user_id&quot;)&#10;            wishlist_id = context.get(&quot;wishlist_id&quot;)&#10;&#10;            if event_type.startswith(&quot;wishlist:&quot;) and user_id:&#10;                # Invalidate user and wishlist caches&#10;                user_pattern = f&quot;user_wishlists:{user_id}:*&quot;&#10;                user_count = await self.cache_repo.delete_pattern(user_pattern)&#10;                results[user_pattern] = user_count&#10;&#10;                if wishlist_id:&#10;                    wishlist_pattern = f&quot;wishlist:{user_id}:{wishlist_id}:*&quot;&#10;                    wishlist_count = await self.cache_repo.delete_pattern(wishlist_pattern)&#10;                    results[wishlist_pattern] = wishlist_count&#10;&#10;            return results&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error in basic cache invalidation: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;" />
              <option name="updatedContent" value="&quot;&quot;&quot;&#10;Core wishlist business logic service.&#10;&#10;This service implements all wishlist operations including CRUD operations,&#10;product enrichment, caching, and background job dispatching.&#10;&quot;&quot;&quot;&#10;import asyncio&#10;import logging&#10;from datetime import datetime&#10;from typing import List, Optional, Dict, Any&#10;from uuid import uuid4&#10;&#10;from app.models.domain.wishlist import Wishlist, WishlistItem&#10;from app.repositories.wishlist_repo import WishlistRepository&#10;from app.repositories.cache_repo import CacheRepository&#10;from app.services.algolia_service import AlgoliaService&#10;from app.services.cloudfront_service import CloudFrontService&#10;from app.services.cache_management_service import CacheManagementService&#10;from app.schemas.requests.wishlist import (&#10;    CreateWishlistRequest,&#10;    UpdateWishlistRequest&#10;)&#10;from app.schemas.requests.item import (&#10;    AddItemRequest,&#10;    BulkAddItemsRequest,&#10;    BulkRemoveItemsRequest,&#10;    MoveItemsRequest,&#10;    UpdateItemRequest&#10;)&#10;from app.core.exceptions import (&#10;    WishlistNotFoundError,&#10;    ValidationError,&#10;    ExternalServiceError&#10;)&#10;from app.core.metrics import metrics&#10;from app.utils.helpers import generate_correlation_id&#10;&#10;logger = logging.getLogger(__name__)&#10;&#10;&#10;class WishlistService:&#10;    &quot;&quot;&quot;&#10;    Core business logic service for wishlist operations.&#10;&#10;    Handles all wishlist CRUD operations, product enrichment,&#10;    caching strategies, and background job dispatching.&#10;    &quot;&quot;&quot;&#10;&#10;    def __init__(&#10;        self,&#10;        wishlist_repo: WishlistRepository,&#10;        cache_repo: CacheRepository,&#10;        algolia_service: AlgoliaService,&#10;        cloudfront_service: CloudFrontService,&#10;        cache_management_service: Optional[CacheManagementService] = None,&#10;    ):&#10;        self.wishlist_repo = wishlist_repo&#10;        self.cache_repo = cache_repo&#10;        self.algolia_service = algolia_service&#10;        self.cloudfront_service = cloudfront_service&#10;        self.cache_management_service = cache_management_service&#10;&#10;    async def create_wishlist(&#10;        self,&#10;        user_id: str,&#10;        request: CreateWishlistRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Create a new wishlist for a user.&#10;&#10;        Args:&#10;            user_id: The ID of the user creating the wishlist&#10;            request: The wishlist creation request data&#10;&#10;        Returns:&#10;            The created wishlist&#10;&#10;        Raises:&#10;            ValidationError: If the request data is invalid&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Creating wishlist for user {user_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_name&quot;: request.name&#10;        })&#10;&#10;        with metrics.timer(&quot;wishlist_create_duration&quot;):&#10;            try:&#10;                # Handle default wishlist logic&#10;                if request.is_default:&#10;                    await self._unset_default_wishlist(user_id)&#10;&#10;                # Create new wishlist using domain model factory&#10;                wishlist = Wishlist.create_new(&#10;                    user_id=user_id,&#10;                    name=request.name,&#10;                    is_default=request.is_default,&#10;                    is_public=False  # New wishlists are private by default&#10;                )&#10;&#10;                # Save to database&#10;                created_wishlist = await self.wishlist_repo.create(wishlist)&#10;&#10;                # Invalidate user's wishlist cache&#10;                await self._invalidate_user_cache(user_id)&#10;&#10;                # Dispatch background notification (fire and forget)&#10;                asyncio.create_task(&#10;                    self._dispatch_wishlist_created_notification(created_wishlist)&#10;                )&#10;&#10;                metrics.counter(&quot;wishlist_created&quot;).inc()&#10;                logger.info(f&quot;Successfully created wishlist {created_wishlist.wishlist_id}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;wishlist_id&quot;: created_wishlist.wishlist_id&#10;                })&#10;&#10;                return created_wishlist&#10;&#10;            except Exception as e:&#10;                metrics.counter(&quot;wishlist_create_error&quot;).inc()&#10;                logger.error(f&quot;Failed to create wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def get_user_wishlists(&#10;        self,&#10;        user_id: str,&#10;        country: str = &quot;ae&quot;,&#10;        language: str = &quot;en&quot;,&#10;        include_products: bool = True&#10;    ) -&gt; List[Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Get all wishlists for a user with optional product enrichment.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            country: Country code for product localization&#10;            language: Language code for product localization&#10;            include_products: Whether to enrich items with product data&#10;&#10;        Returns:&#10;            List of user's wishlists&#10;        &quot;&quot;&quot;&#10;        import pdb; pdb.set_trace()&#10;        correlation_id = generate_correlation_id()&#10;        cache_key = f&quot;user_wishlists:{user_id}:{country}:{language}:{include_products}&quot;&#10;&#10;        logger.info(f&quot;Getting wishlists for user {user_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;country&quot;: country,&#10;            &quot;language&quot;: language&#10;        })&#10;&#10;        # Try cache first&#10;        try:&#10;            cached_result = await self.cache_repo.get(cache_key)&#10;            if cached_result:&#10;                metrics.counter(&quot;cache_hit&quot;, {&quot;type&quot;: &quot;user_wishlists&quot;}).inc()&#10;                logger.debug(f&quot;Cache hit for user wishlists&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;cache_key&quot;: cache_key&#10;                })&#10;                return cached_result&#10;        except Exception as e:&#10;            logger.warning(f&quot;Cache lookup failed: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id&#10;            })&#10;&#10;&#10;        with metrics.timer(&quot;get_user_wishlists_duration&quot;):&#10;            try:&#10;                # Get from database&#10;                wishlists = await self.wishlist_repo.get_by_user_id(user_id)&#10;&#10;                # Enrich with product details if requested&#10;                if include_products and wishlists:&#10;                    enriched_wishlists = await self._enrich_wishlists_with_products(&#10;                        wishlists, country, language&#10;                    )&#10;                else:&#10;                    enriched_wishlists = wishlists&#10;&#10;                # Cache the result&#10;                try:&#10;                    await self.cache_repo.set(&#10;                        cache_key,&#10;                        enriched_wishlists,&#10;                        ttl=300  # 5 minutes&#10;                    )&#10;                except Exception as e:&#10;                    logger.warning(f&quot;Failed to cache result: {e}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                metrics.counter(&quot;cache_miss&quot;, {&quot;type&quot;: &quot;user_wishlists&quot;}).inc()&#10;                logger.info(f&quot;Retrieved {len(enriched_wishlists)} wishlists for user&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;wishlist_count&quot;: len(enriched_wishlists)&#10;                })&#10;&#10;                return enriched_wishlists&#10;&#10;            except Exception as e:&#10;                metrics.counter(&quot;get_user_wishlists_error&quot;).inc()&#10;                logger.error(f&quot;Failed to get user wishlists: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def get_wishlist_by_id(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        country: str = &quot;ae&quot;,&#10;        language: str = &quot;en&quot;,&#10;        include_products: bool = True&#10;    ) -&gt; Optional[Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Get a specific wishlist by ID.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            country: Country code for product localization&#10;            language: Language code for product localization&#10;            include_products: Whether to enrich items with product data&#10;&#10;        Returns:&#10;            The wishlist if found, None otherwise&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        cache_key = f&quot;wishlist:{user_id}:{wishlist_id}:{country}:{language}:{include_products}&quot;&#10;&#10;        logger.info(f&quot;Getting wishlist {wishlist_id} for user {user_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id&#10;        })&#10;&#10;        # Try cache first&#10;        try:&#10;            cached_result = await self.cache_repo.get(cache_key)&#10;            if cached_result:&#10;                metrics.counter(&quot;cache_hit&quot;, {&quot;type&quot;: &quot;wishlist&quot;}).inc()&#10;                return cached_result&#10;        except Exception as e:&#10;            logger.warning(f&quot;Cache lookup failed: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id&#10;            })&#10;&#10;        try:&#10;            # Get from database&#10;            wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;            if not wishlist:&#10;                logger.info(f&quot;Wishlist {wishlist_id} not found&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;                return None&#10;&#10;            # Enrich with product details if requested&#10;            if include_products:&#10;                enriched_wishlist = await self._enrich_wishlist_with_products(&#10;                    wishlist, country, language&#10;                )&#10;            else:&#10;                enriched_wishlist = wishlist&#10;&#10;            # Cache the result&#10;            try:&#10;                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=300)&#10;            except Exception as e:&#10;                logger.warning(f&quot;Failed to cache result: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;            metrics.counter(&quot;cache_miss&quot;, {&quot;type&quot;: &quot;wishlist&quot;}).inc()&#10;            return enriched_wishlist&#10;&#10;        except Exception as e:&#10;            metrics.counter(&quot;get_wishlist_error&quot;).inc()&#10;            logger.error(f&quot;Failed to get wishlist: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;user_id&quot;: user_id,&#10;                &quot;wishlist_id&quot;: wishlist_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            raise&#10;&#10;    async def update_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: UpdateWishlistRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Update a wishlist's properties.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist to update&#10;            request: The update request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Updating wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id&#10;        })&#10;&#10;        with metrics.timer(&quot;wishlist_update_duration&quot;):&#10;            try:&#10;                # Get existing wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Handle default wishlist logic&#10;                if request.is_default is not None and request.is_default and not wishlist.is_default:&#10;                    await self._unset_default_wishlist(user_id)&#10;&#10;                # Update fields&#10;                if request.name is not None:&#10;                    wishlist.name = request.name&#10;                if request.is_default is not None:&#10;                    wishlist.is_default = request.is_default&#10;&#10;                wishlist.updated_at = datetime.utcnow()&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                metrics.counter(&quot;wishlist_updated&quot;).inc()&#10;                logger.info(f&quot;Successfully updated wishlist {wishlist_id}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;wishlist_update_error&quot;).inc()&#10;                logger.error(f&quot;Failed to update wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def delete_wishlist(self, user_id: str, wishlist_id: str) -&gt; bool:&#10;        &quot;&quot;&quot;&#10;        Delete a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist to delete&#10;&#10;        Returns:&#10;            True if deleted successfully&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Deleting wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id&#10;        })&#10;&#10;        with metrics.timer(&quot;wishlist_delete_duration&quot;):&#10;            try:&#10;                # Verify wishlist exists&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Delete from database&#10;                success = await self.wishlist_repo.delete(user_id, wishlist_id)&#10;&#10;                if success:&#10;                    # Invalidate cache&#10;                    await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                    # Dispatch background notification&#10;                    asyncio.create_task(&#10;                        self._dispatch_wishlist_deleted_notification(wishlist)&#10;                    )&#10;&#10;                    metrics.counter(&quot;wishlist_deleted&quot;).inc()&#10;                    logger.info(f&quot;Successfully deleted wishlist {wishlist_id}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                return success&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;wishlist_delete_error&quot;).inc()&#10;                logger.error(f&quot;Failed to delete wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def add_item_to_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: AddItemRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Add an item to a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The add item request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Adding item {request.product_id} to wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_id&quot;: request.product_id&#10;        })&#10;&#10;        with metrics.timer(&quot;add_item_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Verify product exists in Algolia (optional validation)&#10;                try:&#10;                    product_exists = await self.algolia_service.product_exists(request.product_id)&#10;                    if not product_exists:&#10;                        logger.warning(f&quot;Product {request.product_id} not found in Algolia&quot;, extra={&#10;                            &quot;correlation_id&quot;: correlation_id,&#10;                            &quot;product_id&quot;: request.product_id&#10;                        })&#10;                        # Continue anyway - product might be temporarily unavailable&#10;                except Exception as e:&#10;                    logger.warning(f&quot;Failed to verify product existence: {e}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                # Add item using domain model method&#10;                added_item = wishlist.add_item(request.product_id, request.notes)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_item_added_notification(updated_wishlist, added_item)&#10;                )&#10;&#10;                metrics.counter(&quot;item_added&quot;).inc()&#10;                logger.info(f&quot;Successfully added item to wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;item_count&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;add_item_error&quot;).inc()&#10;                logger.error(f&quot;Failed to add item to wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;product_id&quot;: request.product_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def remove_item_from_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        product_id: str&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Remove an item from a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            product_id: The ID of the product to remove&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If the item doesn't exist in the wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Removing item {product_id} from wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_id&quot;: product_id&#10;        })&#10;&#10;        with metrics.timer(&quot;remove_item_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Remove item using domain model method&#10;                removed = wishlist.remove_item(product_id)&#10;                if not removed:&#10;                    raise ValidationError(f&quot;Product {product_id} not found in wishlist&quot;)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_item_removed_notification(updated_wishlist, product_id)&#10;                )&#10;&#10;                metrics.counter(&quot;item_removed&quot;).inc()&#10;                logger.info(f&quot;Successfully removed item from wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;item_count&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;remove_item_error&quot;).inc()&#10;                logger.error(f&quot;Failed to remove item from wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;product_id&quot;: product_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def bulk_add_items_to_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: BulkAddItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Bulk add items to a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The bulk add request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Bulk adding items to wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_ids&quot;: request.product_ids&#10;        })&#10;        with metrics.timer(&quot;bulk_add_items_duration&quot;):&#10;            try:&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;                for product_id in request.product_ids:&#10;                    wishlist.add_item(product_id)&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;                metrics.counter(&quot;bulk_items_added&quot;).inc(len(request.product_ids))&#10;                return updated_wishlist&#10;            except Exception as e:&#10;                metrics.counter(&quot;bulk_add_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to bulk add items: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def bulk_remove_items_from_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: BulkRemoveItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Bulk remove items from a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The bulk remove request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If no items were found to remove and ignore_missing is False&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Bulk removing items from wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_ids&quot;: request.product_ids&#10;        })&#10;        with metrics.timer(&quot;bulk_remove_items_duration&quot;):&#10;            try:&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;                for product_id in request.product_ids:&#10;                    wishlist.remove_item(product_id)&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;                metrics.counter(&quot;bulk_items_removed&quot;).inc(len(request.product_ids))&#10;                return updated_wishlist&#10;            except Exception as e:&#10;                metrics.counter(&quot;bulk_remove_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to bulk remove items: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def reorder_items_in_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: MoveItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Reorder items in a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The move items request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If items don't exist in wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Reordering items in wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;new_order&quot;: request.new_order&#10;        })&#10;        with metrics.timer(&quot;reorder_items_duration&quot;):&#10;            try:&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;                # Reorder items based on new_order list&#10;                product_id_to_item = {item.product_id: item for item in wishlist.items}&#10;                wishlist.items = [product_id_to_item[pid] for pid in request.new_order if pid in product_id_to_item]&#10;                wishlist.updated_at = datetime.utcnow()&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;                metrics.counter(&quot;items_reordered&quot;).inc()&#10;                return updated_wishlist&#10;            except Exception as e:&#10;                metrics.counter(&quot;reorder_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to reorder items: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def get_shared_wishlist(&#10;        self,&#10;        share_hash: str,&#10;        country: str = &quot;ae&quot;,&#10;        language: str = &quot;en&quot;&#10;    ) -&gt; Optional[Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Get a shared wishlist by its share hash.&#10;&#10;        Args:&#10;            share_hash: The share hash of the wishlist&#10;            country: Country code for product localization&#10;            language: Language code for product localization&#10;&#10;        Returns:&#10;            The shared wishlist if found and public, None otherwise&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        cache_key = f&quot;shared_wishlist:{share_hash}:{country}:{language}&quot;&#10;&#10;        logger.info(f&quot;Getting shared wishlist {share_hash}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;share_hash&quot;: share_hash&#10;        })&#10;&#10;        # Try cache first&#10;        try:&#10;            cached_result = await self.cache_repo.get(cache_key)&#10;            if cached_result:&#10;                metrics.counter(&quot;cache_hit&quot;, {&quot;type&quot;: &quot;shared_wishlist&quot;}).inc()&#10;                return cached_result&#10;        except Exception as e:&#10;            logger.warning(f&quot;Cache lookup failed: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id&#10;            })&#10;&#10;        try:&#10;            # Get from database using share hash&#10;            wishlist = await self.wishlist_repo.get_by_share_hash(share_hash)&#10;            if not wishlist or not wishlist.is_public:&#10;                logger.info(f&quot;Shared wishlist not found or not public&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;share_hash&quot;: share_hash&#10;                })&#10;                return None&#10;&#10;            # Enrich with product details&#10;            enriched_wishlist = await self._enrich_wishlist_with_products(&#10;                wishlist, country, language&#10;            )&#10;&#10;            # Cache the result (longer TTL for shared wishlists)&#10;            try:&#10;                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=1800)  # 30 minutes&#10;            except Exception as e:&#10;                logger.warning(f&quot;Failed to cache result: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;            metrics.counter(&quot;cache_miss&quot;, {&quot;type&quot;: &quot;shared_wishlist&quot;}).inc()&#10;            return enriched_wishlist&#10;&#10;        except Exception as e:&#10;            metrics.counter(&quot;get_shared_wishlist_error&quot;).inc()&#10;            logger.error(f&quot;Failed to get shared wishlist: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;share_hash&quot;: share_hash,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            raise&#10;&#10;    # Helper methods&#10;&#10;    async def _enrich_wishlists_with_products(&#10;        self,&#10;        wishlists: List[Wishlist],&#10;        country: str,&#10;        language: str&#10;    ) -&gt; List[Wishlist]:&#10;        &quot;&quot;&quot;Enrich multiple wishlists with product data concurrently.&quot;&quot;&quot;&#10;        if not wishlists:&#10;            return wishlists&#10;&#10;        tasks = [&#10;            self._enrich_wishlist_with_products(wishlist, country, language)&#10;            for wishlist in wishlists&#10;        ]&#10;        return await asyncio.gather(*tasks, return_exceptions=False)&#10;&#10;    async def _enrich_wishlist_with_products(&#10;        self,&#10;        wishlist: Wishlist,&#10;        country: str,&#10;        language: str&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Enrich a single wishlist with product data using advanced caching and fallback strategies.&#10;&#10;        Features:&#10;        - Concurrent product fetching&#10;        - Cache warming for frequently accessed products&#10;        - Fallback to cached data on service failure&#10;        - Graceful degradation with partial results&#10;        &quot;&quot;&quot;&#10;        if not wishlist.items:&#10;            return wishlist&#10;&#10;        correlation_id = generate_correlation_id()&#10;        product_ids = [item.product_id for item in wishlist.items]&#10;&#10;        logger.debug(f&quot;Enriching wishlist {wishlist.wishlist_id} with {len(product_ids)} products&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;            &quot;product_count&quot;: len(product_ids)&#10;        })&#10;&#10;        try:&#10;            with metrics.timer(&quot;product_enrichment_duration&quot;):&#10;                # Get product data from Algolia with batching&#10;                products = await self.algolia_service.get_products_by_ids(&#10;                    product_ids, country, language&#10;                )&#10;&#10;                # Enrich items with product data&#10;                enriched_count = 0&#10;                missing_products = []&#10;&#10;                for item in wishlist.items:&#10;                    if item.product_id in products:&#10;                        item.product = products[item.product_id]&#10;                        enriched_count += 1&#10;                    else:&#10;                        missing_products.append(item.product_id)&#10;                        # Try to get cached product data as fallback&#10;                        if self.cache_repo:&#10;                            fallback_product = await self._get_fallback_product_data(&#10;                                item.product_id, country, language&#10;                            )&#10;                            if fallback_product:&#10;                                item.product = fallback_product&#10;                                enriched_count += 1&#10;                                logger.info(f&quot;Used fallback data for product {item.product_id}&quot;)&#10;&#10;                # Log enrichment results&#10;                if missing_products:&#10;                    logger.warning(f&quot;Products not found during enrichment: {missing_products}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id,&#10;                        &quot;missing_count&quot;: len(missing_products)&#10;                    })&#10;&#10;                metrics.counter(&quot;products_enriched&quot;).inc(enriched_count)&#10;                metrics.counter(&quot;products_missing&quot;).inc(len(missing_products))&#10;&#10;                logger.debug(f&quot;Enriched {enriched_count}/{len(product_ids)} products&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;enriched_count&quot;: enriched_count,&#10;                    &quot;total_count&quot;: len(product_ids)&#10;                })&#10;&#10;                # Trigger cache warming for frequently accessed products (fire and forget)&#10;                if enriched_count &gt; 0:&#10;                    asyncio.create_task(&#10;                        self._warm_product_cache(product_ids, country, language)&#10;                    )&#10;&#10;                return wishlist&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to enrich wishlist with products: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            metrics.counter(&quot;product_enrichment_error&quot;).inc()&#10;&#10;            # Try to use cached data as complete fallback&#10;            try:&#10;                cached_products = await self._get_cached_products_batch(product_ids, country, language)&#10;                if cached_products:&#10;                    for item in wishlist.items:&#10;                        if item.product_id in cached_products:&#10;                            item.product = cached_products[item.product_id]&#10;&#10;                    logger.info(f&quot;Used cached fallback data for {len(cached_products)} products&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;                    metrics.counter(&quot;product_enrichment_fallback_success&quot;).inc()&#10;            except Exception as fallback_error:&#10;                logger.error(f&quot;Fallback enrichment also failed: {fallback_error}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;                metrics.counter(&quot;product_enrichment_fallback_error&quot;).inc()&#10;&#10;            # Return wishlist without product enrichment rather than failing&#10;            return wishlist&#10;&#10;    async def _unset_default_wishlist(self, user_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Unset any existing default wishlist for a user.&quot;&quot;&quot;&#10;        try:&#10;            current_default = await self.wishlist_repo.get_default_wishlist(user_id)&#10;            if current_default:&#10;                current_default.is_default = False&#10;                current_default.updated_at = datetime.utcnow()&#10;                await self.wishlist_repo.update(current_default)&#10;                logger.info(f&quot;Unset default wishlist {current_default.wishlist_id} for user {user_id}&quot;)&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to unset default wishlist: {e}&quot;)&#10;            # Don't raise - this is not critical for the main operation&#10;&#10;    async def _invalidate_user_cache(self, user_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Invalidate all cache entries for a user using intelligent cache management.&quot;&quot;&quot;&#10;        try:&#10;            if self.cache_management_service:&#10;                # Use intelligent cache invalidation&#10;                await self.cache_management_service.invalidate_cache_intelligently(&#10;                    trigger_event=&quot;user:cache:invalidate&quot;,&#10;                    context={&quot;user_id&quot;: user_id}&#10;                )&#10;            else:&#10;                # Fallback to direct pattern deletion&#10;                patterns = [&#10;                    f&quot;user_wishlists:{user_id}:*&quot;,&#10;                    f&quot;wishlist:{user_id}:*&quot;&#10;                ]&#10;                for pattern in patterns:&#10;                    await self.cache_repo.delete_pattern(pattern)&#10;&#10;            logger.debug(f&quot;Invalidated cache for user {user_id}&quot;)&#10;        except Exception as e:&#10;            logger.warning(f&quot;Failed to invalidate user cache: {e}&quot;)&#10;&#10;    async def _invalidate_wishlist_cache(self, user_id: str, wishlist_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Invalidate cache entries for a specific wishlist using intelligent cache management.&quot;&quot;&quot;&#10;        try:&#10;            if self.cache_management_service:&#10;                # Use intelligent cache invalidation&#10;                await self.cache_management_service.invalidate_cache_intelligently(&#10;                    trigger_event=&quot;wishlist:updated&quot;,&#10;                    context={&#10;                        &quot;user_id&quot;: user_id,&#10;                        &quot;wishlist_id&quot;: wishlist_id&#10;                    }&#10;                )&#10;            else:&#10;                # Fallback to direct pattern deletion&#10;                patterns = [&#10;                    f&quot;wishlist:{user_id}:{wishlist_id}:*&quot;,&#10;                    f&quot;user_wishlists:{user_id}:*&quot;&#10;                ]&#10;                for pattern in patterns:&#10;                    await self.cache_repo.delete_pattern(pattern)&#10;&#10;            logger.debug(f&quot;Invalidated cache for wishlist {wishlist_id}&quot;)&#10;        except Exception as e:&#10;            logger.warning(f&quot;Failed to invalidate wishlist cache: {e}&quot;)&#10;&#10;    # Background notification methods (fire and forget)&#10;&#10;    async def _dispatch_wishlist_created_notification(self, wishlist: Wishlist) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for wishlist creation.&quot;&quot;&quot;&#10;        try:&#10;            # This would typically queue a Celery task&#10;            # For now, we'll just log the event&#10;            logger.info(f&quot;Wishlist created notification&quot;, extra={&#10;                &quot;event&quot;: &quot;wishlist_created&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;wishlist_name&quot;: wishlist.name&#10;            })&#10;&#10;            # Trigger CloudFront cache invalidation if needed&#10;            if wishlist.is_public:&#10;                await self.cloudfront_service.invalidate_cache([&#10;                    f&quot;/api/v1/wishlists/shared/{wishlist.share_hash}&quot;&#10;                ])&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch wishlist created notification: {e}&quot;)&#10;&#10;    async def _dispatch_wishlist_deleted_notification(self, wishlist: Wishlist) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for wishlist deletion.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Wishlist deleted notification&quot;, extra={&#10;                &quot;event&quot;: &quot;wishlist_deleted&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id&#10;            })&#10;&#10;            # Trigger CloudFront cache invalidation&#10;            if wishlist.is_public:&#10;                await self.cloudfront_service.invalidate_cache([&#10;                    f&quot;/api/v1/wishlists/shared/{wishlist.share_hash}&quot;&#10;                ])&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch wishlist deleted notification: {e}&quot;)&#10;&#10;    async def _dispatch_item_added_notification(self, wishlist: Wishlist, item: WishlistItem) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item addition.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Item added notification&quot;, extra={&#10;                &quot;event&quot;: &quot;item_added&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;product_id&quot;: item.product_id&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch item added notification: {e}&quot;)&#10;&#10;    async def _dispatch_item_removed_notification(self, wishlist: Wishlist, product_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item removal.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Item removed notification&quot;, extra={&#10;                &quot;event&quot;: &quot;item_removed&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;product_id&quot;: product_id&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch item removed notification: {e}&quot;)&#10;&#10;    # Bulk Operations (Subtask 6.2)&#10;&#10;    async def bulk_add_items_to_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: BulkAddItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Add multiple items to a wishlist in a single operation.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The bulk add request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Bulk adding {len(request.items)} items to wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;item_count&quot;: len(request.items)&#10;        })&#10;&#10;        with metrics.timer(&quot;bulk_add_items_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Verify products exist in Algolia (batch operation)&#10;                product_ids = [item.product_id for item in request.items]&#10;                try:&#10;                    existing_products = await self.algolia_service.batch_check_products_exist(product_ids)&#10;                    missing_products = [pid for pid in product_ids if not existing_products.get(pid, False)]&#10;                    if missing_products:&#10;                        logger.warning(f&quot;Some products not found in Algolia: {missing_products}&quot;, extra={&#10;                            &quot;correlation_id&quot;: correlation_id,&#10;                            &quot;missing_products&quot;: missing_products&#10;                        })&#10;                except Exception as e:&#10;                    logger.warning(f&quot;Failed to verify product existence: {e}&quot;, extra={&#10;                        &quot;correlation_id&quot;: correlation_id&#10;                    })&#10;&#10;                # Add items using domain model methods&#10;                added_items = []&#10;                skipped_items = []&#10;&#10;                for item_data in request.items:&#10;                    # Check if item already exists and skip_duplicates is True&#10;                    if request.skip_duplicates and wishlist.has_item(item_data.product_id):&#10;                        skipped_items.append(item_data.product_id)&#10;                        continue&#10;&#10;                    added_item = wishlist.add_item(item_data.product_id, item_data.notes)&#10;                    added_items.append(added_item)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_bulk_items_added_notification(&#10;                        updated_wishlist, added_items, skipped_items&#10;                    )&#10;                )&#10;&#10;                metrics.counter(&quot;bulk_items_added&quot;).inc()&#10;                metrics.counter(&quot;items_added_total&quot;).inc(len(added_items))&#10;                logger.info(f&quot;Successfully bulk added items to wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;added_count&quot;: len(added_items),&#10;                    &quot;skipped_count&quot;: len(skipped_items),&#10;                    &quot;total_items&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except WishlistNotFoundError:&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;bulk_add_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to bulk add items to wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def bulk_remove_items_from_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        request: BulkRemoveItemsRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Remove multiple items from a wishlist in a single operation.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            request: The bulk remove request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If no items were found to remove and ignore_missing is False&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Bulk removing {len(request.product_ids)} items from wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_ids&quot;: request.product_ids&#10;        })&#10;&#10;        with metrics.timer(&quot;bulk_remove_items_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Remove items using domain model methods&#10;                removed_items = []&#10;                missing_items = []&#10;&#10;                for product_id in request.product_ids:&#10;                    removed = wishlist.remove_item(product_id)&#10;                    if removed:&#10;                        removed_items.append(product_id)&#10;                    else:&#10;                        missing_items.append(product_id)&#10;&#10;                # Check if any items were removed&#10;                if not removed_items and not request.ignore_missing:&#10;                    raise ValidationError(&quot;No items found to remove from wishlist&quot;)&#10;&#10;                # Save to database if any items were removed&#10;                if removed_items:&#10;                    updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                    # Invalidate cache&#10;                    await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                    # Dispatch background notification&#10;                    asyncio.create_task(&#10;                        self._dispatch_bulk_items_removed_notification(&#10;                            updated_wishlist, removed_items, missing_items&#10;                        )&#10;                    )&#10;                else:&#10;                    updated_wishlist = wishlist&#10;&#10;                metrics.counter(&quot;bulk_items_removed&quot;).inc()&#10;                metrics.counter(&quot;items_removed_total&quot;).inc(len(removed_items))&#10;                logger.info(f&quot;Successfully bulk removed items from wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;removed_count&quot;: len(removed_items),&#10;                    &quot;missing_count&quot;: len(missing_items),&#10;                    &quot;total_items&quot;: len(updated_wishlist.items)&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;bulk_remove_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to bulk remove items from wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def move_items_between_wishlists(&#10;        self,&#10;        user_id: str,&#10;        request: MoveItemsRequest&#10;    ) -&gt; Dict[str, Wishlist]:&#10;        &quot;&quot;&quot;&#10;        Move or copy items between wishlists.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            request: The move items request data&#10;&#10;        Returns:&#10;            Dictionary with 'source' and 'target' wishlists&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If either wishlist doesn't exist&#10;            ValidationError: If items don't exist in source wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Moving {len(request.product_ids)} items between wishlists&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;source_wishlist_id&quot;: request.source_wishlist_id,&#10;            &quot;target_wishlist_id&quot;: request.target_wishlist_id,&#10;            &quot;copy_mode&quot;: request.copy_instead_of_move&#10;        })&#10;&#10;        with metrics.timer(&quot;move_items_duration&quot;):&#10;            try:&#10;                # Get both wishlists&#10;                source_wishlist = await self.wishlist_repo.get_by_id(user_id, request.source_wishlist_id)&#10;                if not source_wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Source wishlist {request.source_wishlist_id} not found&quot;)&#10;&#10;                target_wishlist = await self.wishlist_repo.get_by_id(user_id, request.target_wishlist_id)&#10;                if not target_wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Target wishlist {request.target_wishlist_id} not found&quot;)&#10;&#10;                # Find items to move/copy&#10;                items_to_move = []&#10;                missing_items = []&#10;&#10;                for product_id in request.product_ids:&#10;                    item = source_wishlist.get_item(product_id)&#10;                    if item:&#10;                        items_to_move.append(item)&#10;                    else:&#10;                        missing_items.append(product_id)&#10;&#10;                if not items_to_move:&#10;                    raise ValidationError(&quot;No items found in source wishlist to move&quot;)&#10;&#10;                # Add items to target wishlist&#10;                for item in items_to_move:&#10;                    target_wishlist.add_item(item.product_id, item.notes)&#10;&#10;                # Remove items from source wishlist if moving (not copying)&#10;                if not request.copy_instead_of_move:&#10;                    for item in items_to_move:&#10;                        source_wishlist.remove_item(item.product_id)&#10;&#10;                # Save both wishlists&#10;                updated_source = await self.wishlist_repo.update(source_wishlist)&#10;                updated_target = await self.wishlist_repo.update(target_wishlist)&#10;&#10;                # Invalidate cache for both wishlists&#10;                await self._invalidate_wishlist_cache(user_id, request.source_wishlist_id)&#10;                await self._invalidate_wishlist_cache(user_id, request.target_wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_items_moved_notification(&#10;                        updated_source, updated_target, items_to_move, request.copy_instead_of_move&#10;                    )&#10;                )&#10;&#10;                operation = &quot;copied&quot; if request.copy_instead_of_move else &quot;moved&quot;&#10;                metrics.counter(f&quot;items_{operation}&quot;).inc()&#10;                metrics.counter(f&quot;items_{operation}_total&quot;).inc(len(items_to_move))&#10;&#10;                logger.info(f&quot;Successfully {operation} items between wishlists&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;moved_count&quot;: len(items_to_move),&#10;                    &quot;missing_count&quot;: len(missing_items)&#10;                })&#10;&#10;                return {&#10;                    &quot;source&quot;: updated_source,&#10;                    &quot;target&quot;: updated_target&#10;                }&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;move_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to move items between wishlists: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def update_item_in_wishlist(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        product_id: str,&#10;        request: UpdateItemRequest&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Update an existing item in a wishlist.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            product_id: The ID of the product to update&#10;            request: The update request data&#10;&#10;        Returns:&#10;            The updated wishlist&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If the item doesn't exist in the wishlist&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Updating item {product_id} in wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;product_id&quot;: product_id&#10;        })&#10;&#10;        with metrics.timer(&quot;update_item_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Update item using domain model method&#10;                updated = wishlist.update_item_notes(product_id, request.notes)&#10;                if not updated:&#10;                    raise ValidationError(f&quot;Item {product_id} not found in wishlist&quot;)&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_item_updated_notification(updated_wishlist, product_id)&#10;                )&#10;&#10;                metrics.counter(&quot;item_updated&quot;).inc()&#10;                logger.info(f&quot;Successfully updated item in wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;update_item_error&quot;).inc()&#10;                logger.error(f&quot;Failed to update item in wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;product_id&quot;: product_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    async def reorder_wishlist_items(&#10;        self,&#10;        user_id: str,&#10;        wishlist_id: str,&#10;        ordered_product_ids: List[str]&#10;    ) -&gt; Wishlist:&#10;        &quot;&quot;&quot;&#10;        Reorder items in a wishlist based on provided product ID sequence.&#10;&#10;        Args:&#10;            user_id: The ID of the user&#10;            wishlist_id: The ID of the wishlist&#10;            ordered_product_ids: List of product IDs in desired order&#10;&#10;        Returns:&#10;            The updated wishlist with reordered items&#10;&#10;        Raises:&#10;            WishlistNotFoundError: If the wishlist doesn't exist&#10;            ValidationError: If product IDs don't match wishlist items&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Reordering items in wishlist {wishlist_id}&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;user_id&quot;: user_id,&#10;            &quot;wishlist_id&quot;: wishlist_id,&#10;            &quot;item_count&quot;: len(ordered_product_ids)&#10;        })&#10;&#10;        with metrics.timer(&quot;reorder_items_duration&quot;):&#10;            try:&#10;                # Get wishlist&#10;                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)&#10;                if not wishlist:&#10;                    raise WishlistNotFoundError(f&quot;Wishlist {wishlist_id} not found&quot;)&#10;&#10;                # Validate that all product IDs exist in the wishlist&#10;                existing_product_ids = set(wishlist.get_product_ids())&#10;                provided_product_ids = set(ordered_product_ids)&#10;&#10;                if existing_product_ids != provided_product_ids:&#10;                    missing_ids = existing_product_ids - provided_product_ids&#10;                    extra_ids = provided_product_ids - existing_product_ids&#10;                    error_msg = &quot;Product ID mismatch: &quot;&#10;                    if missing_ids:&#10;                        error_msg += f&quot;missing {list(missing_ids)}, &quot;&#10;                    if extra_ids:&#10;                        error_msg += f&quot;extra {list(extra_ids)}&quot;&#10;                    raise ValidationError(error_msg.rstrip(&quot;, &quot;))&#10;&#10;                # Create a mapping of product_id to item&#10;                item_map = {item.product_id: item for item in wishlist.items}&#10;&#10;                # Reorder items based on provided sequence&#10;                reordered_items = [item_map[product_id] for product_id in ordered_product_ids]&#10;                wishlist.items = reordered_items&#10;                wishlist.updated_at = datetime.utcnow()&#10;&#10;                # Save to database&#10;                updated_wishlist = await self.wishlist_repo.update(wishlist)&#10;&#10;                # Invalidate cache&#10;                await self._invalidate_wishlist_cache(user_id, wishlist_id)&#10;&#10;                # Dispatch background notification&#10;                asyncio.create_task(&#10;                    self._dispatch_items_reordered_notification(updated_wishlist)&#10;                )&#10;&#10;                metrics.counter(&quot;items_reordered&quot;).inc()&#10;                logger.info(f&quot;Successfully reordered items in wishlist&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id&#10;                })&#10;&#10;                return updated_wishlist&#10;&#10;            except (WishlistNotFoundError, ValidationError):&#10;                raise&#10;            except Exception as e:&#10;                metrics.counter(&quot;reorder_items_error&quot;).inc()&#10;                logger.error(f&quot;Failed to reorder items in wishlist: {e}&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;user_id&quot;: user_id,&#10;                    &quot;wishlist_id&quot;: wishlist_id,&#10;                    &quot;error&quot;: str(e)&#10;                })&#10;                raise&#10;&#10;    # Additional background notification methods&#10;&#10;    async def _dispatch_bulk_items_added_notification(&#10;        self,&#10;        wishlist: Wishlist,&#10;        added_items: List[WishlistItem],&#10;        skipped_items: List[str]&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for bulk item addition.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Bulk items added notification&quot;, extra={&#10;                &quot;event&quot;: &quot;bulk_items_added&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;added_count&quot;: len(added_items),&#10;                &quot;skipped_count&quot;: len(skipped_items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch bulk items added notification: {e}&quot;)&#10;&#10;    async def _dispatch_bulk_items_removed_notification(&#10;        self,&#10;        wishlist: Wishlist,&#10;        removed_items: List[str],&#10;        missing_items: List[str]&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for bulk item removal.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Bulk items removed notification&quot;, extra={&#10;                &quot;event&quot;: &quot;bulk_items_removed&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;removed_count&quot;: len(removed_items),&#10;                &quot;missing_count&quot;: len(missing_items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch bulk items removed notification: {e}&quot;)&#10;&#10;    async def _dispatch_items_moved_notification(&#10;        self,&#10;        source_wishlist: Wishlist,&#10;        target_wishlist: Wishlist,&#10;        moved_items: List[WishlistItem],&#10;        is_copy: bool&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item movement.&quot;&quot;&quot;&#10;        try:&#10;            operation = &quot;copied&quot; if is_copy else &quot;moved&quot;&#10;            logger.info(f&quot;Items {operation} notification&quot;, extra={&#10;                &quot;event&quot;: f&quot;items_{operation}&quot;,&#10;                &quot;user_id&quot;: source_wishlist.user_id,&#10;                &quot;source_wishlist_id&quot;: source_wishlist.wishlist_id,&#10;                &quot;target_wishlist_id&quot;: target_wishlist.wishlist_id,&#10;                &quot;item_count&quot;: len(moved_items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch items moved notification: {e}&quot;)&#10;&#10;    async def _dispatch_item_updated_notification(self, wishlist: Wishlist, product_id: str) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item update.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Item updated notification&quot;, extra={&#10;                &quot;event&quot;: &quot;item_updated&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;product_id&quot;: product_id&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch item updated notification: {e}&quot;)&#10;&#10;    async def _dispatch_items_reordered_notification(self, wishlist: Wishlist) -&gt; None:&#10;        &quot;&quot;&quot;Dispatch background notification for item reordering.&quot;&quot;&quot;&#10;        try:&#10;            logger.info(f&quot;Items reordered notification&quot;, extra={&#10;                &quot;event&quot;: &quot;items_reordered&quot;,&#10;                &quot;user_id&quot;: wishlist.user_id,&#10;                &quot;wishlist_id&quot;: wishlist.wishlist_id,&#10;                &quot;item_count&quot;: len(wishlist.items)&#10;            })&#10;        except Exception as e:&#10;            logger.error(f&quot;Failed to dispatch items reordered notification: {e}&quot;)&#10;&#10;    # Product Enrichment Helper Methods (Subtask 6.3)&#10;&#10;    async def _get_fallback_product_data(&#10;        self,&#10;        product_id: str,&#10;        country: str,&#10;        language: str&#10;    ) -&gt; Optional[Dict[str, Any]]:&#10;        &quot;&quot;&quot;&#10;        Get fallback product data from cache with multiple fallback strategies.&#10;        &quot;&quot;&quot;&#10;        if not self.cache_repo:&#10;            return None&#10;&#10;        try:&#10;            # Try exact cache key first&#10;            cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;            cached_data = await self.cache_repo.get(cache_key)&#10;            if cached_data:&#10;                return cached_data&#10;&#10;            # Try fallback to English if not already English&#10;            if language != 'en':&#10;                fallback_key = f&quot;algolia:product:{product_id}:{country}:en&quot;&#10;                cached_data = await self.cache_repo.get(fallback_key)&#10;                if cached_data:&#10;                    logger.debug(f&quot;Using English fallback for product {product_id}&quot;)&#10;                    return cached_data&#10;&#10;            # Try fallback to UAE if different country&#10;            if country != 'ae':&#10;                fallback_key = f&quot;algolia:product:{product_id}:ae:{language}&quot;&#10;                cached_data = await self.cache_repo.get(fallback_key)&#10;                if cached_data:&#10;                    logger.debug(f&quot;Using UAE fallback for product {product_id}&quot;)&#10;                    return cached_data&#10;&#10;            # Try UAE + English as final fallback&#10;            if country != 'ae' or language != 'en':&#10;                fallback_key = f&quot;algolia:product:{product_id}:ae:en&quot;&#10;                cached_data = await self.cache_repo.get(fallback_key)&#10;                if cached_data:&#10;                    logger.debug(f&quot;Using UAE+English fallback for product {product_id}&quot;)&#10;                    return cached_data&#10;&#10;            return None&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting fallback product data for {product_id}: {e}&quot;)&#10;            return None&#10;&#10;    async def _get_cached_products_batch(&#10;        self,&#10;        product_ids: List[str],&#10;        country: str,&#10;        language: str&#10;    ) -&gt; Dict[str, Dict[str, Any]]:&#10;        &quot;&quot;&quot;&#10;        Get multiple products from cache with fallback strategies.&#10;        &quot;&quot;&quot;&#10;        if not self.cache_repo or not product_ids:&#10;            return {}&#10;&#10;        try:&#10;            cached_products = {}&#10;&#10;            # Create tasks for concurrent cache lookups&#10;            cache_tasks = []&#10;            for product_id in product_ids:&#10;                cache_tasks.append(&#10;                    self._get_fallback_product_data(product_id, country, language)&#10;                )&#10;&#10;            # Execute all cache lookups concurrently&#10;            results = await asyncio.gather(*cache_tasks, return_exceptions=True)&#10;&#10;            # Process results&#10;            for product_id, result in zip(product_ids, results):&#10;                if isinstance(result, dict) and result:&#10;                    cached_products[product_id] = result&#10;                elif isinstance(result, Exception):&#10;                    logger.warning(f&quot;Cache lookup failed for product {product_id}: {result}&quot;)&#10;&#10;            logger.debug(f&quot;Retrieved {len(cached_products)}/{len(product_ids)} products from cache&quot;)&#10;            return cached_products&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting cached products batch: {e}&quot;)&#10;            return {}&#10;&#10;    async def _warm_product_cache(&#10;        self,&#10;        product_ids: List[str],&#10;        country: str,&#10;        language: str&#10;    ) -&gt; None:&#10;        &quot;&quot;&quot;&#10;        Warm the cache for frequently accessed products (background task).&#10;&#10;        This method runs in the background to pre-fetch and cache products&#10;        that are likely to be accessed again soon.&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_repo or not product_ids:&#10;                return&#10;&#10;            # Check which products are not in cache&#10;            uncached_products = []&#10;            for product_id in product_ids:&#10;                cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                if not await self.cache_repo.exists(cache_key):&#10;                    uncached_products.append(product_id)&#10;&#10;            if not uncached_products:&#10;                logger.debug(&quot;All products already cached, no warming needed&quot;)&#10;                return&#10;&#10;            # Limit cache warming to avoid overwhelming the system&#10;            max_warm_products = 20&#10;            if len(uncached_products) &gt; max_warm_products:&#10;                uncached_products = uncached_products[:max_warm_products]&#10;&#10;            logger.info(f&quot;Warming cache for {len(uncached_products)} products&quot;)&#10;&#10;            # Fetch products from Algolia&#10;            warmed_products = await self.algolia_service.get_products_by_ids(&#10;                uncached_products, country, language&#10;            )&#10;&#10;            # Cache the results with extended TTL for warmed cache&#10;            cache_tasks = []&#10;            extended_ttl = self.settings.CACHE_TTL_PRODUCTS * 2  # Double TTL for warmed cache&#10;&#10;            for product_id, product_data in warmed_products.items():&#10;                cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                cache_tasks.append(&#10;                    self.cache_repo.set(cache_key, product_data, ttl=extended_ttl)&#10;                )&#10;&#10;            if cache_tasks:&#10;                await asyncio.gather(*cache_tasks, return_exceptions=True)&#10;                metrics.counter(&quot;cache_warmed_products&quot;).inc(len(cache_tasks))&#10;                logger.info(f&quot;Successfully warmed cache for {len(cache_tasks)} products&quot;)&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error warming product cache: {e}&quot;)&#10;            metrics.counter(&quot;cache_warming_error&quot;).inc()&#10;&#10;    async def prefetch_popular_products(&#10;        self,&#10;        country: str = 'ae',&#10;        language: str = 'en',&#10;        limit: int = 100&#10;    ) -&gt; int:&#10;        &quot;&quot;&quot;&#10;        Prefetch popular products to warm the cache proactively.&#10;&#10;        This method can be called periodically to cache frequently accessed products.&#10;&#10;        Args:&#10;            country: Country code for localization&#10;            language: Language code for localization&#10;            limit: Maximum number of products to prefetch&#10;&#10;        Returns:&#10;            Number of products successfully cached&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Prefetching popular products&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;country&quot;: country,&#10;            &quot;language&quot;: language,&#10;            &quot;limit&quot;: limit&#10;        })&#10;&#10;        try:&#10;            with metrics.timer(&quot;prefetch_popular_products_duration&quot;):&#10;                # Get popular product IDs from recent wishlists&#10;                # This is a simplified implementation - in production you might want to&#10;                # track product popularity metrics or use analytics data&#10;                popular_product_ids = await self._get_popular_product_ids(limit)&#10;&#10;                if not popular_product_ids:&#10;                    logger.info(&quot;No popular products found to prefetch&quot;)&#10;                    return 0&#10;&#10;                # Check which products are not already cached&#10;                uncached_products = []&#10;                for product_id in popular_product_ids:&#10;                    cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                    if not await self.cache_repo.exists(cache_key):&#10;                        uncached_products.append(product_id)&#10;&#10;                if not uncached_products:&#10;                    logger.info(&quot;All popular products already cached&quot;)&#10;                    return 0&#10;&#10;                # Fetch and cache products&#10;                products = await self.algolia_service.get_products_by_ids(&#10;                    uncached_products, country, language&#10;                )&#10;&#10;                # Cache with extended TTL&#10;                cache_tasks = []&#10;                extended_ttl = self.settings.CACHE_TTL_PRODUCTS * 3  # Triple TTL for popular products&#10;&#10;                for product_id, product_data in products.items():&#10;                    cache_key = f&quot;algolia:product:{product_id}:{country}:{language}&quot;&#10;                    cache_tasks.append(&#10;                        self.cache_repo.set(cache_key, product_data, ttl=extended_ttl)&#10;                    )&#10;&#10;                if cache_tasks:&#10;                    await asyncio.gather(*cache_tasks, return_exceptions=True)&#10;&#10;                cached_count = len(cache_tasks)&#10;                metrics.counter(&quot;popular_products_prefetched&quot;).inc(cached_count)&#10;                logger.info(f&quot;Successfully prefetched {cached_count} popular products&quot;, extra={&#10;                    &quot;correlation_id&quot;: correlation_id,&#10;                    &quot;cached_count&quot;: cached_count&#10;                })&#10;&#10;                return cached_count&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error prefetching popular products: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            metrics.counter(&quot;prefetch_popular_products_error&quot;).inc()&#10;            return 0&#10;&#10;    async def _get_popular_product_ids(self, limit: int) -&gt; List[str]:&#10;        &quot;&quot;&quot;&#10;        Get popular product IDs based on wishlist data.&#10;&#10;        This is a simplified implementation that gets products from recent wishlists.&#10;        In production, you might want to use analytics data or maintain popularity metrics.&#10;        &quot;&quot;&quot;&#10;        try:&#10;            # This is a placeholder implementation&#10;            # In a real system, you would:&#10;            # 1. Query analytics data for popular products&#10;            # 2. Use machine learning to predict popular products&#10;            # 3. Maintain popularity counters in Redis&#10;            # 4. Use business intelligence data&#10;&#10;            # For now, we'll return an empty list&#10;            # The actual implementation would depend on your analytics infrastructure&#10;            return []&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting popular product IDs: {e}&quot;)&#10;            return []&#10;&#10;    # Cache Management and Statistics Methods (Subtask 6.4)&#10;&#10;    async def get_cache_statistics(self) -&gt; Dict[str, Any]:&#10;        &quot;&quot;&quot;&#10;        Get comprehensive cache statistics for wishlist operations.&#10;&#10;        Returns:&#10;            Dictionary containing cache performance metrics&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_management_service:&#10;                logger.warning(&quot;Cache management service not available&quot;)&#10;                return {&quot;error&quot;: &quot;Cache management service not available&quot;}&#10;&#10;            # Get cache statistics from cache management service&#10;            stats = await self.cache_management_service.get_cache_statistics()&#10;&#10;            # Add wishlist-specific metrics&#10;            wishlist_stats = {&#10;                &quot;cache_statistics&quot;: stats,&#10;                &quot;service_metrics&quot;: {&#10;                    &quot;total_cache_operations&quot;: metrics.get_counter_value(&quot;cache_operations_total&quot;),&#10;                    &quot;cache_hit_rate&quot;: self._calculate_cache_hit_rate(),&#10;                    &quot;avg_response_time_ms&quot;: metrics.get_histogram_value(&quot;wishlist_operation_duration&quot;),&#10;                    &quot;last_updated&quot;: datetime.utcnow().isoformat()&#10;                }&#10;            }&#10;&#10;            return wishlist_stats&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error getting cache statistics: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def optimize_cache_performance(self) -&gt; Dict[str, Any]:&#10;        &quot;&quot;&quot;&#10;        Analyze and optimize cache performance for wishlist operations.&#10;&#10;        Returns:&#10;            Dictionary with optimization results and recommendations&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_management_service:&#10;                return {&quot;error&quot;: &quot;Cache management service not available&quot;}&#10;&#10;            # Run cache optimization&#10;            optimization_results = await self.cache_management_service.optimize_cache_performance()&#10;&#10;            # Add wishlist-specific optimizations&#10;            wishlist_optimizations = await self._perform_wishlist_cache_optimizations()&#10;            optimization_results[&quot;wishlist_optimizations&quot;] = wishlist_optimizations&#10;&#10;            return optimization_results&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error optimizing cache performance: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def warm_wishlist_caches(&#10;        self,&#10;        strategy: str = &quot;popular&quot;,&#10;        limit: int = 100&#10;    ) -&gt; Dict[str, int]:&#10;        &quot;&quot;&quot;&#10;        Warm wishlist caches using strategic approaches.&#10;&#10;        Args:&#10;            strategy: Warming strategy (&quot;popular&quot;, &quot;recent&quot;, &quot;predictive&quot;)&#10;            limit: Maximum number of items to warm&#10;&#10;        Returns:&#10;            Dictionary with warming results by cache type&#10;        &quot;&quot;&quot;&#10;        correlation_id = generate_correlation_id()&#10;        logger.info(f&quot;Starting wishlist cache warming&quot;, extra={&#10;            &quot;correlation_id&quot;: correlation_id,&#10;            &quot;strategy&quot;: strategy,&#10;            &quot;limit&quot;: limit&#10;        })&#10;&#10;        try:&#10;            warming_results = {}&#10;&#10;            # Warm product cache&#10;            if self.cache_management_service:&#10;                from app.services.cache_management_service import CachePartition&#10;                product_count = await self.cache_management_service.warm_cache_strategically(&#10;                    CachePartition.PRODUCTS, strategy, limit&#10;                )&#10;                warming_results[&quot;products&quot;] = product_count&#10;&#10;                # Warm wishlist cache&#10;                wishlist_count = await self.cache_management_service.warm_cache_strategically(&#10;                    CachePartition.WISHLISTS, strategy, limit&#10;                )&#10;                warming_results[&quot;wishlists&quot;] = wishlist_count&#10;&#10;            # Warm frequently accessed user wishlists&#10;            user_warming_count = await self._warm_user_wishlist_cache(strategy, limit)&#10;            warming_results[&quot;user_wishlists&quot;] = user_warming_count&#10;&#10;            total_warmed = sum(warming_results.values())&#10;            logger.info(f&quot;Cache warming completed&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;total_warmed&quot;: total_warmed,&#10;                &quot;results&quot;: warming_results&#10;            })&#10;&#10;            return warming_results&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error warming wishlist caches: {e}&quot;, extra={&#10;                &quot;correlation_id&quot;: correlation_id,&#10;                &quot;error&quot;: str(e)&#10;            })&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def invalidate_related_caches(&#10;        self,&#10;        event_type: str,&#10;        context: Dict[str, Any]&#10;    ) -&gt; Dict[str, int]:&#10;        &quot;&quot;&quot;&#10;        Intelligently invalidate related caches based on event type.&#10;&#10;        Args:&#10;            event_type: Type of event that triggered invalidation&#10;            context: Context data for the event&#10;&#10;        Returns:&#10;            Dictionary with invalidation results&#10;        &quot;&quot;&quot;&#10;        try:&#10;            if not self.cache_management_service:&#10;                # Fallback to basic invalidation&#10;                return await self._basic_cache_invalidation(event_type, context)&#10;&#10;            # Use intelligent cache invalidation&#10;            return await self.cache_management_service.invalidate_cache_intelligently(&#10;                trigger_event=event_type,&#10;                context=context&#10;            )&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error invalidating related caches: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    # Helper methods for cache management&#10;&#10;    def _calculate_cache_hit_rate(self) -&gt; float:&#10;        &quot;&quot;&quot;Calculate overall cache hit rate for wishlist operations.&quot;&quot;&quot;&#10;        try:&#10;            hit_count = metrics.get_counter_value(&quot;cache_hit_total&quot;) or 0&#10;            miss_count = metrics.get_counter_value(&quot;cache_miss_total&quot;) or 0&#10;            total_requests = hit_count + miss_count&#10;&#10;            if total_requests == 0:&#10;                return 0.0&#10;&#10;            return hit_count / total_requests&#10;        except Exception:&#10;            return 0.0&#10;&#10;    async def _perform_wishlist_cache_optimizations(self) -&gt; Dict[str, Any]:&#10;        &quot;&quot;&quot;Perform wishlist-specific cache optimizations.&quot;&quot;&quot;&#10;        optimizations = {&#10;            &quot;actions_taken&quot;: [],&#10;            &quot;recommendations&quot;: []&#10;        }&#10;&#10;        try:&#10;            # Check cache hit rates and recommend optimizations&#10;            hit_rate = self._calculate_cache_hit_rate()&#10;&#10;            if hit_rate &lt; 0.7:  # Less than 70% hit rate&#10;                optimizations[&quot;recommendations&quot;].append(&#10;                    f&quot;Consider implementing cache warming for frequently accessed wishlists (current hit rate: {hit_rate:.2%})&quot;&#10;                )&#10;&#10;            # Check for cache key patterns that might need optimization&#10;            # This would analyze actual cache usage patterns in production&#10;            optimizations[&quot;recommendations&quot;].append(&#10;                &quot;Consider implementing cache key compression for large wishlist objects&quot;&#10;            )&#10;&#10;            return optimizations&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error performing wishlist cache optimizations: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;&#10;    async def _warm_user_wishlist_cache(self, strategy: str, limit: int) -&gt; int:&#10;        &quot;&quot;&quot;Warm cache for user wishlists based on strategy.&quot;&quot;&quot;&#10;        try:&#10;            # This is a placeholder implementation&#10;            # In production, you would:&#10;            # 1. Identify frequently accessed users&#10;            # 2. Pre-fetch their wishlists&#10;            # 3. Cache the results with appropriate TTL&#10;&#10;            warmed_count = 0&#10;&#10;            if strategy == &quot;popular&quot;:&#10;                # Get popular users (placeholder)&#10;                popular_users = []  # Would come from analytics&#10;&#10;                for user_id in popular_users[:limit]:&#10;                    try:&#10;                        # Pre-fetch and cache user wishlists&#10;                        wishlists = await self.get_user_wishlists(user_id)&#10;                        if wishlists:&#10;                            warmed_count += 1&#10;                    except Exception as e:&#10;                        logger.warning(f&quot;Failed to warm cache for user {user_id}: {e}&quot;)&#10;&#10;            return warmed_count&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error warming user wishlist cache: {e}&quot;)&#10;            return 0&#10;&#10;    async def _basic_cache_invalidation(&#10;        self,&#10;        event_type: str,&#10;        context: Dict[str, Any]&#10;    ) -&gt; Dict[str, int]:&#10;        &quot;&quot;&quot;Basic cache invalidation fallback when cache management service is unavailable.&quot;&quot;&quot;&#10;        results = {}&#10;&#10;        try:&#10;            user_id = context.get(&quot;user_id&quot;)&#10;            wishlist_id = context.get(&quot;wishlist_id&quot;)&#10;&#10;            if event_type.startswith(&quot;wishlist:&quot;) and user_id:&#10;                # Invalidate user and wishlist caches&#10;                user_pattern = f&quot;user_wishlists:{user_id}:*&quot;&#10;                user_count = await self.cache_repo.delete_pattern(user_pattern)&#10;                results[user_pattern] = user_count&#10;&#10;                if wishlist_id:&#10;                    wishlist_pattern = f&quot;wishlist:{user_id}:{wishlist_id}:*&quot;&#10;                    wishlist_count = await self.cache_repo.delete_pattern(wishlist_pattern)&#10;                    results[wishlist_pattern] = wishlist_count&#10;&#10;            return results&#10;&#10;        except Exception as e:&#10;            logger.error(f&quot;Error in basic cache invalidation: {e}&quot;)&#10;            return {&quot;error&quot;: str(e)}&#10;" />
            </PendingDiffInfo>
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>