# Base requirements for all environments
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
aioboto3==12.0.0
redis[hiredis]==5.0.1
httpx==0.25.2
aiohttp==3.9.1
tenacity==8.2.3
celery[redis]==5.3.4
prometheus-client==0.19.0
opentelemetry-api==1.21.0
opentelemetry-sdk==1.21.0
opentelemetry-instrumentation-fastapi==0.42b0
structlog==23.2.0
python-multipart==0.0.6
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4