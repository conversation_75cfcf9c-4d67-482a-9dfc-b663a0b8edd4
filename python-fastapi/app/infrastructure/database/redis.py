"""
Redis client implementation with async support, connection pooling, and clustering.
"""

import asyncio
import json
import logging
from typing import Any, Dict, List, Optional, Union
from contextlib import asynccontextmanager

import redis.asyncio as redis
from redis.asyncio import Redis
from redis.exceptions import RedisError, ConnectionError as RedisConnectionError

from app.config.settings import get_settings
from app.core.exceptions import DatabaseError, ConnectionError

logger = logging.getLogger(__name__)


class RedisClient:
    """Async Redis client with connection pooling and health monitoring."""
    
    def __init__(self, settings=None):
        self.settings = settings or get_settings()
        self._pool = None
        self._redis = None
        self._health_status = True
        self._connection_pool_size = getattr(self.settings, 'REDIS_MAX_CONNECTIONS', 100)
        self._retry_on_timeout = getattr(self.settings, 'REDIS_RETRY_ON_TIMEOUT', True)
    
    async def initialize(self):
        """Initialize Redis connection pool."""
        try:
            # Parse Redis URL
            redis_url = self.settings.REDIS_URL
            
            # Create connection pool
            self._pool = redis.ConnectionPool.from_url(
                redis_url,
                max_connections=self._connection_pool_size,
                retry_on_timeout=self._retry_on_timeout,
                socket_connect_timeout=10,
                socket_timeout=30,
                health_check_interval=30
            )
            
            # Create Redis client
            self._redis = Redis(connection_pool=self._pool)
            
            # Test connection
            await self.health_check()
            logger.info("Redis client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis client: {e}")
            self._health_status = False
            raise ConnectionError(f"Redis initialization failed: {e}")
    
    async def close(self):
        """Close Redis connection pool."""
        if self._redis:
            await self._redis.close()
        if self._pool:
            await self._pool.disconnect()
        logger.info("Redis client closed")
    
    @property
    def redis(self) -> Redis:
        """Get Redis client instance."""
        if not self._redis:
            raise ConnectionError("Redis client not initialized")
        return self._redis
    
    async def health_check(self) -> bool:
        """Check Redis connection health."""
        try:
            if not self._redis:
                return False
            
            # Simple ping operation
            await self._redis.ping()
            self._health_status = True
            return True
            
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            self._health_status = False
            return False
    
    @property
    def is_healthy(self) -> bool:
        """Get current health status."""
        return self._health_status
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        if not self._pool:
            return {"status": "not_initialized"}
        
        return {
            "max_connections": self._connection_pool_size,
            "created_connections": len(self._pool._created_connections),
            "available_connections": len(self._pool._available_connections),
            "in_use_connections": len(self._pool._in_use_connections),
            "healthy": self._health_status
        }


# Global Redis client instance
_redis_client: Optional[RedisClient] = None


async def get_redis_client() -> RedisClient:
    """Get the global Redis client instance."""
    global _redis_client
    
    if _redis_client is None:
        _redis_client = RedisClient()
        await _redis_client.initialize()
    
    return _redis_client


async def init_redis(settings=None) -> RedisClient:
    """Initialize Redis client during application startup."""
    global _redis_client
    
    _redis_client = RedisClient(settings)
    await _redis_client.initialize()
    
    return _redis_client


class RedisOperations:
    """High-level Redis operations with error handling and serialization."""
    
    def __init__(self, client: RedisClient):
        self.client = client
    
    async def get(self, key: str) -> Optional[Any]:
        """Get value from Redis with automatic deserialization."""
        try:
            value = await self.client.redis.get(key)
            if value is None:
                return None
            
            # Try to deserialize JSON
            try:
                return json.loads(value)
            except (json.JSONDecodeError, TypeError):
                # Return as string if not JSON
                return value.decode('utf-8') if isinstance(value, bytes) else value
                
        except RedisError as e:
            logger.error(f"Redis get error for key {key}: {e}")
            raise DatabaseError(f"Failed to get key {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis get for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """Set value in Redis with automatic serialization."""
        try:
            # Serialize value
            if isinstance(value, (dict, list)):
                serialized_value = json.dumps(value)
            elif isinstance(value, (int, float, bool)):
                serialized_value = json.dumps(value)
            else:
                serialized_value = str(value)
            
            # Set with options
            result = await self.client.redis.set(
                key, 
                serialized_value, 
                ex=ttl,
                nx=nx,
                xx=xx
            )
            
            return result is not False
            
        except RedisError as e:
            logger.error(f"Redis set error for key {key}: {e}")
            raise DatabaseError(f"Failed to set key {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis set for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def delete(self, key: str) -> bool:
        """Delete key from Redis."""
        try:
            result = await self.client.redis.delete(key)
            return result > 0
            
        except RedisError as e:
            logger.error(f"Redis delete error for key {key}: {e}")
            raise DatabaseError(f"Failed to delete key {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis delete for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern."""
        try:
            # Get all keys matching pattern
            keys = await self.client.redis.keys(pattern)
            
            if not keys:
                return 0
            
            # Delete all matching keys
            result = await self.client.redis.delete(*keys)
            return result
            
        except RedisError as e:
            logger.error(f"Redis delete pattern error for pattern {pattern}: {e}")
            raise DatabaseError(f"Failed to delete pattern {pattern}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis delete pattern for pattern {pattern}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis."""
        try:
            result = await self.client.redis.exists(key)
            return result > 0
            
        except RedisError as e:
            logger.error(f"Redis exists error for key {key}: {e}")
            raise DatabaseError(f"Failed to check key existence {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis exists for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for key."""
        try:
            result = await self.client.redis.expire(key, ttl)
            return result
            
        except RedisError as e:
            logger.error(f"Redis expire error for key {key}: {e}")
            raise DatabaseError(f"Failed to set expiration for key {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis expire for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key."""
        try:
            result = await self.client.redis.ttl(key)
            return result
            
        except RedisError as e:
            logger.error(f"Redis TTL error for key {key}: {e}")
            raise DatabaseError(f"Failed to get TTL for key {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis TTL for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """Increment key value."""
        try:
            if amount == 1:
                result = await self.client.redis.incr(key)
            else:
                result = await self.client.redis.incrby(key, amount)
            return result
            
        except RedisError as e:
            logger.error(f"Redis increment error for key {key}: {e}")
            raise DatabaseError(f"Failed to increment key {key}: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis increment for key {key}: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def mget(self, keys: List[str]) -> List[Optional[Any]]:
        """Get multiple values from Redis."""
        try:
            values = await self.client.redis.mget(keys)
            
            # Deserialize each value
            results = []
            for value in values:
                if value is None:
                    results.append(None)
                else:
                    try:
                        results.append(json.loads(value))
                    except (json.JSONDecodeError, TypeError):
                        results.append(value.decode('utf-8') if isinstance(value, bytes) else value)
            
            return results
            
        except RedisError as e:
            logger.error(f"Redis mget error for keys {keys}: {e}")
            raise DatabaseError(f"Failed to get multiple keys: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis mget: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def mset(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """Set multiple key-value pairs."""
        try:
            # Serialize all values
            serialized_mapping = {}
            for key, value in mapping.items():
                if isinstance(value, (dict, list)):
                    serialized_mapping[key] = json.dumps(value)
                elif isinstance(value, (int, float, bool)):
                    serialized_mapping[key] = json.dumps(value)
                else:
                    serialized_mapping[key] = str(value)
            
            # Set all values
            result = await self.client.redis.mset(serialized_mapping)
            
            # Set TTL for all keys if specified
            if ttl and result:
                await asyncio.gather(*[
                    self.client.redis.expire(key, ttl) 
                    for key in mapping.keys()
                ])
            
            return result
            
        except RedisError as e:
            logger.error(f"Redis mset error: {e}")
            raise DatabaseError(f"Failed to set multiple keys: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis mset: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def pipeline_execute(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """Execute multiple operations in a pipeline."""
        try:
            pipe = self.client.redis.pipeline()
            
            # Add operations to pipeline
            for op in operations:
                method = getattr(pipe, op['method'])
                args = op.get('args', [])
                kwargs = op.get('kwargs', {})
                method(*args, **kwargs)
            
            # Execute pipeline
            results = await pipe.execute()
            return results
            
        except RedisError as e:
            logger.error(f"Redis pipeline error: {e}")
            raise DatabaseError(f"Failed to execute pipeline: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in Redis pipeline: {e}")
            raise DatabaseError(f"Unexpected Redis error: {e}")
    
    async def health_check(self) -> bool:
        """Check Redis operations health."""
        try:
            return await self.client.health_check()
        except Exception:
            return False