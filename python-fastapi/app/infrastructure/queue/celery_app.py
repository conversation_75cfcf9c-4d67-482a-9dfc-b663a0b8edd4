"""
Celery application configuration for background task processing.
"""
import logging
from celery import Celery
from kombu import Queue

from app.config.settings import get_settings

logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create Celery app
celery_app = Celery(
    "wishlist_service",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=[
        "app.infrastructure.queue.tasks"
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task routing
    task_routes={
        "app.infrastructure.queue.tasks.invalidate_cloudfront_cache": {"queue": "cache_invalidation"},
        "app.infrastructure.queue.tasks.batch_invalidate_cloudfront_cache": {"queue": "cache_invalidation"},
        "app.infrastructure.queue.tasks.cleanup_expired_cache": {"queue": "maintenance"},
    },
    
    # Queue configuration
    task_default_queue="default",
    task_queues=(
        Queue("default", routing_key="default"),
        Queue("cache_invalidation", routing_key="cache_invalidation"),
        Queue("maintenance", routing_key="maintenance"),
        Queue("notifications", routing_key="notifications"),
    ),
    
    # Task execution settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # Retry settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_default_retry_delay=60,  # 1 minute
    task_max_retries=3,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Beat schedule (for periodic tasks)
    beat_schedule={
        "cleanup-expired-cache": {
            "task": "app.infrastructure.queue.tasks.cleanup_expired_cache",
            "schedule": 300.0,  # Every 5 minutes
            "options": {"queue": "maintenance"}
        },
    },
)

# Configure logging
celery_app.conf.worker_log_format = "[%(asctime)s: %(levelname)s/%(processName)s] %(message)s"
celery_app.conf.worker_task_log_format = "[%(asctime)s: %(levelname)s/%(processName)s][%(task_name)s(%(task_id)s)] %(message)s"

logger.info("Celery app configured", extra={
    "broker": settings.CELERY_BROKER_URL,
    "backend": settings.CELERY_RESULT_BACKEND
})