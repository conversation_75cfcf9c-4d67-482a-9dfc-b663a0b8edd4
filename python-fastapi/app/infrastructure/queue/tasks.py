"""
Celery tasks for background processing.
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone

from celery import Task
from celery.exceptions import Retry

from app.infrastructure.queue.celery_app import celery_app
from app.services.cloudfront_service import get_cloudfront_service, CloudFrontInvalidationRequest
from app.repositories.cache_repo import get_cache_repository
from app.core.metrics import metrics

logger = logging.getLogger(__name__)


class AsyncTask(Task):
    """Base task class for async operations."""

    def run_async(self, coro):
        """Run async coroutine in task."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()


@celery_app.task(
    bind=True,
    base=AsyncTask,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 3, "countdown": 60},
    retry_backoff=True,
    retry_backoff_max=600,
    retry_jitter=True
)
def invalidate_cloudfront_cache(
    self,
    invalidation_type: str,
    identifier: str,
    paths: Optional[List[str]] = None,
    priority: int = 0
) -> Dict[str, Any]:
    """
    Invalidate CloudFront cache for specific paths or identifiers.

    Args:
        invalidation_type: Type of invalidation (wishlist, shared, user, path, paths)
        identifier: The identifier (wishlist_id, user_id, share_hash, or path)
        paths: Optional list of specific paths to invalidate
        priority: Priority for processing (higher = processed first)

    Returns:
        Dictionary with task result information
    """

    async def _invalidate():
        cloudfront_service = await get_cloudfront_service()

        try:
            result = False
            paths_invalidated = []

            if invalidation_type == "wishlist":
                result = await cloudfront_service.invalidate_wishlist(identifier)
                paths_invalidated = [
                    f"/api/v1/wishlists/{identifier}",
                    f"/api/v1/wishlists/{identifier}/*"
                ]

            elif invalidation_type == "shared":
                result = await cloudfront_service.invalidate_shared_wishlist(identifier)
                paths_invalidated = [f"/api/v1/wishlists/shared/{identifier}"]

            elif invalidation_type == "user":
                result = await cloudfront_service.invalidate_user_wishlists(identifier)
                paths_invalidated = [
                    f"/api/v1/wishlists?user_id={identifier}",
                    f"/api/v1/wishlists?user_id={identifier}&*"
                ]

            elif invalidation_type == "path":
                result = await cloudfront_service.invalidate_path(identifier, priority=priority)
                paths_invalidated = [identifier]

            elif invalidation_type == "paths" and paths:
                result = await cloudfront_service.invalidate_paths(paths, priority=priority)
                paths_invalidated = paths

            else:
                raise ValueError(f"Unknown invalidation type: {invalidation_type}")

            if result:
                logger.info("CloudFront invalidation task completed", extra={
                    "task_id": self.request.id,
                    "invalidation_type": invalidation_type,
                    "identifier": identifier,
                    "paths_count": len(paths_invalidated),
                    "attempt": self.request.retries + 1
                })

                metrics.counter("cloudfront_task_success", {
                    "type": invalidation_type
                }).inc()

                return {
                    "status": "success",
                    "invalidation_type": invalidation_type,
                    "identifier": identifier,
                    "paths_invalidated": paths_invalidated,
                    "task_id": self.request.id,
                    "completed_at": datetime.now(timezone.utc).isoformat()
                }
            else:
                raise Exception("CloudFront invalidation failed")

        except Exception as e:
            logger.error("CloudFront invalidation task failed", extra={
                "task_id": self.request.id,
                "invalidation_type": invalidation_type,
                "identifier": identifier,
                "error": str(e),
                "attempt": self.request.retries + 1
            })

            metrics.counter("cloudfront_task_error", {
                "type": invalidation_type
            }).inc()

            # Re-raise to trigger retry
            raise

        finally:
            await cloudfront_service.close()

    try:
        return self.run_async(_invalidate())

    except Exception as e:
        logger.error(f"CloudFront invalidation task error: {e}")

        # If we've exhausted retries, log final failure
        if self.request.retries >= self.max_retries:
            logger.error("CloudFront invalidation task failed after all retries", extra={
                "task_id": self.request.id,
                "invalidation_type": invalidation_type,
                "identifier": identifier,
                "max_retries": self.max_retries
            })

            metrics.counter("cloudfront_task_failed_final", {
                "type": invalidation_type
            }).inc()

            return {
                "status": "failed",
                "invalidation_type": invalidation_type,
                "identifier": identifier,
                "error": str(e),
                "task_id": self.request.id,
                "failed_at": datetime.now(timezone.utc).isoformat()
            }

        # Re-raise for retry
        raise


@celery_app.task(
    bind=True,
    base=AsyncTask,
    autoretry_for=(Exception,),
    retry_kwargs={"max_retries": 2, "countdown": 120},
    retry_backoff=True
)
def batch_invalidate_cloudfront_cache(
    self,
    invalidation_requests: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    Process multiple CloudFront invalidation requests in batch.

    Args:
        invalidation_requests: List of invalidation request dictionaries

    Returns:
        Dictionary with batch processing results
    """

    async def _batch_invalidate():
        cloudfront_service = await get_cloudfront_service()

        try:
            # Convert dictionaries back to CloudFrontInvalidationRequest objects
            requests = [
                CloudFrontInvalidationRequest.from_dict(req_data)
                for req_data in invalidation_requests
            ]

            # Add all requests to batch queue
            for request in requests:
                await cloudfront_service._add_to_batch(request)

            # Force process the batch
            success = await cloudfront_service.flush_pending_invalidations()

            if success:
                logger.info("Batch CloudFront invalidation completed", extra={
                    "task_id": self.request.id,
                    "requests_count": len(requests),
                    "attempt": self.request.retries + 1
                })

                metrics.counter("cloudfront_batch_task_success").inc()

                return {
                    "status": "success",
                    "requests_processed": len(requests),
                    "task_id": self.request.id,
                    "completed_at": datetime.now(timezone.utc).isoformat()
                }
            else:
                raise Exception("Batch CloudFront invalidation failed")

        except Exception as e:
            logger.error("Batch CloudFront invalidation task failed", extra={
                "task_id": self.request.id,
                "requests_count": len(invalidation_requests),
                "error": str(e),
                "attempt": self.request.retries + 1
            })

            metrics.counter("cloudfront_batch_task_error").inc()
            raise

        finally:
            await cloudfront_service.close()

    try:
        return self.run_async(_batch_invalidate())

    except Exception as e:
        logger.error(f"Batch CloudFront invalidation task error: {e}")

        if self.request.retries >= self.max_retries:
            logger.error("Batch CloudFront invalidation task failed after all retries", extra={
                "task_id": self.request.id,
                "requests_count": len(invalidation_requests)
            })

            metrics.counter("cloudfront_batch_task_failed_final").inc()

            return {
                "status": "failed",
                "requests_count": len(invalidation_requests),
                "error": str(e),
                "task_id": self.request.id,
                "failed_at": datetime.now(timezone.utc).isoformat()
            }

        raise


@celery_app.task(bind=True, base=AsyncTask)
def cleanup_expired_cache(self) -> Dict[str, Any]:
    """
    Cleanup expired cache entries (periodic maintenance task).

    Returns:
        Dictionary with cleanup results
    """

    async def _cleanup():
        cache_repo = await get_cache_repository()

        try:
            # This would implement cache cleanup logic
            # For now, just log that the task ran
            logger.info("Cache cleanup task started", extra={
                "task_id": self.request.id
            })

            # TODO: Implement actual cache cleanup logic
            # - Remove expired entries
            # - Cleanup orphaned keys
            # - Collect cache statistics

            cleaned_count = 0  # Placeholder

            logger.info("Cache cleanup task completed", extra={
                "task_id": self.request.id,
                "cleaned_entries": cleaned_count
            })

            metrics.counter("cache_cleanup_success").inc()

            return {
                "status": "success",
                "cleaned_entries": cleaned_count,
                "task_id": self.request.id,
                "completed_at": datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            logger.error("Cache cleanup task failed", extra={
                "task_id": self.request.id,
                "error": str(e)
            })

            metrics.counter("cache_cleanup_error").inc()
            raise

        finally:
            await cache_repo.close()

    try:
        return self.run_async(_cleanup())

    except Exception as e:
        logger.error(f"Cache cleanup task error: {e}")

        return {
            "status": "failed",
            "error": str(e),
            "task_id": self.request.id,
            "failed_at": datetime.now(timezone.utc).isoformat()
        }


# Convenience functions for task dispatching
def queue_cloudfront_invalidation(
    invalidation_type: str,
    identifier: str,
    paths: Optional[List[str]] = None,
    priority: int = 0,
    delay: Optional[int] = None
) -> str:
    """
    Queue a CloudFront invalidation task.

    Args:
        invalidation_type: Type of invalidation
        identifier: The identifier
        paths: Optional specific paths
        priority: Task priority
        delay: Optional delay in seconds

    Returns:
        Task ID
    """
    task_kwargs = {
        "invalidation_type": invalidation_type,
        "identifier": identifier,
        "paths": paths,
        "priority": priority
    }

    if delay:
        task = invalidate_cloudfront_cache.apply_async(
            kwargs=task_kwargs,
            countdown=delay,
            queue="cache_invalidation"
        )
    else:
        task = invalidate_cloudfront_cache.delay(**task_kwargs)

    logger.info("Queued CloudFront invalidation task", extra={
        "task_id": task.id,
        "invalidation_type": invalidation_type,
        "identifier": identifier,
        "delay": delay
    })

    return task.id


def queue_batch_cloudfront_invalidation(
    invalidation_requests: List[Dict[str, Any]],
    delay: Optional[int] = None
) -> str:
    """
    Queue a batch CloudFront invalidation task.

    Args:
        invalidation_requests: List of invalidation request dictionaries
        delay: Optional delay in seconds

    Returns:
        Task ID
    """
    if delay:
        task = batch_invalidate_cloudfront_cache.apply_async(
            args=[invalidation_requests],
            countdown=delay,
            queue="cache_invalidation"
        )
    else:
        task = batch_invalidate_cloudfront_cache.delay(invalidation_requests)

    logger.info("Queued batch CloudFront invalidation task", extra={
        "task_id": task.id,
        "requests_count": len(invalidation_requests),
        "delay": delay
    })

    return task.id
