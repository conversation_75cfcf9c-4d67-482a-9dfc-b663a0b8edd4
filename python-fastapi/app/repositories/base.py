"""
Base repository interface and abstract classes for data access layer.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, TypeVar, Generic
from datetime import datetime

# Generic type for domain models
T = TypeVar('T')


class BaseRepository(ABC, Generic[T]):
    """Abstract base repository interface for CRUD operations."""
    
    @abstractmethod
    async def create(self, entity: T) -> T:
        """Create a new entity."""
        pass
    
    @abstractmethod
    async def get_by_id(self, entity_id: str, **kwargs) -> Optional[T]:
        """Get entity by ID."""
        pass
    
    @abstractmethod
    async def update(self, entity: T) -> T:
        """Update an existing entity."""
        pass
    
    @abstractmethod
    async def delete(self, entity_id: str, **kwargs) -> bool:
        """Delete an entity by ID."""
        pass
    
    @abstractmethod
    async def exists(self, entity_id: str, **kwargs) -> bool:
        """Check if entity exists."""
        pass


class DynamoDBRepository(BaseRepository[T]):
    """Base repository implementation for DynamoDB operations."""
    
    def __init__(self, table_name: str, dynamodb_operations):
        self.table_name = table_name
        self.dynamodb = dynamodb_operations
    
    def _serialize_for_dynamodb(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Convert Python data types to DynamoDB format."""
        serialized = {}
        
        for key, value in data.items():
            if value is None:
                continue  # Skip None values
            elif isinstance(value, str):
                serialized[key] = {'S': value}
            elif isinstance(value, (int, float)):
                serialized[key] = {'N': str(value)}
            elif isinstance(value, bool):
                serialized[key] = {'BOOL': value}
            elif isinstance(value, list):
                if not value:  # Empty list
                    serialized[key] = {'L': []}
                else:
                    # Assume list of dictionaries for complex objects
                    serialized[key] = {'L': [
                        {'M': self._serialize_for_dynamodb(item)} if isinstance(item, dict)
                        else {'S': str(item)}
                        for item in value
                    ]}
            elif isinstance(value, dict):
                serialized[key] = {'M': self._serialize_for_dynamodb(value)}
            elif isinstance(value, datetime):
                serialized[key] = {'S': value.isoformat()}
            else:
                # Fallback to string representation
                serialized[key] = {'S': str(value)}
        
        return serialized
    
    def _deserialize_from_dynamodb(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """Convert DynamoDB format to Python data types."""
        if not item:
            return {}
        
        deserialized = {}
        
        for key, value in item.items():
            if 'S' in value:
                # Try to parse as datetime first
                try:
                    deserialized[key] = datetime.fromisoformat(value['S'])
                except (ValueError, TypeError):
                    deserialized[key] = value['S']
            elif 'N' in value:
                # Try integer first, then float
                try:
                    deserialized[key] = int(value['N'])
                except ValueError:
                    deserialized[key] = float(value['N'])
            elif 'BOOL' in value:
                deserialized[key] = value['BOOL']
            elif 'L' in value:
                deserialized[key] = [
                    self._deserialize_from_dynamodb({'item': item_value})['item']
                    if 'M' in item_value
                    else item_value.get('S', item_value.get('N', item_value))
                    for item_value in value['L']
                ]
            elif 'M' in value:
                deserialized[key] = self._deserialize_from_dynamodb(value['M'])
            elif 'NULL' in value:
                deserialized[key] = None
            else:
                # Fallback
                deserialized[key] = value
        
        return deserialized
    
    def _build_key(self, hash_key: str, range_key: Optional[str] = None) -> Dict[str, Any]:
        """Build DynamoDB key structure."""
        key = {'user_id': {'S': hash_key}}
        
        if range_key:
            key['wishlist_id'] = {'S': range_key}
        
        return key
    
    async def health_check(self) -> bool:
        """Check repository health by testing database connection."""
        try:
            return await self.dynamodb.client.health_check()
        except Exception:
            return False


class CacheRepository(ABC):
    """Abstract base repository interface for caching operations."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """Set value in cache with optional TTL."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> bool:
        """Delete value from cache."""
        pass
    
    @abstractmethod
    async def delete_pattern(self, pattern: str) -> int:
        """Delete all keys matching pattern."""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists in cache."""
        pass
    
    @abstractmethod
    async def expire(self, key: str, ttl: int) -> bool:
        """Set expiration time for key."""
        pass
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check cache health."""
        pass


class RepositoryError(Exception):
    """Base exception for repository operations."""
    pass


class EntityNotFoundError(RepositoryError):
    """Raised when an entity is not found."""
    pass


class EntityAlreadyExistsError(RepositoryError):
    """Raised when trying to create an entity that already exists."""
    pass


class ValidationError(RepositoryError):
    """Raised when entity validation fails."""
    pass