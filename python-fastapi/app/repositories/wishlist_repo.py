"""
Wishlist repository implementation for DynamoDB operations.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import uuid4

from app.repositories.base import DynamoDBRepository, EntityNotFoundError, ValidationError
from app.models.domain.wishlist import Wishlist, WishlistItem
from app.models.database.dynamodb import DynamoDBWishlistMapper
from app.infrastructure.database.dynamodb import DynamoDBOperations
from app.config.settings import get_settings

logger = logging.getLogger(__name__)


class WishlistRepository(DynamoDBRepository[Wishlist]):
    """Repository for Wishlist operations with DynamoDB."""
    
    def __init__(self, dynamodb_operations: DynamoDBOperations):
        settings = get_settings()
        table_name = getattr(settings, 'DYNAMODB_WISHLIST_TABLE', 'wishlists')
        super().__init__(table_name, dynamodb_operations)
        self.share_hash_index = 'share_hash-index'
        self.wishlist_id_index = 'wishlist_id-index'
        self.mapper = DynamoDBWishlistMapper()
    
    async def create(self, wishlist: Wishlist) -> Wishlist:
        """Create a new wishlist."""
        try:
            # Serialize wishlist to DynamoDB format using mapper
            item_data = self.mapper.serialize_for_dynamodb(wishlist)
            
            # Use conditional expression to prevent overwrites
            condition_expression = "attribute_not_exists(user_id) AND attribute_not_exists(wishlist_id)"
            
            success = await self.dynamodb.put_item(
                table_name=self.table_name,
                item=item_data,
                condition_expression=condition_expression
            )
            
            if not success:
                raise ValidationError(f"Wishlist with ID {wishlist.wishlist_id} already exists")
            
            logger.info(f"Created wishlist {wishlist.wishlist_id} for user {wishlist.user_id}")
            return wishlist
            
        except Exception as e:
            logger.error(f"Failed to create wishlist: {e}")
            raise
    
    async def get_by_id(self, user_id: str, wishlist_id: str) -> Optional[Wishlist]:
        """Get wishlist by composite key (user_id, wishlist_id)."""
        try:
            key = self._build_key(user_id, wishlist_id)
            
            item = await self.dynamodb.get_item(
                table_name=self.table_name,
                key=key,
                consistent_read=True
            )
            
            if not item:
                return None
            
            return self.mapper.deserialize_from_dynamodb(item)
            
        except Exception as e:
            logger.error(f"Failed to get wishlist {wishlist_id} for user {user_id}: {e}")
            raise
    
    async def get_by_user_id(self, user_id: str) -> List[Wishlist]:
        """Get all wishlists for a user."""
        try:
            items = await self.dynamodb.query(
                table_name=self.table_name,
                key_condition_expression="user_id = :user_id",
                expression_attribute_values={
                    ':user_id': {'S': user_id}
                },
                scan_index_forward=False  # Most recent first
            )
            
            wishlists = self.mapper.batch_deserialize(items)
            
            logger.info(f"Retrieved {len(wishlists)} wishlists for user {user_id}")
            return wishlists
            
        except Exception as e:
            logger.error(f"Failed to get wishlists for user {user_id}: {e}")
            raise
    
    async def get_by_share_hash(self, share_hash: str) -> Optional[Wishlist]:
        """Get wishlist by share hash using GSI."""
        try:
            items = await self.dynamodb.query(
                table_name=self.table_name,
                index_name=self.share_hash_index,
                key_condition_expression="share_hash = :share_hash",
                expression_attribute_values={
                    ':share_hash': {'S': share_hash}
                },
                limit=1
            )
            
            if not items:
                return None
            
            return self.mapper.deserialize_from_dynamodb(items[0])
            
        except Exception as e:
            logger.error(f"Failed to get wishlist by share hash {share_hash}: {e}")
            raise
    
    async def get_default_wishlist(self, user_id: str) -> Optional[Wishlist]:
        """Get the default wishlist for a user."""
        try:
            items = await self.dynamodb.query(
                table_name=self.table_name,
                key_condition_expression="user_id = :user_id",
                filter_expression="is_default = :is_default",
                expression_attribute_values={
                    ':user_id': {'S': user_id},
                    ':is_default': {'BOOL': True}
                },
                limit=1
            )
            
            if not items:
                return None
            
            return self.mapper.deserialize_from_dynamodb(items[0])
            
        except Exception as e:
            logger.error(f"Failed to get default wishlist for user {user_id}: {e}")
            raise
    
    async def update(self, wishlist: Wishlist) -> Wishlist:
        """Update an existing wishlist."""
        try:
            # Use mapper to serialize the entire wishlist and then update
            serialized_data = self.mapper.serialize_for_dynamodb(wishlist)
            key = self._build_key(wishlist.user_id, wishlist.wishlist_id)
            
            # Build update expression for all fields except the key
            update_expression = "SET #name = :name, #is_public = :is_public, #is_default = :is_default, #items = :items, #updated_at = :updated_at, #share_hash = :share_hash"
            
            expression_attribute_names = {
                '#name': 'name',
                '#is_public': 'is_public',
                '#is_default': 'is_default',
                '#items': 'items',
                '#updated_at': 'updated_at',
                '#share_hash': 'share_hash'
            }
            
            expression_attribute_values = {
                ':name': serialized_data['name'],
                ':is_public': serialized_data['is_public'],
                ':is_default': serialized_data['is_default'],
                ':items': serialized_data['items'],
                ':updated_at': serialized_data['updated_at'],
                ':share_hash': serialized_data['share_hash']
            }
            
            # Ensure the wishlist exists before updating
            condition_expression = "attribute_exists(user_id) AND attribute_exists(wishlist_id)"
            
            updated_item = await self.dynamodb.update_item(
                table_name=self.table_name,
                key=key,
                update_expression=update_expression,
                expression_attribute_names=expression_attribute_names,
                expression_attribute_values=expression_attribute_values,
                condition_expression=condition_expression
            )
            
            if not updated_item:
                raise EntityNotFoundError(f"Wishlist {wishlist.wishlist_id} not found for user {wishlist.user_id}")
            
            logger.info(f"Updated wishlist {wishlist.wishlist_id} for user {wishlist.user_id}")
            return self.mapper.deserialize_from_dynamodb(updated_item)
            
        except Exception as e:
            logger.error(f"Failed to update wishlist {wishlist.wishlist_id}: {e}")
            raise
    
    async def delete(self, user_id: str, wishlist_id: str) -> bool:
        """Delete a wishlist."""
        try:
            key = self._build_key(user_id, wishlist_id)
            
            # Ensure the wishlist exists before deleting
            condition_expression = "attribute_exists(user_id) AND attribute_exists(wishlist_id)"
            
            success = await self.dynamodb.delete_item(
                table_name=self.table_name,
                key=key,
                condition_expression=condition_expression
            )
            
            if success:
                logger.info(f"Deleted wishlist {wishlist_id} for user {user_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete wishlist {wishlist_id} for user {user_id}: {e}")
            raise
    
    async def exists(self, user_id: str, wishlist_id: str) -> bool:
        """Check if a wishlist exists."""
        try:
            wishlist = await self.get_by_id(user_id, wishlist_id)
            return wishlist is not None
            
        except Exception as e:
            logger.error(f"Failed to check if wishlist exists: {e}")
            return False
    
    async def unset_default_wishlist(self, user_id: str, exclude_wishlist_id: Optional[str] = None) -> bool:
        """Unset default flag for all user's wishlists except the excluded one."""
        try:
            # Get all user wishlists
            wishlists = await self.get_by_user_id(user_id)
            
            # Update each default wishlist (except the excluded one)
            updated_count = 0
            for wishlist in wishlists:
                if (wishlist.is_default and 
                    wishlist.wishlist_id != exclude_wishlist_id):
                    
                    wishlist.is_default = False
                    wishlist.updated_at = datetime.utcnow()
                    await self.update(wishlist)
                    updated_count += 1
            
            logger.info(f"Unset default flag for {updated_count} wishlists for user {user_id}")
            return updated_count > 0
            
        except Exception as e:
            logger.error(f"Failed to unset default wishlists for user {user_id}: {e}")
            raise
    
