"""
Cache repository implementation using Redis with consistent key management.
"""

import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timedelta

from app.repositories.base import CacheRepository
from app.infrastructure.database.redis import RedisOperations
from app.config.settings import get_settings

logger = logging.getLogger(__name__)


class RedisCacheRepository(CacheRepository):
    """Redis-based cache repository with consistent key management."""
    
    def __init__(self, redis_operations: RedisOperations):
        self.redis = redis_operations
        self.settings = get_settings()
        self.key_prefix = "wishlist_service"
        self.default_ttl = getattr(self.settings, 'CACHE_TTL_DEFAULT', 300)  # 5 minutes
        self.product_ttl = getattr(self.settings, 'CACHE_TTL_PRODUCTS', 3600)  # 1 hour
    
    def _build_key(self, key: str, namespace: Optional[str] = None) -> str:
        """Build consistent cache key with prefix and namespace."""
        parts = [self.key_prefix]
        
        if namespace:
            parts.append(namespace)
        
        parts.append(key)
        
        return ":".join(parts)
    
    async def get(self, key: str, namespace: Optional[str] = None) -> Optional[Any]:
        """Get value from cache."""
        try:
            cache_key = self._build_key(key, namespace)
            value = await self.redis.get(cache_key)
            
            if value is not None:
                logger.debug(f"Cache hit for key: {cache_key}")
            else:
                logger.debug(f"Cache miss for key: {cache_key}")
            
            return value
            
        except Exception as e:
            logger.error(f"Failed to get cache key {key}: {e}")
            return None  # Graceful degradation
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: Optional[int] = None,
        namespace: Optional[str] = None
    ) -> bool:
        """Set value in cache with optional TTL."""
        try:
            cache_key = self._build_key(key, namespace)
            effective_ttl = ttl or self.default_ttl
            
            success = await self.redis.set(cache_key, value, ttl=effective_ttl)
            
            if success:
                logger.debug(f"Cache set for key: {cache_key} (TTL: {effective_ttl}s)")
            else:
                logger.warning(f"Failed to set cache key: {cache_key}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to set cache key {key}: {e}")
            return False  # Graceful degradation
    
    async def delete(self, key: str, namespace: Optional[str] = None) -> bool:
        """Delete value from cache."""
        try:
            cache_key = self._build_key(key, namespace)
            success = await self.redis.delete(cache_key)
            
            if success:
                logger.debug(f"Cache deleted for key: {cache_key}")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to delete cache key {key}: {e}")
            return False
    
    async def delete_pattern(self, pattern: str, namespace: Optional[str] = None) -> int:
        """Delete all keys matching pattern."""
        try:
            cache_pattern = self._build_key(pattern, namespace)
            count = await self.redis.delete_pattern(cache_pattern)
            
            logger.debug(f"Cache pattern deleted: {cache_pattern} (count: {count})")
            return count
            
        except Exception as e:
            logger.error(f"Failed to delete cache pattern {pattern}: {e}")
            return 0
    
    async def exists(self, key: str, namespace: Optional[str] = None) -> bool:
        """Check if key exists in cache."""
        try:
            cache_key = self._build_key(key, namespace)
            return await self.redis.exists(cache_key)
            
        except Exception as e:
            logger.error(f"Failed to check cache key existence {key}: {e}")
            return False
    
    async def expire(self, key: str, ttl: int, namespace: Optional[str] = None) -> bool:
        """Set expiration time for key."""
        try:
            cache_key = self._build_key(key, namespace)
            success = await self.redis.expire(cache_key, ttl)
            
            if success:
                logger.debug(f"Cache expiration set for key: {cache_key} (TTL: {ttl}s)")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to set expiration for cache key {key}: {e}")
            return False
    
    async def get_ttl(self, key: str, namespace: Optional[str] = None) -> int:
        """Get time to live for key."""
        try:
            cache_key = self._build_key(key, namespace)
            return await self.redis.ttl(cache_key)
            
        except Exception as e:
            logger.error(f"Failed to get TTL for cache key {key}: {e}")
            return -1
    
    async def increment(self, key: str, amount: int = 1, namespace: Optional[str] = None) -> int:
        """Increment key value."""
        try:
            cache_key = self._build_key(key, namespace)
            return await self.redis.increment(cache_key, amount)
            
        except Exception as e:
            logger.error(f"Failed to increment cache key {key}: {e}")
            return 0
    
    async def get_multiple(
        self, 
        keys: List[str], 
        namespace: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get multiple values from cache."""
        try:
            cache_keys = [self._build_key(key, namespace) for key in keys]
            values = await self.redis.mget(cache_keys)
            
            # Map original keys to values
            result = {}
            for i, key in enumerate(keys):
                result[key] = values[i] if i < len(values) else None
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get multiple cache keys: {e}")
            return {key: None for key in keys}
    
    async def set_multiple(
        self, 
        mapping: Dict[str, Any], 
        ttl: Optional[int] = None,
        namespace: Optional[str] = None
    ) -> bool:
        """Set multiple key-value pairs."""
        try:
            # Build cache keys
            cache_mapping = {
                self._build_key(key, namespace): value 
                for key, value in mapping.items()
            }
            
            effective_ttl = ttl or self.default_ttl
            success = await self.redis.mset(cache_mapping, ttl=effective_ttl)
            
            if success:
                logger.debug(f"Cache mset for {len(mapping)} keys (TTL: {effective_ttl}s)")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to set multiple cache keys: {e}")
            return False
    
    async def health_check(self) -> bool:
        """Check cache health."""
        try:
            return await self.redis.health_check()
        except Exception:
            return False
    
    # Wishlist-specific cache methods
    
    async def get_user_wishlists(
        self, 
        user_id: str, 
        country: str = "ae", 
        language: str = "en"
    ) -> Optional[Any]:
        """Get cached user wishlists."""
        key = f"user_wishlists:{user_id}:{country}:{language}"
        return await self.get(key, namespace="wishlists")
    
    async def set_user_wishlists(
        self, 
        user_id: str, 
        wishlists: Any, 
        country: str = "ae", 
        language: str = "en",
        ttl: Optional[int] = None
    ) -> bool:
        """Cache user wishlists."""
        key = f"user_wishlists:{user_id}:{country}:{language}"
        return await self.set(key, wishlists, ttl=ttl, namespace="wishlists")
    
    async def get_wishlist(
        self, 
        user_id: str, 
        wishlist_id: str, 
        country: str = "ae", 
        language: str = "en"
    ) -> Optional[Any]:
        """Get cached wishlist."""
        key = f"wishlist:{user_id}:{wishlist_id}:{country}:{language}"
        return await self.get(key, namespace="wishlists")
    
    async def set_wishlist(
        self, 
        user_id: str, 
        wishlist_id: str, 
        wishlist: Any, 
        country: str = "ae", 
        language: str = "en",
        ttl: Optional[int] = None
    ) -> bool:
        """Cache wishlist."""
        key = f"wishlist:{user_id}:{wishlist_id}:{country}:{language}"
        return await self.set(key, wishlist, ttl=ttl, namespace="wishlists")
    
    async def invalidate_user_cache(self, user_id: str) -> int:
        """Invalidate all cache entries for a user."""
        pattern = f"user_wishlists:{user_id}:*"
        return await self.delete_pattern(pattern, namespace="wishlists")
    
    async def invalidate_wishlist_cache(self, user_id: str, wishlist_id: str) -> int:
        """Invalidate cache entries for a specific wishlist."""
        patterns = [
            f"wishlist:{user_id}:{wishlist_id}:*",
            f"user_wishlists:{user_id}:*"
        ]
        
        total_deleted = 0
        for pattern in patterns:
            count = await self.delete_pattern(pattern, namespace="wishlists")
            total_deleted += count
        
        return total_deleted
    
    async def get_product_data(self, product_ids: List[str], country: str, language: str) -> Dict[str, Any]:
        """Get cached product data."""
        keys = [f"product:{product_id}:{country}:{language}" for product_id in product_ids]
        return await self.get_multiple(keys, namespace="products")
    
    async def set_product_data(
        self, 
        product_data: Dict[str, Any], 
        country: str, 
        language: str
    ) -> bool:
        """Cache product data."""
        mapping = {
            f"product:{product_id}:{country}:{language}": data
            for product_id, data in product_data.items()
        }
        return await self.set_multiple(mapping, ttl=self.product_ttl, namespace="products")
    
    async def get_shared_wishlist(self, share_hash: str) -> Optional[Any]:
        """Get cached shared wishlist."""
        key = f"shared:{share_hash}"
        return await self.get(key, namespace="shared")
    
    async def set_shared_wishlist(self, share_hash: str, wishlist: Any, ttl: Optional[int] = None) -> bool:
        """Cache shared wishlist."""
        key = f"shared:{share_hash}"
        return await self.set(key, wishlist, ttl=ttl, namespace="shared")
    
    async def invalidate_shared_wishlist(self, share_hash: str) -> bool:
        """Invalidate shared wishlist cache."""
        key = f"shared:{share_hash}"
        return await self.delete(key, namespace="shared")
    
    # Rate limiting support
    
    async def increment_rate_limit(self, identifier: str, window: int = 60) -> int:
        """Increment rate limit counter."""
        key = f"rate_limit:{identifier}"
        count = await self.increment(key, namespace="rate_limits")
        
        # Set expiration on first increment
        if count == 1:
            await self.expire(key, window, namespace="rate_limits")
        
        return count
    
    async def get_rate_limit_count(self, identifier: str) -> int:
        """Get current rate limit count."""
        key = f"rate_limit:{identifier}"
        value = await self.get(key, namespace="rate_limits")
        return int(value) if value else 0
    
    async def reset_rate_limit(self, identifier: str) -> bool:
        """Reset rate limit counter."""
        key = f"rate_limit:{identifier}"
        return await self.delete(key, namespace="rate_limits")

# Singleton instance for dependency injection
_cache_repository: Optional[CacheRepository] = None


async def get_cache_repository() -> CacheRepository:
    """Get or create cache repository instance."""
    global _cache_repository
    
    if _cache_repository is None:
        from app.infrastructure.database.redis import get_redis_operations
        redis_ops = await get_redis_operations()
        _cache_repository = CacheRepository(redis_ops)
    
    return _cache_repository


async def close_cache_repository():
    """Close cache repository and cleanup resources."""
    global _cache_repository
    
    if _cache_repository:
        await _cache_repository.close()
        _cache_repository = None