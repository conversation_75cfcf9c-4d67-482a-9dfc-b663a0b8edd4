"""
Custom middleware for the FastAPI application.
"""
import time
import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.config.settings import get_settings


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging requests and responses with correlation IDs.
    """
    
    def __init__(self, app):
        super().__init__(app)
        from app.config.logging import get_logger
        self.logger = get_logger("middleware.request")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate correlation ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Extract user information if available
        user_id = request.headers.get("X-User-ID")
        
        # Log request start
        start_time = time.time()
        self.logger.info(
            "Request started",
            method=request.method,
            path=str(request.url.path),
            query_params=str(request.url.query) if request.url.query else None,
            user_agent=request.headers.get("User-Agent"),
            correlation_id=request_id,
            user_id=user_id,
            client_ip=request.client.host if request.client else None,
        )
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Add headers to response
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.3f}"
            
            # Log request completion
            self.logger.info(
                "Request completed",
                method=request.method,
                path=str(request.url.path),
                status_code=response.status_code,
                duration_ms=round(process_time * 1000, 2),
                correlation_id=request_id,
                user_id=user_id,
                response_size=response.headers.get("Content-Length"),
            )
            
            return response
            
        except Exception as exc:
            # Calculate processing time for failed requests
            process_time = time.time() - start_time
            
            # Log request failure
            self.logger.error(
                "Request failed",
                method=request.method,
                path=str(request.url.path),
                duration_ms=round(process_time * 1000, 2),
                correlation_id=request_id,
                user_id=user_id,
                error=str(exc),
                error_type=type(exc).__name__,
            )
            
            # Re-raise the exception
            raise


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware for collecting metrics and performance data.
    """
    
    def __init__(self, app):
        super().__init__(app)
        from app.config.logging import PerformanceLogger
        self.perf_logger = PerformanceLogger()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        response = await call_next(request)
        
        # Collect performance metrics
        duration = time.time() - start_time
        
        # Store metrics in request state
        request.state.duration = duration
        
        # Log performance metrics
        correlation_id = getattr(request.state, "request_id", None)
        
        # Log slow requests (>1 second)
        if duration > 1.0:
            self.perf_logger.logger.warning(
                "Slow request detected",
                method=request.method,
                path=str(request.url.path),
                duration_ms=round(duration * 1000, 2),
                status_code=response.status_code,
                correlation_id=correlation_id,
            )
        
        # TODO: Send metrics to Prometheus/CloudWatch
        # This will be implemented in the monitoring task
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware (placeholder for now).
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        settings = get_settings()
        
        # Skip rate limiting for health checks
        if request.url.path.startswith("/health"):
            return await call_next(request)
        
        # Rate limiting logic will be implemented in security task
        # For now, just pass through
        
        response = await call_next(request)
        
        # Add rate limit headers (placeholder values)
        response.headers["X-RateLimit-Limit"] = str(settings.RATE_LIMIT_REQUESTS)
        response.headers["X-RateLimit-Remaining"] = str(settings.RATE_LIMIT_REQUESTS - 1)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + settings.RATE_LIMIT_WINDOW)
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware for adding security headers.
    """
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Content Security Policy - Allow Swagger UI resources from trusted CDNs
        csp_policy = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net; "
            "style-src 'self' 'unsafe-inline' https://unpkg.com https://cdn.jsdelivr.net; "
            "img-src 'self' data: https:; "
            "font-src 'self' https://unpkg.com https://cdn.jsdelivr.net; "
            "connect-src 'self'"
        )
        response.headers["Content-Security-Policy"] = csp_policy
        
        # Add HSTS header for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response