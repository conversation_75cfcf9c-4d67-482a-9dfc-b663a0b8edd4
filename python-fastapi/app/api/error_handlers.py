"""
Global error handlers for the FastAPI application.

This module provides centralized error handling with consistent response formats,
proper logging, and correlation ID tracking.
"""
import logging
from typing import Union
from datetime import datetime, timezone
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException

from app.schemas.base import ErrorResponseSchema
from app.core.exceptions import (
    WishlistServiceException,
    WishlistNotFoundError,
    ValidationError as CustomValidationError,
    ExternalServiceError
)
from app.utils.helpers import generate_correlation_id

logger = logging.getLogger(__name__)


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle HTTP exceptions with consistent error response format.
    """
    correlation_id = getattr(request.state, "request_id", generate_correlation_id())

    # Log the error
    logger.warning(
        f"HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            "correlation_id": correlation_id,
            "status_code": exc.status_code,
            "path": str(request.url.path),
            "method": request.method,
            "detail": exc.detail
        }
    )

    # If detail is already a dict (from our custom exceptions), use it directly
    if isinstance(exc.detail, dict):
        return JSONResponse(
            status_code=exc.status_code,
            content=exc.detail
        )

    # Otherwise, create a standard error response

    error_response = ErrorResponseSchema(
        code=f"HTTP_{exc.status_code}",
        message=str(exc.detail),
        correlation_id=correlation_id
    )

    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": error_response.model_dump(),
            "request_id": correlation_id,
            "timestamp": error_response.timestamp
        }
    )


async def validation_exception_handler(request: Request, exc: RequestValidationError) -> JSONResponse:
    """
    Handle Pydantic validation errors with detailed field information.
    """
    correlation_id = getattr(request.state, "request_id", generate_correlation_id())

    # Extract validation errors
    validation_errors = []
    for error in exc.errors():
        field_path = " -> ".join(str(loc) for loc in error["loc"])
        validation_errors.append({
            "field": field_path,
            "message": error["msg"],
            "type": error["type"],
            "input": error.get("input")
        })

    # Log validation error
    logger.warning(
        f"Validation error: {len(validation_errors)} field(s) failed validation",
        extra={
            "correlation_id": correlation_id,
            "path": str(request.url.path),
            "method": request.method,
            "validation_errors": validation_errors
        }
    )

    error_response = {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "details": validation_errors,
            "correlation_id": correlation_id
        },
        "request_id": correlation_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    return JSONResponse(
        status_code=422,
        content=error_response
    )


async def wishlist_not_found_handler(request: Request, exc: WishlistNotFoundError) -> JSONResponse:
    """
    Handle wishlist not found errors.
    """
    correlation_id = getattr(request.state, "request_id", generate_correlation_id())

    logger.info(
        f"Wishlist not found: {exc}",
        extra={
            "correlation_id": correlation_id,
            "path": str(request.url.path),
            "method": request.method,
            "error": str(exc)
        }
    )

    error_response = {
        "error": {
            "code": "WISHLIST_NOT_FOUND",
            "message": str(exc),
            "correlation_id": correlation_id
        },
        "request_id": correlation_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    return JSONResponse(
        status_code=404,
        content=error_response
    )


async def custom_validation_error_handler(request: Request, exc: CustomValidationError) -> JSONResponse:
    """
    Handle custom validation errors.
    """
    correlation_id = getattr(request.state, "request_id", generate_correlation_id())

    logger.warning(
        f"Custom validation error: {exc}",
        extra={
            "correlation_id": correlation_id,
            "path": str(request.url.path),
            "method": request.method,
            "error": str(exc)
        }
    )

    error_response = {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": str(exc),
            "correlation_id": correlation_id
        },
        "request_id": correlation_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    return JSONResponse(
        status_code=400,
        content=error_response
    )


async def external_service_error_handler(request: Request, exc: ExternalServiceError) -> JSONResponse:
    """
    Handle external service errors.
    """
    correlation_id = getattr(request.state, "request_id", generate_correlation_id())

    logger.error(
        f"External service error: {exc}",
        extra={
            "correlation_id": correlation_id,
            "path": str(request.url.path),
            "method": request.method,
            "error": str(exc)
        }
    )

    error_response = {
        "error": {
            "code": "EXTERNAL_SERVICE_ERROR",
            "message": "External service temporarily unavailable",
            "correlation_id": correlation_id
        },
        "request_id": correlation_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    return JSONResponse(
        status_code=503,
        content=error_response
    )


async def generic_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle unexpected exceptions with proper logging and safe error responses.
    """
    correlation_id = getattr(request.state, "request_id", generate_correlation_id())

    # Log the full exception details
    logger.error(
        f"Unhandled exception: {type(exc).__name__}: {exc}",
        extra={
            "correlation_id": correlation_id,
            "path": str(request.url.path),
            "method": request.method,
            "error_type": type(exc).__name__,
            "error": str(exc)
        },
        exc_info=True  # Include stack trace
    )

    # Return a safe error response without exposing internal details
    error_response = {
        "error": {
            "code": "INTERNAL_SERVER_ERROR",
            "message": "An unexpected error occurred. Please try again later.",
            "correlation_id": correlation_id
        },
        "request_id": correlation_id,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }

    return JSONResponse(
        status_code=500,
        content=error_response
    )


def register_error_handlers(app):
    """
    Register all error handlers with the FastAPI application.
    """
    # HTTP exceptions
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)

    # Validation errors
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ValidationError, validation_exception_handler)

    # Custom application exceptions
    app.add_exception_handler(WishlistNotFoundError, wishlist_not_found_handler)
    app.add_exception_handler(CustomValidationError, custom_validation_error_handler)
    app.add_exception_handler(ExternalServiceError, external_service_error_handler)

    # Generic exception handler (catch-all)
    app.add_exception_handler(Exception, generic_exception_handler)
