"""
Dependency injection for FastAPI endpoints.

This module provides dependency injection functions for services, authentication,
validation, and other cross-cutting concerns used by API endpoints.
"""
from typing import Dict, Any
from fastapi import Depends, HTTPException, Header, Query
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.services.wishlist_service import WishlistService
from app.services.algolia_service import AlgoliaService
from app.services.cloudfront_service import CloudFrontService
from app.repositories.wishlist_repo import WishlistRepository
from app.repositories.cache_repo import RedisCacheRepository
from app.repositories.base import CacheRepository
from app.infrastructure.database.dynamodb import get_dynamodb_operations
from app.infrastructure.database.redis import get_redis_client, RedisOperations
from app.config.settings import get_settings
from app.core.exceptions import ValidationError
from app.utils.helpers import generate_correlation_id
import logging

logger = logging.getLogger(__name__)

# Security scheme for API key authentication
security = HTTPBearer()

# Supported countries and languages
SUPPORTED_COUNTRIES = {"ae", "sa", "kw", "bh", "om", "qa"}
SUPPORTED_LANGUAGES = {"en", "ar"}


async def get_current_user_id(
    authorization: HTTPAuthorizationCredentials = Depends(security)
) -> str:
    """
    Extract and validate user ID from API key.

    For now, this is a simplified implementation that extracts user ID from the token.
    In production, this would validate the API key against a secure store.
    """
    try:
        # Simplified implementation - in production, validate against secure store
        token = authorization.credentials

        # For demo purposes, assume token format is "user_id:api_key"
        # In production, this would be a proper JWT or API key validation
        if ":" in token:
            user_id = token.split(":")[0]
            if user_id:
                return user_id

        # Fallback for testing - use the token as user_id if it looks like a user ID
        if token and len(token) > 3:
            return token

        raise HTTPException(
            status_code=401,
            detail={
                "code": "INVALID_API_KEY",
                "message": "Invalid API key format",
                "correlation_id": generate_correlation_id()
            }
        )

    except Exception as e:
        logger.error(f"Authentication failed: {e}")
        raise HTTPException(
            status_code=401,
            detail={
                "code": "AUTHENTICATION_FAILED",
                "message": "Authentication failed",
                "correlation_id": generate_correlation_id()
            }
        )


async def validate_country_language(
    country: str = Query("ae"),
    language: str = Query("en")
) -> Dict[str, str]:
    """
    Validate country and language parameters.
    """
    if country not in SUPPORTED_COUNTRIES:
        raise HTTPException(
            status_code=400,
            detail={
                "code": "INVALID_COUNTRY",
                "message": f"Country '{country}' is not supported. Supported countries: {', '.join(SUPPORTED_COUNTRIES)}",
                "correlation_id": generate_correlation_id()
            }
        )

    if language not in SUPPORTED_LANGUAGES:
        raise HTTPException(
            status_code=400,
            detail={
                "code": "INVALID_LANGUAGE",
                "message": f"Language '{language}' is not supported. Supported languages: {', '.join(SUPPORTED_LANGUAGES)}",
                "correlation_id": generate_correlation_id()
            }
        )

    return {"country": country, "language": language}


# Service dependencies

async def get_cache_repository() -> CacheRepository:
    """Get cache repository instance."""
    redis_client = await get_redis_client()
    redis_operations = RedisOperations(redis_client)
    return RedisCacheRepository(redis_operations)


async def get_wishlist_repository() -> WishlistRepository:
    """Get wishlist repository instance."""
    dynamodb_ops = await get_dynamodb_operations()
    return WishlistRepository(dynamodb_ops)


async def get_algolia_service(
    cache_repo: CacheRepository = Depends(get_cache_repository)
) -> AlgoliaService:
    """Get Algolia service instance."""
    return AlgoliaService(cache_repo)


async def get_cloudfront_service() -> CloudFrontService:
    """Get CloudFront service instance."""
    return CloudFrontService()


async def get_wishlist_service(
    wishlist_repo: WishlistRepository = Depends(get_wishlist_repository),
    cache_repo: CacheRepository = Depends(get_cache_repository),
    algolia_service: AlgoliaService = Depends(get_algolia_service),
    cloudfront_service: CloudFrontService = Depends(get_cloudfront_service)
) -> WishlistService:
    """Get wishlist service instance with all dependencies."""
    return WishlistService(
        wishlist_repo=wishlist_repo,
        cache_repo=cache_repo,
        algolia_service=algolia_service,
        cloudfront_service=cloudfront_service
    )
