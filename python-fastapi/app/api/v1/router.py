"""
Main API router for v1 endpoints.
"""
from fastapi import APIRouter

from app.api.v1.endpoints import health, wishlists, items, shared

# Create the main API router
api_router = APIRouter()

# Include endpoint routers
api_router.include_router(
    health.router,
    prefix="/health",
    tags=["health"]
)

api_router.include_router(
    wishlists.router,
    prefix="/wishlists",
    tags=["wishlists"]
)

api_router.include_router(
    items.router,
    prefix="/wishlists",
    tags=["wishlist-items"]
)

api_router.include_router(
    shared.router,
    prefix="/wishlists/shared",
    tags=["shared-wishlists"]
)