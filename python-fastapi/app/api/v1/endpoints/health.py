"""
Health check and monitoring endpoints.
"""
import asyncio
import time
from typing import Dict, Any, List
from datetime import datetime, timezone

from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from app.config.settings import get_settings, Settings
from app.config.logging import get_logger
from app.utils.circuit_breaker import (
    health_check_all_circuit_breakers,
    get_circuit_breaker_stats,
    get_unhealthy_circuit_breakers,
    get_open_circuit_breakers,
    get_half_open_circuit_breakers,
    get_circuit_breaker_summary,
    force_open_circuit_breaker,
    reset_circuit_breaker
)

logger = get_logger(__name__)
router = APIRouter()


class HealthStatus(BaseModel):
    """Health status model."""
    status: str  # "healthy", "degraded", "unhealthy"
    timestamp: datetime
    version: str
    environment: str
    uptime_seconds: float


class DependencyStatus(BaseModel):
    """Dependency health status model."""
    name: str
    status: str  # "healthy", "degraded", "unhealthy"
    response_time_ms: float
    error: str = None
    details: Dict[str, Any] = {}


class DetailedHealthResponse(BaseModel):
    """Detailed health check response."""
    status: str
    timestamp: datetime
    version: str
    environment: str
    uptime_seconds: float
    dependencies: List[DependencyStatus]
    circuit_breakers: Dict[str, Any]
    system_info: Dict[str, Any]


class MetricsResponse(BaseModel):
    """Metrics response model."""
    timestamp: datetime
    metrics: Dict[str, Any]


# Track application start time
_start_time = time.time()


async def check_redis_health() -> DependencyStatus:
    """Check Redis connection health."""
    start_time = time.time()
    
    try:
        from app.infrastructure.database.connection_manager import get_connection_manager
        
        connection_manager = await get_connection_manager()
        stats = await connection_manager.get_connection_stats()
        redis_stats = stats.get('redis', {})
        
        response_time = (time.time() - start_time) * 1000
        
        status = "healthy" if redis_stats.get('is_healthy', False) else "unhealthy"
        
        return DependencyStatus(
            name="redis",
            status=status,
            response_time_ms=round(response_time, 2),
            details={
                "connection_pool_size": redis_stats.get('max_connections', 0),
                "active_connections": redis_stats.get('connection_count', 0),
                "connection_utilization": redis_stats.get('connection_utilization', 0),
                "circuit_breaker_state": redis_stats.get('circuit_breaker_state', 'unknown'),
                "error_count": redis_stats.get('error_count', 0)
            }
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Redis health check failed", error=str(e))
        
        return DependencyStatus(
            name="redis",
            status="unhealthy",
            response_time_ms=round(response_time, 2),
            error=str(e)
        )


async def check_dynamodb_health() -> DependencyStatus:
    """Check DynamoDB connection health."""
    start_time = time.time()
    
    try:
        from app.infrastructure.database.connection_manager import get_connection_manager
        
        connection_manager = await get_connection_manager()
        stats = await connection_manager.get_connection_stats()
        dynamodb_stats = stats.get('dynamodb', {})
        
        response_time = (time.time() - start_time) * 1000
        
        status = "healthy" if dynamodb_stats.get('is_healthy', False) else "unhealthy"
        
        return DependencyStatus(
            name="dynamodb",
            status=status,
            response_time_ms=round(response_time, 2),
            details={
                "region": "us-east-1",  # From settings
                "max_connections": dynamodb_stats.get('max_connections', 0),
                "circuit_breaker_state": dynamodb_stats.get('circuit_breaker_state', 'unknown'),
                "error_count": dynamodb_stats.get('error_count', 0),
                "uptime_seconds": dynamodb_stats.get('uptime_seconds', 0)
            }
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("DynamoDB health check failed", error=str(e))
        
        return DependencyStatus(
            name="dynamodb",
            status="unhealthy",
            response_time_ms=round(response_time, 2),
            error=str(e)
        )


async def check_algolia_health() -> DependencyStatus:
    """Check Algolia service health."""
    start_time = time.time()
    
    try:
        # TODO: Implement actual Algolia health check
        # This is a placeholder that will be implemented when Algolia is integrated
        await asyncio.sleep(0.005)  # Simulate Algolia API call
        
        response_time = (time.time() - start_time) * 1000
        
        return DependencyStatus(
            name="algolia",
            status="healthy",
            response_time_ms=round(response_time, 2),
            details={
                "app_id": "configured",
                "indexes_available": ["products_en", "products_ar"],  # Placeholder
            }
        )
    except Exception as e:
        response_time = (time.time() - start_time) * 1000
        logger.error("Algolia health check failed", error=str(e))
        
        return DependencyStatus(
            name="algolia",
            status="unhealthy",
            response_time_ms=round(response_time, 2),
            error=str(e)
        )


def get_system_info() -> Dict[str, Any]:
    """Get system information."""
    import psutil
    import platform
    
    try:
        return {
            "python_version": platform.python_version(),
            "platform": platform.platform(),
            "cpu_count": psutil.cpu_count(),
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "percent": psutil.virtual_memory().percent,
            },
            "disk": {
                "total": psutil.disk_usage('/').total,
                "free": psutil.disk_usage('/').free,
                "percent": psutil.disk_usage('/').percent,
            }
        }
    except ImportError:
        # psutil not available, return basic info
        return {
            "python_version": platform.python_version(),
            "platform": platform.platform(),
        }
    except Exception as e:
        logger.warning("Failed to get system info", error=str(e))
        return {"error": "Failed to retrieve system information"}


@router.get("/", response_model=HealthStatus, tags=["health"])
async def health_check(settings: Settings = Depends(get_settings)):
    """
    Basic health check endpoint.
    Returns simple health status for load balancers and monitoring systems.
    """
    uptime = time.time() - _start_time
    
    health_status = HealthStatus(
        status="healthy",
        timestamp=datetime.now(timezone.utc),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
        uptime_seconds=round(uptime, 2)
    )
    
    logger.info("Health check requested", status="healthy", uptime=uptime)
    
    return health_status


@router.get("/detailed", response_model=DetailedHealthResponse, tags=["health"])
async def detailed_health_check(settings: Settings = Depends(get_settings)):
    """
    Detailed health check endpoint.
    Returns comprehensive health status including all dependencies.
    """
    uptime = time.time() - _start_time
    
    # Check all dependencies concurrently
    dependency_checks = await asyncio.gather(
        check_redis_health(),
        check_dynamodb_health(),
        check_algolia_health(),
        return_exceptions=True
    )
    
    # Filter out exceptions and create dependency status list
    dependencies = []
    for check_result in dependency_checks:
        if isinstance(check_result, DependencyStatus):
            dependencies.append(check_result)
        elif isinstance(check_result, Exception):
            logger.error("Dependency check failed", error=str(check_result))
            dependencies.append(DependencyStatus(
                name="unknown",
                status="unhealthy",
                response_time_ms=0,
                error=str(check_result)
            ))
    
    # Determine overall health status
    unhealthy_deps = [dep for dep in dependencies if dep.status == "unhealthy"]
    degraded_deps = [dep for dep in dependencies if dep.status == "degraded"]
    
    if unhealthy_deps:
        overall_status = "unhealthy"
    elif degraded_deps:
        overall_status = "degraded"
    else:
        overall_status = "healthy"
    
    # Get circuit breaker health status
    circuit_breaker_health = await health_check_all_circuit_breakers()
    
    # Check if any circuit breakers are unhealthy
    unhealthy_breakers = [name for name, status in circuit_breaker_health.items() if not status["healthy"]]
    if unhealthy_breakers and overall_status == "healthy":
        overall_status = "degraded"
    
    # Get system information
    system_info = get_system_info()
    
    response = DetailedHealthResponse(
        status=overall_status,
        timestamp=datetime.now(timezone.utc),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT,
        uptime_seconds=round(uptime, 2),
        dependencies=dependencies,
        circuit_breakers=circuit_breaker_health,
        system_info=system_info
    )
    
    logger.info(
        "Detailed health check requested",
        status=overall_status,
        uptime=uptime,
        dependencies_count=len(dependencies),
        unhealthy_count=len(unhealthy_deps),
        degraded_count=len(degraded_deps)
    )
    
    # Return appropriate HTTP status code
    if overall_status == "unhealthy":
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=response.dict()
        )
    elif overall_status == "degraded":
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content=response.dict()
        )
    else:
        return response


@router.get("/ready", tags=["health"])
async def readiness_probe():
    """
    Kubernetes readiness probe endpoint.
    Returns 200 if the service is ready to accept traffic.
    """
    # Check critical dependencies
    try:
        redis_status = await check_redis_health()
        dynamodb_status = await check_dynamodb_health()
        
        # Service is ready if critical dependencies are healthy
        if redis_status.status == "healthy" and dynamodb_status.status == "healthy":
            return {"status": "ready"}
        else:
            logger.warning("Service not ready", redis=redis_status.status, dynamodb=dynamodb_status.status)
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Service not ready"
            )
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service not ready"
        )


@router.get("/live", tags=["health"])
async def liveness_probe():
    """
    Kubernetes liveness probe endpoint.
    Returns 200 if the service is alive and should not be restarted.
    """
    # Simple liveness check - if we can respond, we're alive
    return {"status": "alive", "timestamp": datetime.now(timezone.utc)}


@router.get("/metrics", response_model=MetricsResponse, tags=["monitoring"])
async def metrics_endpoint(settings: Settings = Depends(get_settings)):
    """
    Prometheus-compatible metrics endpoint.
    Returns application metrics in a structured format.
    """
    if not settings.ENABLE_METRICS:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Metrics endpoint is disabled"
        )
    
    uptime = time.time() - _start_time
    
    # Collect basic metrics
    metrics = {
        "app_info": {
            "name": settings.APP_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
        },
        "app_uptime_seconds": round(uptime, 2),
        "app_start_time": _start_time,
        
        # Request metrics (placeholder - will be implemented with actual metrics collection)
        "http_requests_total": 0,
        "http_request_duration_seconds": 0,
        "http_requests_in_progress": 0,
        
        # Dependency metrics (placeholder)
        "redis_connections_active": 0,
        "dynamodb_requests_total": 0,
        "algolia_requests_total": 0,
        
        # System metrics
        "system_info": get_system_info(),
    }
    
    response = MetricsResponse(
        timestamp=datetime.now(timezone.utc),
        metrics=metrics
    )
    
    logger.debug("Metrics requested", metrics_count=len(metrics))
    
    return response


@router.get("/status", tags=["monitoring"])
async def status_dashboard():
    """
    Status dashboard endpoint for operational visibility.
    Returns a comprehensive view of system status.
    """
    uptime = time.time() - _start_time
    settings = get_settings()
    
    # Get dependency statuses
    dependencies = await asyncio.gather(
        check_redis_health(),
        check_dynamodb_health(),
        check_algolia_health(),
        return_exceptions=True
    )
    
    # Filter valid dependency statuses
    valid_dependencies = [dep for dep in dependencies if isinstance(dep, DependencyStatus)]
    
    status_info = {
        "service": {
            "name": settings.APP_NAME,
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "uptime_seconds": round(uptime, 2),
            "uptime_human": f"{int(uptime // 3600)}h {int((uptime % 3600) // 60)}m {int(uptime % 60)}s",
            "started_at": datetime.fromtimestamp(_start_time, timezone.utc).isoformat(),
        },
        "health": {
            "overall": "healthy" if all(dep.status == "healthy" for dep in valid_dependencies) else "degraded",
            "dependencies": [dep.dict() for dep in valid_dependencies],
        },
        "configuration": {
            "debug_mode": settings.DEBUG,
            "log_level": settings.LOG_LEVEL,
            "metrics_enabled": settings.ENABLE_METRICS,
            "tracing_enabled": settings.ENABLE_TRACING,
        },
        "system": get_system_info(),
        "timestamp": datetime.now(timezone.utc).isoformat(),
    }
    
    return status_info


@router.get("/circuit-breakers", tags=["monitoring"])
async def circuit_breaker_status():
    """
    Circuit breaker status endpoint.
    Returns detailed information about all circuit breakers.
    """
    try:
        # Get comprehensive circuit breaker statistics
        circuit_breaker_stats = get_circuit_breaker_stats()
        circuit_breaker_health = await health_check_all_circuit_breakers()
        unhealthy_breakers = get_unhealthy_circuit_breakers()
        open_breakers = get_open_circuit_breakers()
        half_open_breakers = get_half_open_circuit_breakers()
        summary = get_circuit_breaker_summary()
        
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "summary": summary,
            "circuit_breakers": {
                "detailed_stats": circuit_breaker_stats,
                "health_status": circuit_breaker_health,
                "unhealthy": {name: breaker.get_stats() for name, breaker in unhealthy_breakers.items()},
                "open": {name: breaker.get_stats() for name, breaker in open_breakers.items()},
                "half_open": {name: breaker.get_stats() for name, breaker in half_open_breakers.items()}
            }
        }
    except Exception as e:
        logger.error("Failed to get circuit breaker status", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve circuit breaker status"
        )


@router.post("/circuit-breakers/{breaker_name}/reset", tags=["monitoring"])
async def reset_circuit_breaker_endpoint(breaker_name: str):
    """
    Reset a specific circuit breaker.
    This endpoint allows manual intervention to reset a circuit breaker.
    """
    try:
        success = reset_circuit_breaker(breaker_name)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Circuit breaker '{breaker_name}' not found"
            )
        
        logger.info(f"Circuit breaker '{breaker_name}' manually reset via API")
        
        return {
            "message": f"Circuit breaker '{breaker_name}' has been reset",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset circuit breaker '{breaker_name}'", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to reset circuit breaker '{breaker_name}'"
        )


@router.post("/circuit-breakers/{breaker_name}/force-open", tags=["monitoring"])
async def force_open_circuit_breaker_endpoint(breaker_name: str):
    """
    Force a specific circuit breaker to open state.
    This endpoint allows manual intervention to open a circuit breaker.
    """
    try:
        success = force_open_circuit_breaker(breaker_name)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Circuit breaker '{breaker_name}' not found"
            )
        
        logger.warning(f"Circuit breaker '{breaker_name}' manually forced open via API")
        
        return {
            "message": f"Circuit breaker '{breaker_name}' has been forced open",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to force open circuit breaker '{breaker_name}'", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to force open circuit breaker '{breaker_name}'"
        )


@router.post("/circuit-breakers/reset-all", tags=["monitoring"])
async def reset_all_circuit_breakers_endpoint():
    """
    Reset all circuit breakers.
    This endpoint allows manual intervention to reset all circuit breakers.
    """
    try:
        from app.utils.circuit_breaker import reset_all_circuit_breakers
        
        reset_all_circuit_breakers()
        
        logger.info("All circuit breakers manually reset")
        
        return {
            "message": "All circuit breakers have been reset",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error("Failed to reset all circuit breakers", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset all circuit breakers"
        )