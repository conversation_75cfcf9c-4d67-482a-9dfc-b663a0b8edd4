"""
Wishlist management endpoints.

This module contains all the REST API endpoints for wishlist operations
including CRUD operations, privacy settings, and sharing functionality.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from fastapi.responses import JSONResponse

from app.services.wishlist_service import WishlistService
from app.schemas.requests.wishlist import (
    CreateWishlistRequest,
    UpdateWishlistRequest,
    UpdateWishlistPrivacyRequest
)
from app.schemas.responses.wishlist import (
    WishlistResponse,
    WishlistListResponse,
    ShareHashResponse
)
from app.schemas.base import (
    SuccessResponseSchema,
    ErrorResponseSchema
)
from app.api.dependencies import (
    get_wishlist_service,
    get_current_user_id,
    validate_country_language
)
from app.core.exceptions import WishlistNotFoundError, ValidationError
from app.utils.helpers import generate_correlation_id
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/",
    response_model=WishlistListResponse,
    summary="Get user wishlists",
    description="Retrieve all wishlists for the authenticated user with filtering, pagination, and optional product enrichment"
)
async def get_user_wishlists(
    country: str = Query("ae", description="Country code for product localization"),
    language: str = Query("en", description="Language code for product localization"),
    include_products: bool = Query(True, description="Whether to include product details"),
    include_items: bool = Query(True, description="Whether to include wishlist items"),
    default_only: bool = Query(False, description="Whether to return only the default wishlist"),
    public_only: bool = Query(False, description="Whether to return only public wishlists"),
    sort_by: str = Query("updated_at", description="Field to sort by (name, created_at, updated_at, item_count)"),
    sort_order: str = Query("desc", description="Sort order (asc, desc)"),
    page: int = Query(1, ge=1, description="Page number (1-based)"),
    page_size: int = Query(20, ge=1, le=100, description="Number of items per page (max 100)"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    _: dict = Depends(validate_country_language)
) -> WishlistListResponse:
    """Get all wishlists for the authenticated user."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting wishlists for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "country": country,
            "language": language,
            "filters": {
                "default_only": default_only,
                "public_only": public_only,
                "sort_by": sort_by,
                "sort_order": sort_order,
                "page": page,
                "page_size": page_size
            }
        })

        # Validate sort parameters
        valid_sort_fields = ["name", "created_at", "updated_at", "item_count"]
        if sort_by not in valid_sort_fields:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponseSchema(
                    code="INVALID_SORT_FIELD",
                    message=f"sort_by must be one of: {', '.join(valid_sort_fields)}",
                    correlation_id=correlation_id
                ).dict()
            )

        if sort_order.lower() not in ["asc", "desc"]:
            raise HTTPException(
                status_code=400,
                detail=ErrorResponseSchema(
                    code="INVALID_SORT_ORDER",
                    message="sort_order must be 'asc' or 'desc'",
                    correlation_id=correlation_id
                ).dict()
            )

        wishlists = await wishlist_service.get_user_wishlists(
            user_id=user_id,
            country=country,
            language=language,
            include_products=include_products,
            include_items=include_items,
            default_only=default_only,
            public_only=public_only,
            sort_by=sort_by,
            sort_order=sort_order.lower(),
            page=page,
            page_size=page_size
        )

        # Convert domain models to response schemas
        wishlist_responses = [
            WishlistResponse.from_domain_model(wishlist)
            for wishlist in wishlists
        ]

        # Calculate pagination info
        total_pages = (len(wishlist_responses) + page_size - 1) // page_size
        has_next = page < total_pages
        has_previous = page > 1

        return WishlistListResponse(
            wishlists=wishlist_responses,
            total=len(wishlist_responses),
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            has_next=has_next,
            has_previous=has_previous
        )

    except Exception as e:
        logger.error(f"Failed to get user wishlists: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve wishlists",
                correlation_id=correlation_id
            ).dict()
        )


@router.post(
    "/",
    response_model=WishlistResponse,
    status_code=201,
    summary="Create wishlist",
    description="Create a new wishlist for the authenticated user"
)
async def create_wishlist(
    request: CreateWishlistRequest,
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Create a new wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Creating wishlist for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_name": request.name
        })

        wishlist = await wishlist_service.create_wishlist(user_id, request)

        return WishlistResponse.from_domain_model(wishlist)

    except ValidationError as e:
        logger.warning(f"Validation error creating wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to create wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to create wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.get(
    "/{wishlist_id}",
    response_model=WishlistResponse,
    summary="Get wishlist by ID",
    description="Retrieve a specific wishlist by its ID"
)
async def get_wishlist(
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    country: str = Query("ae", description="Country code for product localization"),
    language: str = Query("en", description="Language code for product localization"),
    include_products: bool = Query(True, description="Whether to include product details"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    _: dict = Depends(validate_country_language)
) -> WishlistResponse:
    """Get a specific wishlist by ID."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        wishlist = await wishlist_service.get_wishlist_by_id(
            user_id=user_id,
            wishlist_id=wishlist_id,
            country=country,
            language=language,
            include_products=include_products
        )

        if not wishlist:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponseSchema(
                    code="WISHLIST_NOT_FOUND",
                    message=f"Wishlist {wishlist_id} not found",
                    correlation_id=correlation_id
                ).dict()
            )

        return WishlistResponse.from_domain_model(wishlist)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.put(
    "/{wishlist_id}",
    response_model=WishlistResponse,
    summary="Update wishlist",
    description="Update a wishlist's properties"
)
async def update_wishlist(
    request: UpdateWishlistRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Update a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Updating wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        wishlist = await wishlist_service.update_wishlist(user_id, wishlist_id, request)

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error updating wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to update wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to update wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.delete(
    "/{wishlist_id}",
    response_model=SuccessResponseSchema,
    summary="Delete wishlist",
    description="Delete a wishlist and all its items"
)
async def delete_wishlist(
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> SuccessResponseSchema:
    """Delete a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Deleting wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        success = await wishlist_service.delete_wishlist(user_id, wishlist_id)

        if success:
            return SuccessResponseSchema(
                message="Wishlist deleted successfully",
                correlation_id=correlation_id
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=ErrorResponseSchema(
                    code="DELETE_FAILED",
                    message="Failed to delete wishlist",
                    correlation_id=correlation_id
                ).dict()
            )

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to delete wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to delete wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.put(
    "/{wishlist_id}/privacy",
    response_model=WishlistResponse,
    summary="Update wishlist privacy",
    description="Update the privacy settings of a wishlist (public/private)"
)
async def update_wishlist_privacy(
    request: UpdateWishlistPrivacyRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Update wishlist privacy settings."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Updating privacy for wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "is_public": request.is_public
        })

        wishlist = await wishlist_service.update_wishlist_privacy(
            user_id=user_id,
            wishlist_id=wishlist_id,
            is_public=request.is_public
        )

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to update wishlist privacy: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to update wishlist privacy",
                correlation_id=correlation_id
            ).dict()
        )


@router.put(
    "/{wishlist_id}/regenerate-hash",
    response_model=ShareHashResponse,
    summary="Regenerate share hash",
    description="Generate a new share hash for the wishlist"
)
async def regenerate_share_hash(
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> ShareHashResponse:
    """Regenerate the share hash for a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Regenerating share hash for wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        new_hash = await wishlist_service.regenerate_share_hash(
            user_id=user_id,
            wishlist_id=wishlist_id
        )

        share_url = f"https://api.example.com/api/v1/wishlists/shared/{new_hash}"

        return ShareHashResponse(
            message="Share hash regenerated successfully",
            share_hash=new_hash,
            share_url=share_url,
            correlation_id=correlation_id
        )

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to regenerate share hash: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to regenerate share hash",
                correlation_id=correlation_id
            ).dict()
        )
