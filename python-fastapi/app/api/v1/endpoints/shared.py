"""
Shared wishlist endpoints.

This module contains REST API endpoints for accessing public shared wishlists
using their share hash, without requiring authentication.
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Request

from app.services.wishlist_service import WishlistService
from app.schemas.responses.wishlist import WishlistResponse
from app.schemas.base import ErrorResponseSchema
from app.api.dependencies import (
    get_wishlist_service,
    validate_country_language
)
from app.utils.helpers import generate_correlation_id
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/{share_hash}",
    response_model=WishlistResponse,
    summary="Get shared wishlist",
    description="Retrieve a public shared wishlist using its share hash"
)
async def get_shared_wishlist(
    share_hash: str = Path(..., description="The share hash of the wishlist"),
    country: str = Query("ae", description="Country code for product localization"),
    language: str = Query("en", description="Language code for product localization"),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    _: dict = Depends(validate_country_language)
) -> WishlistResponse:
    """Get a shared wishlist by its share hash."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting shared wishlist {share_hash}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "country": country,
            "language": language
        })

        wishlist = await wishlist_service.get_shared_wishlist(
            share_hash=share_hash,
            country=country,
            language=language
        )

        if not wishlist:
            logger.info(f"Shared wishlist not found or not public", extra={
                "correlation_id": correlation_id,
                "share_hash": share_hash
            })
            raise HTTPException(
                status_code=404,
                detail=ErrorResponseSchema(
                    code="SHARED_WISHLIST_NOT_FOUND",
                    message="Shared wishlist not found or is not public",
                    correlation_id=correlation_id
                ).dict()
            )

        return WishlistResponse.from_domain_model(wishlist)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get shared wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve shared wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.get(
    "/{share_hash}/analytics",
    summary="Get shared wishlist analytics",
    description="Get analytics data for a shared wishlist (view count, access patterns, etc.)"
)
async def get_shared_wishlist_analytics(
    share_hash: str = Path(..., description="The share hash of the wishlist"),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> dict:
    """Get analytics for a shared wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting analytics for shared wishlist {share_hash}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash
        })

        analytics = await wishlist_service.get_shared_wishlist_analytics(share_hash)

        if not analytics:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponseSchema(
                    code="SHARED_WISHLIST_NOT_FOUND",
                    message="Shared wishlist not found or analytics not available",
                    correlation_id=correlation_id
                ).dict()
            )

        return {
            "share_hash": share_hash,
            "analytics": analytics,
            "correlation_id": correlation_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get shared wishlist analytics: {e}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve analytics",
                correlation_id=correlation_id
            ).dict()
        )


@router.post(
    "/{share_hash}/track-view",
    status_code=204,
    summary="Track shared wishlist view",
    description="Track when a shared wishlist is viewed (for analytics)"
)
async def track_shared_wishlist_view(
    share_hash: str = Path(..., description="The share hash of the wishlist"),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    request: Request = None
):
    """Track a view of a shared wishlist for analytics."""
    correlation_id = generate_correlation_id()

    try:
        # Extract tracking information
        user_agent = request.headers.get("User-Agent", "")
        client_ip = request.client.host if request.client else "unknown"
        referer = request.headers.get("Referer", "")

        logger.info(f"Tracking view for shared wishlist {share_hash}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "referer": referer
        })

        await wishlist_service.track_shared_wishlist_view(
            share_hash=share_hash,
            client_ip=client_ip,
            user_agent=user_agent,
            referer=referer
        )

        # Return 204 No Content for successful tracking
        return

    except Exception as e:
        # Don't fail the request if tracking fails, just log it
        logger.warning(f"Failed to track shared wishlist view: {e}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "error": str(e)
        })
        # Still return 204 to not break the client experience
        return
