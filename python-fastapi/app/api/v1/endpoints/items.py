"""
Wishlist items management endpoints.

This module contains REST API endpoints for managing items within wishlists,
including adding, removing, and updating wishlist items.
"""
from fastapi import APIRouter, Depends, HTTPException, Path
from fastapi.responses import JSONResponse

from app.services.wishlist_service import WishlistService
from app.schemas.requests.item import (
    AddItemRequest,
    UpdateItemRequest,
    BulkAddItemsRequest,
    BulkRemoveItemsRequest
)
from app.schemas.responses.wishlist import WishlistResponse
from app.schemas.base import (
    SuccessResponseSchema,
    ErrorResponseSchema
)
from app.api.dependencies import (
    get_wishlist_service,
    get_current_user_id
)
from app.core.exceptions import WishlistNotFoundError, ValidationError
from app.utils.helpers import generate_correlation_id
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post(
    "/{wishlist_id}/items",
    response_model=WishlistResponse,
    status_code=201,
    summary="Add item to wishlist",
    description="Add a product item to a specific wishlist"
)
async def add_item_to_wishlist(
    request: AddItemRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Add an item to a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Adding item {request.product_id} to wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": request.product_id
        })

        wishlist = await wishlist_service.add_item_to_wishlist(
            user_id=user_id,
            wishlist_id=wishlist_id,
            request=request
        )

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error adding item: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": request.product_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to add item to wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": request.product_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to add item to wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.delete(
    "/{wishlist_id}/items/{product_id}",
    response_model=WishlistResponse,
    summary="Remove item from wishlist",
    description="Remove a product item from a specific wishlist"
)
async def remove_item_from_wishlist(
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    product_id: str = Path(..., description="The ID of the product to remove"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Remove an item from a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Removing item {product_id} from wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })

        wishlist = await wishlist_service.remove_item_from_wishlist(
            user_id=user_id,
            wishlist_id=wishlist_id,
            product_id=product_id
        )

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error removing item: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to remove item from wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to remove item from wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.post(
    "/{wishlist_id}/items/bulk",
    response_model=WishlistResponse,
    status_code=201,
    summary="Add multiple items to wishlist",
    description="Add multiple product items to a specific wishlist in a single operation"
)
async def bulk_add_items_to_wishlist(
    request: BulkAddItemsRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Add multiple items to a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Bulk adding {len(request.items)} items to wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "item_count": len(request.items)
        })

        wishlist = await wishlist_service.bulk_add_items_to_wishlist(
            user_id=user_id,
            wishlist_id=wishlist_id,
            request=request
        )

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error bulk adding items: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to bulk add items to wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to bulk add items to wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.delete(
    "/{wishlist_id}/items/bulk",
    response_model=WishlistResponse,
    summary="Remove multiple items from wishlist",
    description="Remove multiple product items from a specific wishlist in a single operation"
)
async def bulk_remove_items_from_wishlist(
    request: BulkRemoveItemsRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Remove multiple items from a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Bulk removing {len(request.product_ids)} items from wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "item_count": len(request.product_ids)
        })

        wishlist = await wishlist_service.bulk_remove_items_from_wishlist(
            user_id=user_id,
            wishlist_id=wishlist_id,
            request=request
        )

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error bulk removing items: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to bulk remove items from wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to bulk remove items from wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.put(
    "/{wishlist_id}/items/{product_id}",
    response_model=WishlistResponse,
    summary="Update item in wishlist",
    description="Update an existing item in a wishlist (notes, quantity, priority)"
)
async def update_item_in_wishlist(
    request: UpdateItemRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    product_id: str = Path(..., description="The ID of the product to update"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Update an item in a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Updating item {product_id} in wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })

        wishlist = await wishlist_service.update_item_in_wishlist(
            user_id=user_id,
            wishlist_id=wishlist_id,
            product_id=product_id,
            request=request
        )

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error updating item: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to update item in wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to update item in wishlist",
                correlation_id=correlation_id
            ).dict()
        )
