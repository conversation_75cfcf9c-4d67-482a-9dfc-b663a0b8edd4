"""
Model factories for testing and data generation.

This module provides factory classes to create domain model instances
with realistic test data for unit tests, integration tests, and development.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from uuid import uuid4
import random
import string
import secrets

from .wishlist import Wishlist, WishlistItem


class WishlistItemFactory:
    """Factory for creating WishlistItem instances with test data."""
    
    # Sample product IDs for testing
    SAMPLE_PRODUCT_IDS = [
        "PROD001", "PROD002", "PROD003", "PROD004", "PROD005",
        "PROD006", "PROD007", "PROD008", "PROD009", "PROD010",
        "BABY001", "BABY002", "BABY003", "TOY001", "TOY002",
        "CLOTH001", "CLOTH002", "FEED001", "FEED002", "CARE001"
    ]
    
    # Sample notes for testing
    SAMPLE_NOTES = [
        "For baby shower gift",
        "Birthday present for nephew",
        "Need to check size first",
        "Wait for sale",
        "Recommended by friend",
        "Perfect for nursery",
        "Add to cart when available",
        None,  # Some items have no notes
        None,
        None
    ]
    
    # Sample product data for enrichment
    SAMPLE_PRODUCTS = {
        "PROD001": {
            "title": "Baby Stroller Premium",
            "price": 299.99,
            "currency": "AED",
            "brand": "BabyBrand",
            "category": "Strollers",
            "image_url": "https://example.com/stroller.jpg",
            "availability": "in_stock"
        },
        "BABY001": {
            "title": "Organic Baby Food Set",
            "price": 45.50,
            "currency": "AED",
            "brand": "OrganicBaby",
            "category": "Baby Food",
            "image_url": "https://example.com/babyfood.jpg",
            "availability": "in_stock"
        },
        "TOY001": {
            "title": "Educational Building Blocks",
            "price": 89.99,
            "currency": "AED",
            "brand": "LearnToys",
            "category": "Educational Toys",
            "image_url": "https://example.com/blocks.jpg",
            "availability": "limited_stock"
        }
    }
    
    @classmethod
    def create(
        self,
        product_id: Optional[str] = None,
        notes: Optional[str] = None,
        added_at: Optional[datetime] = None,
        with_product_data: bool = False
    ) -> WishlistItem:
        """
        Create a WishlistItem with optional test data.
        
        Args:
            product_id: Specific product ID, or random if None
            notes: Specific notes, or random if None
            added_at: Specific datetime, or recent random if None
            with_product_data: Whether to include enriched product data
            
        Returns:
            A WishlistItem instance
        """
        if product_id is None:
            product_id = random.choice(self.SAMPLE_PRODUCT_IDS)
        
        if notes is None:
            notes = random.choice(self.SAMPLE_NOTES)
        
        if added_at is None:
            # Random time in the last 30 days
            days_ago = random.randint(0, 30)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            added_at = datetime.utcnow() - timedelta(
                days=days_ago, 
                hours=hours_ago, 
                minutes=minutes_ago
            )
        
        item = WishlistItem(
            product_id=product_id,
            notes=notes,
            added_at=added_at
        )
        
        if with_product_data and product_id in self.SAMPLE_PRODUCTS:
            item.product = self.SAMPLE_PRODUCTS[product_id].copy()
        
        return item
    
    @classmethod
    def create_batch(
        self,
        count: int,
        unique_products: bool = True,
        with_product_data: bool = False
    ) -> List[WishlistItem]:
        """
        Create multiple WishlistItems.
        
        Args:
            count: Number of items to create
            unique_products: Whether each item should have a unique product_id
            with_product_data: Whether to include enriched product data
            
        Returns:
            List of WishlistItem instances
        """
        items = []
        used_product_ids = set()
        
        for i in range(count):
            if unique_products:
                # Ensure unique product IDs
                available_ids = [
                    pid for pid in self.SAMPLE_PRODUCT_IDS 
                    if pid not in used_product_ids
                ]
                if not available_ids:
                    # Generate new unique ID if we run out
                    product_id = f"PROD{len(used_product_ids) + 1:03d}"
                else:
                    product_id = random.choice(available_ids)
                used_product_ids.add(product_id)
            else:
                product_id = None  # Will be randomly selected
            
            item = self.create(
                product_id=product_id,
                with_product_data=with_product_data
            )
            items.append(item)
        
        return items


class WishlistFactory:
    """Factory for creating Wishlist instances with test data."""
    
    # Sample wishlist names
    SAMPLE_NAMES = [
        "My Wishlist",
        "Baby Essentials",
        "Birthday Gifts",
        "Christmas List",
        "Baby Shower Registry",
        "Nursery Items",
        "Toys & Games",
        "Feeding Supplies",
        "Clothing & Accessories",
        "Books & Learning",
        "Safety & Care",
        "Travel Gear"
    ]
    
    # Sample user IDs for testing
    SAMPLE_USER_IDS = [
        "user_001", "user_002", "user_003", "user_004", "user_005",
        "user_006", "user_007", "user_008", "user_009", "user_010"
    ]
    
    @classmethod
    def create(
        cls,
        user_id: Optional[str] = None,
        wishlist_id: Optional[str] = None,
        name: Optional[str] = None,
        is_default: Optional[bool] = None,
        is_public: Optional[bool] = None,
        share_hash: Optional[str] = None,
        item_count: int = 0,
        with_product_data: bool = False,
        created_days_ago: Optional[int] = None
    ) -> Wishlist:
        """
        Create a Wishlist with optional test data.
        
        Args:
            user_id: Specific user ID, or random if None
            wishlist_id: Specific wishlist ID, or random if None
            name: Specific name, or random if None
            is_default: Specific default status, or random if None
            is_public: Specific public status, or random if None
            share_hash: Specific share hash, or generated if None
            item_count: Number of items to add to the wishlist
            with_product_data: Whether items should have enriched product data
            created_days_ago: How many days ago the wishlist was created
            
        Returns:
            A Wishlist instance
        """
        if user_id is None:
            user_id = random.choice(cls.SAMPLE_USER_IDS)
        
        if wishlist_id is None:
            wishlist_id = str(uuid4())
        
        if name is None:
            name = random.choice(cls.SAMPLE_NAMES)
        
        if is_default is None:
            is_default = random.choice([True, False, False, False])  # 25% chance
        
        if is_public is None:
            is_public = random.choice([True, False, False])  # 33% chance
        
        if share_hash is None:
            alphabet = string.ascii_letters + string.digits
            share_hash = ''.join(secrets.choice(alphabet) for _ in range(32))
        
        # Set creation time
        if created_days_ago is not None:
            created_at = datetime.utcnow() - timedelta(days=created_days_ago)
        else:
            # Random time in the last 90 days
            days_ago = random.randint(0, 90)
            created_at = datetime.utcnow() - timedelta(days=days_ago)
        
        # Updated time is between created time and now
        max_update_days = (datetime.utcnow() - created_at).days
        if max_update_days > 0:
            update_days_ago = random.randint(0, max_update_days)
            updated_at = datetime.utcnow() - timedelta(days=update_days_ago)
        else:
            updated_at = created_at
        
        # Create the wishlist
        wishlist = Wishlist(
            user_id=user_id,
            wishlist_id=wishlist_id,
            name=name,
            is_default=is_default,
            is_public=is_public,
            share_hash=share_hash,
            items=[],
            created_at=created_at,
            updated_at=updated_at
        )
        
        # Add items if requested
        if item_count > 0:
            items = WishlistItemFactory.create_batch(
                count=item_count,
                unique_products=True,
                with_product_data=with_product_data
            )
            wishlist.items = items
        
        return wishlist
    
    @classmethod
    def create_default_wishlist(
        cls,
        user_id: str,
        name: str = "My Wishlist",
        item_count: int = 3
    ) -> Wishlist:
        """
        Create a default wishlist for a user.
        
        Args:
            user_id: The user ID
            name: The wishlist name
            item_count: Number of items to add
            
        Returns:
            A default Wishlist instance
        """
        return cls.create(
            user_id=user_id,
            name=name,
            is_default=True,
            is_public=False,
            item_count=item_count,
            created_days_ago=1  # Created recently
        )
    
    @classmethod
    def create_public_wishlist(
        cls,
        user_id: str,
        name: str = "Baby Registry",
        item_count: int = 10
    ) -> Wishlist:
        """
        Create a public wishlist for sharing.
        
        Args:
            user_id: The user ID
            name: The wishlist name
            item_count: Number of items to add
            
        Returns:
            A public Wishlist instance
        """
        return cls.create(
            user_id=user_id,
            name=name,
            is_default=False,
            is_public=True,
            item_count=item_count,
            with_product_data=True,
            created_days_ago=7  # Created a week ago
        )
    
    @classmethod
    def create_user_wishlists(
        cls,
        user_id: str,
        count: int = 3,
        ensure_default: bool = True
    ) -> List[Wishlist]:
        """
        Create multiple wishlists for a single user.
        
        Args:
            user_id: The user ID
            count: Number of wishlists to create
            ensure_default: Whether to ensure one wishlist is marked as default
            
        Returns:
            List of Wishlist instances
        """
        wishlists = []
        
        for i in range(count):
            # First wishlist is default if ensure_default is True
            is_default = (i == 0) if ensure_default else None
            
            wishlist = cls.create(
                user_id=user_id,
                is_default=is_default,
                item_count=random.randint(0, 8)
            )
            wishlists.append(wishlist)
        
        return wishlists
    
    @classmethod
    def create_empty_wishlist(cls, user_id: str, name: str = "Empty List") -> Wishlist:
        """
        Create an empty wishlist.
        
        Args:
            user_id: The user ID
            name: The wishlist name
            
        Returns:
            An empty Wishlist instance
        """
        return cls.create(
            user_id=user_id,
            name=name,
            item_count=0
        )


# Convenience functions for quick factory usage
def create_wishlist(**kwargs) -> Wishlist:
    """Convenience function to create a wishlist."""
    return WishlistFactory.create(**kwargs)


def create_wishlist_item(**kwargs) -> WishlistItem:
    """Convenience function to create a wishlist item."""
    return WishlistItemFactory.create(**kwargs)


def create_test_data(user_count: int = 5) -> Dict[str, List[Wishlist]]:
    """
    Create a complete set of test data with multiple users and wishlists.
    
    Args:
        user_count: Number of users to create data for
        
    Returns:
        Dictionary mapping user IDs to their wishlists
    """
    test_data = {}
    
    for i in range(user_count):
        user_id = f"test_user_{i+1:03d}"
        wishlist_count = random.randint(1, 4)
        
        wishlists = WishlistFactory.create_user_wishlists(
            user_id=user_id,
            count=wishlist_count,
            ensure_default=True
        )
        
        test_data[user_id] = wishlists
    
    return test_data