"""
Domain models for the Wishlist service.

This module contains the core business entities with their business logic,
validation rules, and common operations.
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import uuid4
import secrets
import string


@dataclass
class WishlistItem:
    """
    Domain model representing a single item in a wishlist.

    Contains product information and metadata about when it was added.
    """
    product_id: str
    notes: Optional[str] = None
    added_at: Optional[datetime] = None
    product: Optional[Dict[str, Any]] = field(default=None, repr=False)  # Enriched product data

    def __post_init__(self):
        """Validate and set defaults after initialization."""
        if not self.product_id or not self.product_id.strip():
            raise ValueError("product_id cannot be empty")

        if self.added_at is None:
            self.added_at = datetime.utcnow()

        # Validate notes length
        if self.notes and len(self.notes) > 500:
            raise ValueError("notes cannot exceed 500 characters")

    def update_notes(self, notes: Optional[str]) -> None:
        """Update the notes for this item."""
        if notes and len(notes) > 500:
            raise ValueError("notes cannot exceed 500 characters")
        self.notes = notes

    def has_product_data(self) -> bool:
        """Check if this item has enriched product data."""
        return self.product is not None and bool(self.product)

    def get_product_title(self) -> Optional[str]:
        """Get the product title from enriched data."""
        if self.has_product_data():
            return self.product.get('title') or self.product.get('name')
        return None


@dataclass
class Wishlist:
    """
    Domain model representing a user's wishlist.

    Contains business logic for managing wishlist items, privacy settings,
    and common operations like adding/removing items.
    """
    user_id: str
    wishlist_id: str
    name: str
    is_default: bool
    is_public: bool
    share_hash: str
    items: List[WishlistItem]
    created_at: datetime
    updated_at: datetime

    def __post_init__(self):
        """Validate wishlist data after initialization."""
        if not self.user_id or not self.user_id.strip():
            raise ValueError("user_id cannot be empty")

        if not self.wishlist_id or not self.wishlist_id.strip():
            raise ValueError("wishlist_id cannot be empty")

        if not self.name or not self.name.strip():
            raise ValueError("name cannot be empty")

        if len(self.name) > 255:
            raise ValueError("name cannot exceed 255 characters")

        if not self.share_hash or len(self.share_hash) < 16:
            raise ValueError("share_hash must be at least 16 characters")

        # Ensure items is a list
        if self.items is None:
            self.items = []

    def add_item(self, product_id: str, notes: Optional[str] = None) -> WishlistItem:
        """
        Add or update an item in the wishlist.

        Args:
            product_id: The ID of the product to add
            notes: Optional notes about the item

        Returns:
            The WishlistItem that was added or updated

        Raises:
            ValueError: If product_id is invalid
        """
        if not product_id or not product_id.strip():
            raise ValueError("product_id cannot be empty")

        # Remove existing item if present
        self.items = [item for item in self.items if item.product_id != product_id]

        # Create and add new item
        new_item = WishlistItem(
            product_id=product_id,
            notes=notes,
            added_at=datetime.utcnow()
        )
        self.items.append(new_item)
        self.updated_at = datetime.utcnow()

        return new_item

    def remove_item(self, product_id: str) -> bool:
        """
        Remove an item from the wishlist.

        Args:
            product_id: The ID of the product to remove

        Returns:
            True if the item was removed, False if it wasn't found
        """
        if not product_id:
            return False

        original_count = len(self.items)
        self.items = [item for item in self.items if item.product_id != product_id]

        if len(self.items) < original_count:
            self.updated_at = datetime.utcnow()
            return True
        return False

    def has_item(self, product_id: str) -> bool:
        """Check if the wishlist contains a specific product."""
        return any(item.product_id == product_id for item in self.items)

    def get_item(self, product_id: str) -> Optional[WishlistItem]:
        """Get a specific item from the wishlist."""
        for item in self.items:
            if item.product_id == product_id:
                return item
        return None

    def update_item_notes(self, product_id: str, notes: Optional[str]) -> bool:
        """
        Update notes for a specific item.

        Args:
            product_id: The ID of the product to update
            notes: New notes for the item

        Returns:
            True if the item was found and updated, False otherwise
        """
        item = self.get_item(product_id)
        if item:
            item.update_notes(notes)
            self.updated_at = datetime.utcnow()
            return True
        return False

    def clear_items(self) -> int:
        """
        Remove all items from the wishlist.

        Returns:
            The number of items that were removed
        """
        count = len(self.items)
        self.items = []
        if count > 0:
            self.updated_at = datetime.utcnow()
        return count

    def get_item_count(self) -> int:
        """Get the total number of items in the wishlist."""
        return len(self.items)

    def update_privacy(self, is_public: bool) -> None:
        """Update the privacy status of the wishlist."""
        if self.is_public != is_public:
            self.is_public = is_public
            self.updated_at = datetime.utcnow()

    def regenerate_share_hash(self) -> str:
        """
        Generate a new share hash for the wishlist.

        Returns:
            The new share hash
        """
        self.share_hash = self._generate_share_hash()
        self.updated_at = datetime.utcnow()
        return self.share_hash

    def update_name(self, name: str) -> None:
        """
        Update the wishlist name.

        Args:
            name: New name for the wishlist

        Raises:
            ValueError: If name is invalid
        """
        if not name or not name.strip():
            raise ValueError("name cannot be empty")

        if len(name) > 255:
            raise ValueError("name cannot exceed 255 characters")

        if self.name != name:
            self.name = name.strip()
            self.updated_at = datetime.utcnow()

    def set_as_default(self) -> None:
        """Mark this wishlist as the default for the user."""
        if not self.is_default:
            self.is_default = True
            self.updated_at = datetime.utcnow()

    def unset_as_default(self) -> None:
        """Remove the default status from this wishlist."""
        if self.is_default:
            self.is_default = False
            self.updated_at = datetime.utcnow()

    def get_product_ids(self) -> List[str]:
        """Get a list of all product IDs in this wishlist."""
        return [item.product_id for item in self.items]

    def is_empty(self) -> bool:
        """Check if the wishlist has no items."""
        return len(self.items) == 0

    def get_items_added_since(self, since: datetime) -> List[WishlistItem]:
        """Get items that were added after a specific datetime."""
        return [
            item for item in self.items
            if item.added_at and item.added_at > since
        ]

    @staticmethod
    def _generate_share_hash() -> str:
        """Generate a secure random share hash."""
        alphabet = string.ascii_letters + string.digits
        return ''.join(secrets.choice(alphabet) for _ in range(16))

    @classmethod
    def create_new(
        cls,
        user_id: str,
        name: str,
        is_default: bool = False,
        is_public: bool = False
    ) -> 'Wishlist':
        """
        Factory method to create a new wishlist with default values.

        Args:
            user_id: The ID of the user who owns the wishlist
            name: The name of the wishlist
            is_default: Whether this should be the default wishlist
            is_public: Whether this wishlist should be public

        Returns:
            A new Wishlist instance

        Raises:
            ValueError: If any parameters are invalid
        """
        now = datetime.utcnow()

        return cls(
            user_id=user_id,
            wishlist_id=str(uuid4()),
            name=name,
            is_default=is_default,
            is_public=is_public,
            share_hash=cls._generate_share_hash(),
            items=[],
            created_at=now,
            updated_at=now
        )
