"""
Database models package.

This module contains database-specific model representations and utilities
for different storage backends (DynamoDB, Redis, etc.).
"""

from .dynamodb import DynamoDBWishlist, DynamoDBWishlistItem
from .base import DatabaseModel, SerializationError, DeserializationError

__all__ = [
    'DynamoDBWishlist',
    'DynamoDBWishlistItem',
    'DatabaseModel',
    'SerializationError',
    'DeserializationError'
]