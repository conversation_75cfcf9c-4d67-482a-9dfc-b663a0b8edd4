"""
DynamoDB-specific model representations.

This module contains database models that represent the DynamoDB table structure
and provide serialization/deserialization between domain models and DynamoDB format.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
import logging

from app.models.domain.wishlist import Wishlist, WishlistItem
from .base import (
    DatabaseModel, 
    DynamoDBTypeConverter, 
    ValidationMixin,
    SerializationError,
    DeserializationError
)

logger = logging.getLogger(__name__)


@dataclass
class DynamoDBWishlistItem(DatabaseModel[WishlistItem], ValidationMixin):
    """
    DynamoDB representation of a wishlist item.
    
    Maps between the domain WishlistItem model and DynamoDB attribute format.
    """
    
    product_id: str
    notes: Optional[str] = None
    added_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    quantity: int = 1
    priority: int = 1
    
    def __post_init__(self):
        """Validate and set defaults after initialization."""
        if not self.product_id:
            raise DeserializationError("product_id is required")
        
        if self.added_at is None:
            self.added_at = datetime.utcnow()
        
        if self.updated_at is None:
            self.updated_at = self.added_at
        
        # Validate constraints
        if self.notes and len(self.notes) > 500:
            raise DeserializationError("notes cannot exceed 500 characters")
        
        if self.quantity < 1:
            raise DeserializationError("quantity must be at least 1")
        
        if self.priority < 1 or self.priority > 5:
            raise DeserializationError("priority must be between 1 and 5")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'product_id': self.product_id,
            'notes': self.notes,
            'added_at': self.added_at.isoformat() if self.added_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'quantity': self.quantity,
            'priority': self.priority
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DynamoDBWishlistItem':
        """Create from dictionary representation."""
        # Validate required fields
        if 'product_id' not in data:
            raise DeserializationError("product_id is required")
        
        return cls(
            product_id=data['product_id'],
            notes=data.get('notes'),
            added_at=cls._parse_datetime(data.get('added_at')),
            updated_at=cls._parse_datetime(data.get('updated_at')),
            quantity=data.get('quantity', 1),
            priority=data.get('priority', 1)
        )
    
    def to_dynamodb_format(self) -> Dict[str, Any]:
        """Convert to DynamoDB attribute format."""
        item_data = {
            'product_id': {'S': self.product_id},
            'added_at': {'S': self.added_at.isoformat()},
            'updated_at': {'S': self.updated_at.isoformat()},
            'quantity': {'N': str(self.quantity)},
            'priority': {'N': str(self.priority)}
        }
        
        if self.notes:
            item_data['notes'] = {'S': self.notes}
        
        return item_data
    
    @classmethod
    def from_dynamodb_format(cls, data: Dict[str, Any]) -> 'DynamoDBWishlistItem':
        """Create from DynamoDB attribute format."""
        try:
            # Convert from DynamoDB types
            converted_data = DynamoDBTypeConverter.deserialize_item(data)
            return cls.from_dict(converted_data)
        except Exception as e:
            raise DeserializationError(f"Failed to deserialize DynamoDB item: {e}")
    
    def to_domain_model(self) -> WishlistItem:
        """Convert to domain model representation."""
        return WishlistItem(
            product_id=self.product_id,
            notes=self.notes,
            added_at=self.added_at
        )
    
    @classmethod
    def from_domain_model(cls, domain_model: WishlistItem) -> 'DynamoDBWishlistItem':
        """Create from domain model representation."""
        return cls(
            product_id=domain_model.product_id,
            notes=domain_model.notes,
            added_at=domain_model.added_at or datetime.utcnow(),
            updated_at=datetime.utcnow(),
            quantity=1,  # Default values for DynamoDB-specific fields
            priority=1
        )
    
    @classmethod
    def _parse_datetime(cls, value: Union[str, datetime, None]) -> Optional[datetime]:
        """Parse datetime from various formats."""
        if value is None:
            return None
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            try:
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError as e:
                raise DeserializationError(f"Invalid datetime format: {value}")
        else:
            raise DeserializationError(f"Invalid datetime type: {type(value)}")


@dataclass
class DynamoDBWishlist(DatabaseModel[Wishlist], ValidationMixin):
    """
    DynamoDB representation of a wishlist.
    
    Maps between the domain Wishlist model and DynamoDB table structure.
    Handles composite keys, GSI attributes, and item serialization.
    """
    
    user_id: str
    wishlist_id: str
    name: str
    share_hash: str
    is_public: bool = False
    is_default: bool = False
    items: List[DynamoDBWishlistItem] = field(default_factory=list)
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    # DynamoDB-specific fields
    item_count: int = 0
    last_accessed: Optional[datetime] = None
    version: int = 1
    
    def __post_init__(self):
        """Validate and set defaults after initialization."""
        # Set timestamps if not provided
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        
        if self.updated_at is None:
            self.updated_at = self.created_at
        
        # Update item count
        self.item_count = len(self.items)
        
        # Validate required fields
        self.validate_required_fields(
            {
                'user_id': self.user_id,
                'wishlist_id': self.wishlist_id,
                'name': self.name,
                'share_hash': self.share_hash
            },
            ['user_id', 'wishlist_id', 'name', 'share_hash']
        )
        
        # Validate field types
        self.validate_field_types(
            {
                'user_id': self.user_id,
                'wishlist_id': self.wishlist_id,
                'name': self.name,
                'share_hash': self.share_hash,
                'is_public': self.is_public,
                'is_default': self.is_default,
                'item_count': self.item_count,
                'version': self.version
            },
            {
                'user_id': str,
                'wishlist_id': str,
                'name': str,
                'share_hash': str,
                'is_public': bool,
                'is_default': bool,
                'item_count': int,
                'version': int
            }
        )
        
        # Validate string lengths
        self.validate_string_length(
            {
                'name': self.name,
                'share_hash': self.share_hash
            },
            {
                'name': 255,
                'share_hash': 64
            }
        )
        
        # Additional business validations
        if len(self.share_hash) < 16:
            raise DeserializationError("share_hash must be at least 16 characters")
        
        if self.version < 1:
            raise DeserializationError("version must be at least 1")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            'user_id': self.user_id,
            'wishlist_id': self.wishlist_id,
            'name': self.name,
            'share_hash': self.share_hash,
            'is_public': self.is_public,
            'is_default': self.is_default,
            'items': [item.to_dict() for item in self.items],
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'item_count': self.item_count,
            'last_accessed': self.last_accessed.isoformat() if self.last_accessed else None,
            'version': self.version
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DynamoDBWishlist':
        """Create from dictionary representation."""
        # Parse items
        items = []
        if 'items' in data and isinstance(data['items'], list):
            for item_data in data['items']:
                if isinstance(item_data, dict):
                    items.append(DynamoDBWishlistItem.from_dict(item_data))
                else:
                    logger.warning(f"Invalid item data format: {item_data}")
        
        return cls(
            user_id=data['user_id'],
            wishlist_id=data['wishlist_id'],
            name=data['name'],
            share_hash=data['share_hash'],
            is_public=data.get('is_public', False),
            is_default=data.get('is_default', False),
            items=items,
            created_at=cls._parse_datetime(data.get('created_at')),
            updated_at=cls._parse_datetime(data.get('updated_at')),
            item_count=data.get('item_count', len(items)),
            last_accessed=cls._parse_datetime(data.get('last_accessed')),
            version=data.get('version', 1)
        )
    
    def to_dynamodb_format(self) -> Dict[str, Any]:
        """Convert to DynamoDB item format."""
        item_data = {
            'user_id': {'S': self.user_id},
            'wishlist_id': {'S': self.wishlist_id},
            'name': {'S': self.name},
            'share_hash': {'S': self.share_hash},
            'is_public': {'BOOL': self.is_public},
            'is_default': {'BOOL': self.is_default},
            'created_at': {'S': self.created_at.isoformat()},
            'updated_at': {'S': self.updated_at.isoformat()},
            'item_count': {'N': str(self.item_count)},
            'version': {'N': str(self.version)}
        }
        
        # Add optional fields
        if self.last_accessed:
            item_data['last_accessed'] = {'S': self.last_accessed.isoformat()}
        
        # Serialize items
        if self.items:
            item_data['items'] = {
                'L': [{'M': item.to_dynamodb_format()} for item in self.items]
            }
        else:
            item_data['items'] = {'L': []}
        
        return item_data
    
    @classmethod
    def from_dynamodb_format(cls, data: Dict[str, Any]) -> 'DynamoDBWishlist':
        """Create from DynamoDB item format."""
        try:
            # Convert from DynamoDB types
            converted_data = DynamoDBTypeConverter.deserialize_item(data)
            
            # Handle items separately due to nested structure
            items = []
            if 'items' in converted_data and isinstance(converted_data['items'], list):
                for item_data in converted_data['items']:
                    if isinstance(item_data, dict):
                        items.append(DynamoDBWishlistItem.from_dict(item_data))
            
            converted_data['items'] = items
            return cls.from_dict(converted_data)
            
        except Exception as e:
            raise DeserializationError(f"Failed to deserialize DynamoDB wishlist: {e}")
    
    def to_domain_model(self) -> Wishlist:
        """Convert to domain model representation."""
        domain_items = [item.to_domain_model() for item in self.items]
        
        return Wishlist(
            user_id=self.user_id,
            wishlist_id=self.wishlist_id,
            name=self.name,
            is_default=self.is_default,
            is_public=self.is_public,
            share_hash=self.share_hash,
            items=domain_items,
            created_at=self.created_at,
            updated_at=self.updated_at
        )
    
    @classmethod
    def from_domain_model(cls, domain_model: Wishlist) -> 'DynamoDBWishlist':
        """Create from domain model representation."""
        db_items = [DynamoDBWishlistItem.from_domain_model(item) for item in domain_model.items]
        
        return cls(
            user_id=domain_model.user_id,
            wishlist_id=domain_model.wishlist_id,
            name=domain_model.name,
            share_hash=domain_model.share_hash,
            is_public=domain_model.is_public,
            is_default=domain_model.is_default,
            items=db_items,
            created_at=domain_model.created_at,
            updated_at=domain_model.updated_at,
            item_count=len(db_items),
            version=1  # Default version for new items
        )
    
    def get_composite_key(self) -> Dict[str, Any]:
        """Get the DynamoDB composite key for this wishlist."""
        return {
            'user_id': {'S': self.user_id},
            'wishlist_id': {'S': self.wishlist_id}
        }
    
    def get_gsi_key(self, index_name: str) -> Dict[str, Any]:
        """Get the GSI key for the specified index."""
        if index_name == 'share_hash-index':
            return {
                'share_hash': {'S': self.share_hash}
            }
        else:
            raise ValueError(f"Unknown GSI index: {index_name}")
    
    def add_item(self, item: DynamoDBWishlistItem) -> None:
        """Add an item to the wishlist."""
        # Remove existing item with same product_id
        self.items = [existing for existing in self.items if existing.product_id != item.product_id]
        
        # Add new item
        self.items.append(item)
        self.item_count = len(self.items)
        self.updated_at = datetime.utcnow()
        self.version += 1
    
    def remove_item(self, product_id: str) -> bool:
        """Remove an item from the wishlist."""
        original_count = len(self.items)
        self.items = [item for item in self.items if item.product_id != product_id]
        
        if len(self.items) < original_count:
            self.item_count = len(self.items)
            self.updated_at = datetime.utcnow()
            self.version += 1
            return True
        return False
    
    def update_access_time(self) -> None:
        """Update the last accessed timestamp."""
        self.last_accessed = datetime.utcnow()
    
    def increment_version(self) -> None:
        """Increment the version for optimistic locking."""
        self.version += 1
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def _parse_datetime(cls, value: Union[str, datetime, None]) -> Optional[datetime]:
        """Parse datetime from various formats."""
        if value is None:
            return None
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            try:
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError as e:
                raise DeserializationError(f"Invalid datetime format: {value}")
        else:
            raise DeserializationError(f"Invalid datetime type: {type(value)}")


class DynamoDBWishlistMapper:
    """
    Mapper class for converting between domain models and DynamoDB representations.
    
    Provides high-level methods for data transformation with error handling
    and validation.
    """
    
    @staticmethod
    def domain_to_dynamodb(wishlist: Wishlist) -> DynamoDBWishlist:
        """
        Convert domain wishlist to DynamoDB representation.
        
        Args:
            wishlist: Domain wishlist model
            
        Returns:
            DynamoDB wishlist model
            
        Raises:
            SerializationError: If conversion fails
        """
        try:
            return DynamoDBWishlist.from_domain_model(wishlist)
        except Exception as e:
            raise SerializationError(f"Failed to convert domain model to DynamoDB: {e}")
    
    @staticmethod
    def dynamodb_to_domain(db_wishlist: DynamoDBWishlist) -> Wishlist:
        """
        Convert DynamoDB wishlist to domain representation.
        
        Args:
            db_wishlist: DynamoDB wishlist model
            
        Returns:
            Domain wishlist model
            
        Raises:
            DeserializationError: If conversion fails
        """
        try:
            return db_wishlist.to_domain_model()
        except Exception as e:
            raise DeserializationError(f"Failed to convert DynamoDB model to domain: {e}")
    
    @staticmethod
    def serialize_for_dynamodb(wishlist: Wishlist) -> Dict[str, Any]:
        """
        Serialize domain wishlist for DynamoDB storage.
        
        Args:
            wishlist: Domain wishlist model
            
        Returns:
            DynamoDB item format dictionary
            
        Raises:
            SerializationError: If serialization fails
        """
        try:
            db_wishlist = DynamoDBWishlist.from_domain_model(wishlist)
            return db_wishlist.to_dynamodb_format()
        except Exception as e:
            raise SerializationError(f"Failed to serialize for DynamoDB: {e}")
    
    @staticmethod
    def deserialize_from_dynamodb(item: Dict[str, Any]) -> Wishlist:
        """
        Deserialize DynamoDB item to domain wishlist.
        
        Args:
            item: DynamoDB item format dictionary
            
        Returns:
            Domain wishlist model
            
        Raises:
            DeserializationError: If deserialization fails
        """
        try:
            db_wishlist = DynamoDBWishlist.from_dynamodb_format(item)
            return db_wishlist.to_domain_model()
        except Exception as e:
            raise DeserializationError(f"Failed to deserialize from DynamoDB: {e}")
    
    @staticmethod
    def batch_serialize(wishlists: List[Wishlist]) -> List[Dict[str, Any]]:
        """
        Serialize multiple wishlists for batch operations.
        
        Args:
            wishlists: List of domain wishlist models
            
        Returns:
            List of DynamoDB item format dictionaries
            
        Raises:
            SerializationError: If any serialization fails
        """
        try:
            return [DynamoDBWishlistMapper.serialize_for_dynamodb(wishlist) for wishlist in wishlists]
        except Exception as e:
            raise SerializationError(f"Failed to batch serialize wishlists: {e}")
    
    @staticmethod
    def batch_deserialize(items: List[Dict[str, Any]]) -> List[Wishlist]:
        """
        Deserialize multiple DynamoDB items to domain wishlists.
        
        Args:
            items: List of DynamoDB item format dictionaries
            
        Returns:
            List of domain wishlist models
            
        Raises:
            DeserializationError: If any deserialization fails
        """
        try:
            return [DynamoDBWishlistMapper.deserialize_from_dynamodb(item) for item in items]
        except Exception as e:
            raise DeserializationError(f"Failed to batch deserialize wishlists: {e}")