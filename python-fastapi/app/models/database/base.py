"""
Base database model classes and utilities.

This module provides base classes and utilities for database model representations,
serialization, and validation.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, TypeVar, Generic, Optional, List, Union
from datetime import datetime
import json
import logging

logger = logging.getLogger(__name__)

T = TypeVar('T')


class SerializationError(Exception):
    """Raised when serialization to database format fails."""
    pass


class DeserializationError(Exception):
    """Raised when deserialization from database format fails."""
    pass


class DatabaseModel(ABC, Generic[T]):
    """
    Abstract base class for database model representations.
    
    Provides common functionality for serialization, deserialization,
    and validation of database-specific model formats.
    """
    
    @abstractmethod
    def to_dict(self) -> Dict[str, Any]:
        """Convert the model to a dictionary representation."""
        pass
    
    @classmethod
    @abstractmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DatabaseModel':
        """Create a model instance from a dictionary."""
        pass
    
    @abstractmethod
    def to_domain_model(self) -> T:
        """Convert to domain model representation."""
        pass
    
    @classmethod
    @abstractmethod
    def from_domain_model(cls, domain_model: T) -> 'DatabaseModel':
        """Create from domain model representation."""
        pass
    
    def validate(self) -> bool:
        """
        Validate the model data.
        
        Returns:
            True if valid, raises ValidationError if invalid
        """
        return True
    
    def to_json(self) -> str:
        """Convert the model to JSON string."""
        try:
            return json.dumps(self.to_dict(), default=self._json_serializer)
        except (TypeError, ValueError) as e:
            raise SerializationError(f"Failed to serialize to JSON: {e}")
    
    @classmethod
    def from_json(cls, json_str: str) -> 'DatabaseModel':
        """Create a model instance from JSON string."""
        try:
            data = json.loads(json_str)
            return cls.from_dict(data)
        except (json.JSONDecodeError, TypeError, ValueError) as e:
            raise DeserializationError(f"Failed to deserialize from JSON: {e}")
    
    @staticmethod
    def _json_serializer(obj):
        """Custom JSON serializer for complex types."""
        if isinstance(obj, datetime):
            return obj.isoformat()
        raise TypeError(f"Object of type {type(obj)} is not JSON serializable")


class DynamoDBTypeConverter:
    """
    Utility class for converting between Python types and DynamoDB attribute types.
    
    Handles the conversion between standard Python data types and DynamoDB's
    typed attribute format (e.g., {'S': 'string'}, {'N': '123'}, etc.).
    """
    
    @staticmethod
    def to_dynamodb_type(value: Any) -> Dict[str, Any]:
        """
        Convert a Python value to DynamoDB attribute type format.
        
        Args:
            value: Python value to convert
            
        Returns:
            DynamoDB attribute type dictionary
            
        Raises:
            SerializationError: If the value type is not supported
        """
        if value is None:
            return {'NULL': True}
        elif isinstance(value, bool):
            return {'BOOL': value}
        elif isinstance(value, str):
            return {'S': value}
        elif isinstance(value, (int, float)):
            return {'N': str(value)}
        elif isinstance(value, bytes):
            return {'B': value}
        elif isinstance(value, list):
            if not value:  # Empty list
                return {'L': []}
            # Check if all items are the same type for sets
            first_type = type(value[0])
            if all(isinstance(item, first_type) for item in value):
                if isinstance(value[0], str):
                    return {'SS': value}
                elif isinstance(value[0], (int, float)):
                    return {'NS': [str(item) for item in value]}
                elif isinstance(value[0], bytes):
                    return {'BS': value}
            # Mixed types or complex objects - use list
            return {'L': [DynamoDBTypeConverter.to_dynamodb_type(item) for item in value]}
        elif isinstance(value, dict):
            return {'M': {k: DynamoDBTypeConverter.to_dynamodb_type(v) for k, v in value.items()}}
        elif isinstance(value, datetime):
            return {'S': value.isoformat()}
        else:
            raise SerializationError(f"Unsupported type for DynamoDB: {type(value)}")
    
    @staticmethod
    def from_dynamodb_type(attr: Dict[str, Any]) -> Any:
        """
        Convert a DynamoDB attribute type to Python value.
        
        Args:
            attr: DynamoDB attribute type dictionary
            
        Returns:
            Python value
            
        Raises:
            DeserializationError: If the attribute type is not recognized
        """
        if 'NULL' in attr:
            return None
        elif 'BOOL' in attr:
            return attr['BOOL']
        elif 'S' in attr:
            return attr['S']
        elif 'N' in attr:
            # Try to convert to int first, then float
            try:
                return int(attr['N'])
            except ValueError:
                return float(attr['N'])
        elif 'B' in attr:
            return attr['B']
        elif 'SS' in attr:
            return attr['SS']
        elif 'NS' in attr:
            return [int(n) if '.' not in n else float(n) for n in attr['NS']]
        elif 'BS' in attr:
            return attr['BS']
        elif 'L' in attr:
            return [DynamoDBTypeConverter.from_dynamodb_type(item) for item in attr['L']]
        elif 'M' in attr:
            return {k: DynamoDBTypeConverter.from_dynamodb_type(v) for k, v in attr['M'].items()}
        else:
            raise DeserializationError(f"Unknown DynamoDB attribute type: {attr}")
    
    @staticmethod
    def serialize_item(item: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """
        Serialize a complete item to DynamoDB format.
        
        Args:
            item: Dictionary with Python values
            
        Returns:
            Dictionary with DynamoDB attribute types
        """
        return {k: DynamoDBTypeConverter.to_dynamodb_type(v) for k, v in item.items()}
    
    @staticmethod
    def deserialize_item(item: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Deserialize a complete item from DynamoDB format.
        
        Args:
            item: Dictionary with DynamoDB attribute types
            
        Returns:
            Dictionary with Python values
        """
        return {k: DynamoDBTypeConverter.from_dynamodb_type(v) for k, v in item.items()}


class ValidationMixin:
    """
    Mixin class providing common validation utilities for database models.
    """
    
    def validate_required_fields(self, data: Dict[str, Any], required_fields: List[str]) -> None:
        """
        Validate that all required fields are present and not None.
        
        Args:
            data: Data dictionary to validate
            required_fields: List of required field names
            
        Raises:
            DeserializationError: If any required field is missing or None
        """
        missing_fields = []
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            raise DeserializationError(f"Missing required fields: {', '.join(missing_fields)}")
    
    def validate_field_types(self, data: Dict[str, Any], field_types: Dict[str, type]) -> None:
        """
        Validate that fields have the expected types.
        
        Args:
            data: Data dictionary to validate
            field_types: Dictionary mapping field names to expected types
            
        Raises:
            DeserializationError: If any field has an incorrect type
        """
        type_errors = []
        for field, expected_type in field_types.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_type):
                    type_errors.append(f"{field}: expected {expected_type.__name__}, got {type(data[field]).__name__}")
        
        if type_errors:
            raise DeserializationError(f"Type validation errors: {'; '.join(type_errors)}")
    
    def validate_string_length(self, data: Dict[str, Any], field_limits: Dict[str, int]) -> None:
        """
        Validate string field lengths.
        
        Args:
            data: Data dictionary to validate
            field_limits: Dictionary mapping field names to maximum lengths
            
        Raises:
            DeserializationError: If any string field exceeds its limit
        """
        length_errors = []
        for field, max_length in field_limits.items():
            if field in data and isinstance(data[field], str):
                if len(data[field]) > max_length:
                    length_errors.append(f"{field}: length {len(data[field])} exceeds maximum {max_length}")
        
        if length_errors:
            raise DeserializationError(f"String length validation errors: {'; '.join(length_errors)}")
    
    def parse_datetime(self, value: Union[str, datetime, None]) -> Optional[datetime]:
        """
        Parse a datetime value from various formats.
        
        Args:
            value: Value to parse (string, datetime, or None)
            
        Returns:
            Parsed datetime or None
            
        Raises:
            DeserializationError: If the datetime string is invalid
        """
        if value is None:
            return None
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            try:
                # Try ISO format first
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError:
                try:
                    # Try common formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%dT%H:%M:%S']:
                        try:
                            return datetime.strptime(value, fmt)
                        except ValueError:
                            continue
                    raise ValueError("No matching format found")
                except ValueError as e:
                    raise DeserializationError(f"Invalid datetime format: {value}")
        else:
            raise DeserializationError(f"Invalid datetime type: {type(value)}")


class DataTransformationPipeline:
    """
    Pipeline for data transformation with validation and error aggregation.
    
    Provides a structured way to apply multiple transformation and validation
    steps with comprehensive error reporting.
    """
    
    def __init__(self):
        self.steps = []
        self.errors = []
    
    def add_step(self, step_name: str, step_func, *args, **kwargs):
        """
        Add a transformation/validation step to the pipeline.
        
        Args:
            step_name: Name of the step for error reporting
            step_func: Function to execute
            *args, **kwargs: Arguments to pass to the function
        """
        self.steps.append((step_name, step_func, args, kwargs))
    
    def execute(self, data: Any) -> Any:
        """
        Execute all pipeline steps.
        
        Args:
            data: Input data to transform
            
        Returns:
            Transformed data
            
        Raises:
            DeserializationError: If any step fails (with aggregated errors)
        """
        self.errors = []
        current_data = data
        
        for step_name, step_func, args, kwargs in self.steps:
            try:
                current_data = step_func(current_data, *args, **kwargs)
            except Exception as e:
                self.errors.append(f"{step_name}: {str(e)}")
        
        if self.errors:
            raise DeserializationError(f"Pipeline validation failed: {'; '.join(self.errors)}")
        
        return current_data
    
    def has_errors(self) -> bool:
        """Check if the pipeline has any errors."""
        return len(self.errors) > 0
    
    def get_errors(self) -> List[str]:
        """Get all pipeline errors."""
        return self.errors.copy()