"""
Data migration utilities for schema evolution and data consistency validation.

This module provides tools for migrating data between different schema versions,
validating data consistency, and handling schema evolution scenarios.
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple, Callable, Union
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum
import json
import hashlib

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.models.database.dynamodb import DynamoDBWishlist, DynamoDBWishlistMapper
from app.utils.transformers import WishlistTransformationService
from app.core.exceptions import ValidationError, TransformationError

logger = logging.getLogger(__name__)


class MigrationStatus(Enum):
    """Status of a migration operation."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    ROLLED_BACK = "rolled_back"


class ValidationResult(Enum):
    """Result of data validation."""
    VALID = "valid"
    INVALID = "invalid"
    MISSING = "missing"
    CORRUPTED = "corrupted"


@dataclass
class DataDifference:
    """Represents a difference between two data items."""
    field_name: str
    source_value: Any
    target_value: Any
    difference_type: str  # 'missing', 'different', 'extra'
    severity: str = 'medium'  # 'low', 'medium', 'high', 'critical'


@dataclass
class ValidationReport:
    """Report of data validation results."""
    item_id: str
    status: ValidationResult
    differences: List[DataDifference] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_valid(self) -> bool:
        """Check if the item is valid."""
        return self.status == ValidationResult.VALID and not self.errors
    
    def has_critical_issues(self) -> bool:
        """Check if there are critical issues."""
        return any(diff.severity == 'critical' for diff in self.differences) or bool(self.errors)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get a summary of the validation report."""
        return {
            'item_id': self.item_id,
            'status': self.status.value,
            'difference_count': len(self.differences),
            'error_count': len(self.errors),
            'warning_count': len(self.warnings),
            'has_critical_issues': self.has_critical_issues()
        }


@dataclass
class MigrationPlan:
    """Plan for data migration operations."""
    migration_id: str
    source_format: str
    target_format: str
    batch_size: int = 100
    parallel_workers: int = 5
    validation_enabled: bool = True
    rollback_enabled: bool = True
    dry_run: bool = False
    transformations: List[Callable] = field(default_factory=list)
    validators: List[Callable] = field(default_factory=list)
    
    def add_transformation(self, transformation: Callable) -> 'MigrationPlan':
        """Add a transformation step to the migration."""
        self.transformations.append(transformation)
        return self
    
    def add_validator(self, validator: Callable) -> 'MigrationPlan':
        """Add a validation step to the migration."""
        self.validators.append(validator)
        return self


class DataComparator:
    """
    Utility for comparing data between different representations.
    
    Provides methods for deep comparison of data structures with
    detailed difference reporting.
    """
    
    @staticmethod
    def compare_wishlists(source: Wishlist, target: Wishlist) -> List[DataDifference]:
        """
        Compare two wishlist objects and return differences.
        
        Args:
            source: Source wishlist
            target: Target wishlist
            
        Returns:
            List of differences found
        """
        differences = []
        
        # Compare basic fields
        basic_fields = [
            'user_id', 'wishlist_id', 'name', 'is_default', 
            'is_public', 'share_hash'
        ]
        
        for field in basic_fields:
            source_val = getattr(source, field, None)
            target_val = getattr(target, field, None)
            
            if source_val != target_val:
                differences.append(DataDifference(
                    field_name=field,
                    source_value=source_val,
                    target_value=target_val,
                    difference_type='different',
                    severity='high' if field in ['user_id', 'wishlist_id'] else 'medium'
                ))
        
        # Compare timestamps (with tolerance for minor differences)
        timestamp_fields = ['created_at', 'updated_at']
        for field in timestamp_fields:
            source_val = getattr(source, field, None)
            target_val = getattr(target, field, None)
            
            if source_val and target_val:
                # Allow 1 second tolerance for timestamp differences
                time_diff = abs((source_val - target_val).total_seconds())
                if time_diff > 1:
                    differences.append(DataDifference(
                        field_name=field,
                        source_value=source_val,
                        target_value=target_val,
                        difference_type='different',
                        severity='low'
                    ))
            elif source_val != target_val:
                differences.append(DataDifference(
                    field_name=field,
                    source_value=source_val,
                    target_value=target_val,
                    difference_type='missing' if not target_val else 'extra',
                    severity='medium'
                ))
        
        # Compare items
        item_differences = DataComparator.compare_wishlist_items(source.items, target.items)
        differences.extend(item_differences)
        
        return differences
    
    @staticmethod
    def compare_wishlist_items(source_items: List[WishlistItem], target_items: List[WishlistItem]) -> List[DataDifference]:
        """Compare wishlist items between two lists."""
        differences = []
        
        # Create dictionaries for easier comparison
        source_dict = {item.product_id: item for item in source_items}
        target_dict = {item.product_id: item for item in target_items}
        
        # Check for missing items in target
        for product_id, source_item in source_dict.items():
            if product_id not in target_dict:
                differences.append(DataDifference(
                    field_name=f'items.{product_id}',
                    source_value=source_item,
                    target_value=None,
                    difference_type='missing',
                    severity='high'
                ))
            else:
                # Compare individual items
                target_item = target_dict[product_id]
                item_diffs = DataComparator.compare_individual_items(source_item, target_item, product_id)
                differences.extend(item_diffs)
        
        # Check for extra items in target
        for product_id, target_item in target_dict.items():
            if product_id not in source_dict:
                differences.append(DataDifference(
                    field_name=f'items.{product_id}',
                    source_value=None,
                    target_value=target_item,
                    difference_type='extra',
                    severity='medium'
                ))
        
        return differences
    
    @staticmethod
    def compare_individual_items(source: WishlistItem, target: WishlistItem, product_id: str) -> List[DataDifference]:
        """Compare individual wishlist items."""
        differences = []
        
        # Compare notes
        if source.notes != target.notes:
            differences.append(DataDifference(
                field_name=f'items.{product_id}.notes',
                source_value=source.notes,
                target_value=target.notes,
                difference_type='different',
                severity='low'
            ))
        
        # Compare timestamps (with tolerance)
        if source.added_at and target.added_at:
            time_diff = abs((source.added_at - target.added_at).total_seconds())
            if time_diff > 1:
                differences.append(DataDifference(
                    field_name=f'items.{product_id}.added_at',
                    source_value=source.added_at,
                    target_value=target.added_at,
                    difference_type='different',
                    severity='low'
                ))
        elif source.added_at != target.added_at:
            differences.append(DataDifference(
                field_name=f'items.{product_id}.added_at',
                source_value=source.added_at,
                target_value=target.added_at,
                difference_type='missing' if not target.added_at else 'extra',
                severity='medium'
            ))
        
        return differences


class DataValidator:
    """
    Validator for ensuring data integrity and consistency.
    """
    
    def __init__(self, transformation_service: WishlistTransformationService):
        self.transformation_service = transformation_service
    
    async def validate_wishlist_consistency(
        self, 
        source_data: Dict[str, Any], 
        target_data: Dict[str, Any]
    ) -> ValidationReport:
        """
        Validate consistency between source and target wishlist data.
        
        Args:
            source_data: Source data (e.g., from Laravel/MySQL)
            target_data: Target data (e.g., from FastAPI/DynamoDB)
            
        Returns:
            Validation report with differences and issues
        """
        try:
            # Extract identifier for reporting
            item_id = source_data.get('wishlist_id', 'unknown')
            
            # Convert both to domain models for comparison
            source_wishlist = self._convert_to_domain_model(source_data, 'source')
            target_wishlist = self._convert_to_domain_model(target_data, 'target')
            
            # Compare the domain models
            differences = DataComparator.compare_wishlists(source_wishlist, target_wishlist)
            
            # Determine validation status
            status = ValidationResult.VALID
            errors = []
            warnings = []
            
            if differences:
                critical_diffs = [d for d in differences if d.severity == 'critical']
                high_diffs = [d for d in differences if d.severity == 'high']
                
                if critical_diffs:
                    status = ValidationResult.CORRUPTED
                    errors.append(f"Found {len(critical_diffs)} critical differences")
                elif high_diffs:
                    status = ValidationResult.INVALID
                    warnings.append(f"Found {len(high_diffs)} high-severity differences")
                else:
                    warnings.append(f"Found {len(differences)} minor differences")
            
            return ValidationReport(
                item_id=item_id,
                status=status,
                differences=differences,
                errors=errors,
                warnings=warnings,
                metadata={
                    'source_format': 'laravel',
                    'target_format': 'fastapi',
                    'validation_timestamp': datetime.utcnow().isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Validation failed for item {source_data.get('wishlist_id', 'unknown')}: {e}")
            return ValidationReport(
                item_id=source_data.get('wishlist_id', 'unknown'),
                status=ValidationResult.CORRUPTED,
                errors=[f"Validation error: {str(e)}"]
            )
    
    def _convert_to_domain_model(self, data: Dict[str, Any], source_type: str) -> Wishlist:
        """Convert data to domain model based on source type."""
        try:
            if source_type == 'source':
                # Convert from Laravel/MySQL format
                return self._convert_laravel_to_domain(data)
            elif source_type == 'target':
                # Convert from FastAPI/DynamoDB format
                return self.transformation_service.database_to_domain(data)
            else:
                raise ValueError(f"Unknown source type: {source_type}")
        except Exception as e:
            raise TransformationError(f"Failed to convert {source_type} data to domain model: {e}")
    
    def _convert_laravel_to_domain(self, laravel_data: Dict[str, Any]) -> Wishlist:
        """Convert Laravel/MySQL data format to domain model."""
        # This would need to be implemented based on the actual Laravel schema
        # For now, providing a basic implementation
        try:
            # Parse items (assuming JSON format in Laravel)
            items = []
            if 'items' in laravel_data:
                items_data = laravel_data['items']
                if isinstance(items_data, str):
                    items_data = json.loads(items_data)
                
                for item_data in items_data:
                    items.append(WishlistItem(
                        product_id=item_data.get('product_id', ''),
                        notes=item_data.get('notes'),
                        added_at=self._parse_datetime(item_data.get('added_at'))
                    ))
            
            return Wishlist(
                user_id=str(laravel_data.get('user_id', '')),
                wishlist_id=str(laravel_data.get('id', '')),  # Laravel uses 'id'
                name=laravel_data.get('name', ''),
                is_default=bool(laravel_data.get('is_default', False)),
                is_public=bool(laravel_data.get('is_public', False)),
                share_hash=laravel_data.get('share_hash', ''),
                items=items,
                created_at=self._parse_datetime(laravel_data.get('created_at')),
                updated_at=self._parse_datetime(laravel_data.get('updated_at'))
            )
        except Exception as e:
            raise TransformationError(f"Failed to convert Laravel data: {e}")
    
    def _parse_datetime(self, value: Union[str, datetime, None]) -> Optional[datetime]:
        """Parse datetime from various formats."""
        if value is None:
            return None
        elif isinstance(value, datetime):
            return value
        elif isinstance(value, str):
            try:
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            except ValueError:
                try:
                    return datetime.strptime(value, '%Y-%m-%d %H:%M:%S')
                except ValueError:
                    logger.warning(f"Could not parse datetime: {value}")
                    return None
        else:
            return None


class DataMigrationEngine:
    """
    Engine for executing data migration operations.
    
    Provides batch processing, parallel execution, validation,
    and rollback capabilities for data migrations.
    """
    
    def __init__(self, transformation_service: WishlistTransformationService):
        self.transformation_service = transformation_service
        self.validator = DataValidator(transformation_service)
    
    async def execute_migration(
        self, 
        plan: MigrationPlan,
        source_data: List[Dict[str, Any]],
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> Dict[str, Any]:
        """
        Execute a data migration according to the provided plan.
        
        Args:
            plan: Migration plan with configuration
            source_data: List of source data items to migrate
            progress_callback: Optional callback for progress updates
            
        Returns:
            Migration results summary
        """
        logger.info(f"Starting migration {plan.migration_id} with {len(source_data)} items")
        
        results = {
            'migration_id': plan.migration_id,
            'status': MigrationStatus.IN_PROGRESS,
            'total_items': len(source_data),
            'processed_items': 0,
            'successful_items': 0,
            'failed_items': 0,
            'validation_reports': [],
            'errors': [],
            'start_time': datetime.utcnow(),
            'end_time': None
        }
        
        try:
            # Process data in batches
            batches = self._create_batches(source_data, plan.batch_size)
            
            for batch_idx, batch in enumerate(batches):
                batch_results = await self._process_batch(plan, batch, batch_idx)
                
                # Update results
                results['processed_items'] += len(batch)
                results['successful_items'] += batch_results['successful']
                results['failed_items'] += batch_results['failed']
                results['validation_reports'].extend(batch_results['validation_reports'])
                results['errors'].extend(batch_results['errors'])
                
                # Call progress callback
                if progress_callback:
                    progress_callback(results['processed_items'], results['total_items'])
                
                logger.info(f"Processed batch {batch_idx + 1}/{len(batches)}")
            
            # Determine final status
            if results['failed_items'] == 0:
                results['status'] = MigrationStatus.COMPLETED
            elif results['successful_items'] > 0:
                results['status'] = MigrationStatus.COMPLETED  # Partial success
            else:
                results['status'] = MigrationStatus.FAILED
            
            results['end_time'] = datetime.utcnow()
            results['duration'] = (results['end_time'] - results['start_time']).total_seconds()
            
            logger.info(f"Migration {plan.migration_id} completed: {results['successful_items']}/{results['total_items']} successful")
            
            return results
            
        except Exception as e:
            logger.error(f"Migration {plan.migration_id} failed: {e}")
            results['status'] = MigrationStatus.FAILED
            results['errors'].append(f"Migration failed: {str(e)}")
            results['end_time'] = datetime.utcnow()
            return results
    
    async def _process_batch(
        self, 
        plan: MigrationPlan, 
        batch: List[Dict[str, Any]], 
        batch_idx: int
    ) -> Dict[str, Any]:
        """Process a single batch of data."""
        batch_results = {
            'successful': 0,
            'failed': 0,
            'validation_reports': [],
            'errors': []
        }
        
        # Process items in parallel if configured
        if plan.parallel_workers > 1:
            semaphore = asyncio.Semaphore(plan.parallel_workers)
            tasks = [
                self._process_item_with_semaphore(semaphore, plan, item, idx)
                for idx, item in enumerate(batch)
            ]
            results = await asyncio.gather(*tasks, return_exceptions=True)
        else:
            results = []
            for idx, item in enumerate(batch):
                result = await self._process_single_item(plan, item, idx)
                results.append(result)
        
        # Aggregate results
        for result in results:
            if isinstance(result, Exception):
                batch_results['failed'] += 1
                batch_results['errors'].append(str(result))
            elif result['success']:
                batch_results['successful'] += 1
                if result.get('validation_report'):
                    batch_results['validation_reports'].append(result['validation_report'])
            else:
                batch_results['failed'] += 1
                batch_results['errors'].extend(result.get('errors', []))
        
        return batch_results
    
    async def _process_item_with_semaphore(
        self, 
        semaphore: asyncio.Semaphore, 
        plan: MigrationPlan, 
        item: Dict[str, Any], 
        idx: int
    ) -> Dict[str, Any]:
        """Process a single item with semaphore for concurrency control."""
        async with semaphore:
            return await self._process_single_item(plan, item, idx)
    
    async def _process_single_item(
        self, 
        plan: MigrationPlan, 
        item: Dict[str, Any], 
        idx: int
    ) -> Dict[str, Any]:
        """Process a single data item."""
        try:
            # Apply transformations
            transformed_item = item
            for transformation in plan.transformations:
                transformed_item = transformation(transformed_item)
            
            # Run validators if enabled
            validation_report = None
            if plan.validation_enabled and plan.validators:
                for validator in plan.validators:
                    validation_result = await validator(item, transformed_item)
                    if validation_report is None:
                        validation_report = validation_result
            
            # Check if this is a dry run
            if plan.dry_run:
                logger.debug(f"Dry run: would migrate item {idx}")
            else:
                # Perform actual migration (this would be implemented based on target system)
                logger.debug(f"Migrating item {idx}")
            
            return {
                'success': True,
                'item_id': item.get('id', idx),
                'validation_report': validation_report
            }
            
        except Exception as e:
            logger.error(f"Failed to process item {idx}: {e}")
            return {
                'success': False,
                'item_id': item.get('id', idx),
                'errors': [str(e)]
            }
    
    def _create_batches(self, data: List[Dict[str, Any]], batch_size: int) -> List[List[Dict[str, Any]]]:
        """Split data into batches."""
        batches = []
        for i in range(0, len(data), batch_size):
            batches.append(data[i:i + batch_size])
        return batches
    
    def generate_migration_checksum(self, data: List[Dict[str, Any]]) -> str:
        """Generate a checksum for migration data integrity verification."""
        # Create a deterministic representation of the data
        data_str = json.dumps(data, sort_keys=True, default=str)
        return hashlib.sha256(data_str.encode()).hexdigest()


class SchemaEvolutionManager:
    """
    Manager for handling schema evolution and version compatibility.
    """
    
    def __init__(self):
        self.version_handlers = {}
    
    def register_version_handler(self, from_version: str, to_version: str, handler: Callable):
        """Register a handler for migrating between specific versions."""
        key = f"{from_version}->{to_version}"
        self.version_handlers[key] = handler
    
    def migrate_schema_version(
        self, 
        data: Dict[str, Any], 
        from_version: str, 
        to_version: str
    ) -> Dict[str, Any]:
        """Migrate data from one schema version to another."""
        key = f"{from_version}->{to_version}"
        
        if key not in self.version_handlers:
            raise ValueError(f"No handler registered for migration {key}")
        
        handler = self.version_handlers[key]
        return handler(data)
    
    def get_supported_migrations(self) -> List[str]:
        """Get list of supported migration paths."""
        return list(self.version_handlers.keys())