"""
Request validation utilities for comprehensive input validation.

This module provides utilities for validating API requests beyond basic
Pydantic validation, including business rules and cross-field validation.
"""
import re
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from pydantic import ValidationError

from app.core.exceptions import ValidationError as CustomValidationError


class RequestValidator:
    """
    Utility class for advanced request validation.
    """

    @staticmethod
    def validate_user_id(user_id: str) -> str:
        """
        Validate user ID format and constraints.

        Args:
            user_id: User ID to validate

        Returns:
            Validated user ID

        Raises:
            CustomValidationError: If validation fails
        """
        if not user_id or not user_id.strip():
            raise CustomValidationError("user_id cannot be empty")

        user_id = user_id.strip()

        if len(user_id) < 3:
            raise CustomValidationError("user_id must be at least 3 characters long")

        if len(user_id) > 50:
            raise CustomValidationError("user_id cannot exceed 50 characters")

        # Allow alphanumeric, hyphens, and underscores
        if not re.match(r'^[a-zA-Z0-9_-]+$', user_id):
            raise CustomValidationError("user_id contains invalid characters")

        return user_id

    @staticmethod
    def validate_product_id(product_id: str) -> str:
        """
        Validate product ID format and constraints.

        Args:
            product_id: Product ID to validate

        Returns:
            Validated product ID

        Raises:
            CustomValidationError: If validation fails
        """
        if not product_id or not product_id.strip():
            raise CustomValidationError("product_id cannot be empty")

        product_id = product_id.strip()

        if len(product_id) > 100:
            raise CustomValidationError("product_id cannot exceed 100 characters")

        # Basic format validation - adjust based on your product ID format
        if not re.match(r'^[a-zA-Z0-9_-]+$', product_id):
            raise CustomValidationError("product_id contains invalid characters")

        return product_id

    @staticmethod
    def validate_wishlist_name(name: str) -> str:
        """
        Validate wishlist name format and constraints.

        Args:
            name: Wishlist name to validate

        Returns:
            Validated wishlist name

        Raises:
            CustomValidationError: If validation fails
        """
        if not name or not name.strip():
            raise CustomValidationError("name cannot be empty")

        name = name.strip()

        if len(name) > 255:
            raise CustomValidationError("name cannot exceed 255 characters")

        # Check for potentially harmful content
        if any(char in name for char in ['<', '>', '&', '"', "'"]):
            raise CustomValidationError("name contains invalid characters")

        return name

    @staticmethod
    def validate_notes(notes: Optional[str]) -> Optional[str]:
        """
        Validate item notes format and constraints.

        Args:
            notes: Notes to validate

        Returns:
            Validated notes or None

        Raises:
            CustomValidationError: If validation fails
        """
        if notes is None:
            return None

        notes = notes.strip()

        if not notes:
            return None

        if len(notes) > 500:
            raise CustomValidationError("notes cannot exceed 500 characters")

        return notes

    @staticmethod
    def validate_share_hash(share_hash: str) -> str:
        """
        Validate share hash format and constraints.

        Args:
            share_hash: Share hash to validate

        Returns:
            Validated share hash

        Raises:
            CustomValidationError: If validation fails
        """
        if not share_hash or not share_hash.strip():
            raise CustomValidationError("share_hash cannot be empty")

        share_hash = share_hash.strip()

        if len(share_hash) < 16:
            raise CustomValidationError("share_hash must be at least 16 characters long")

        if len(share_hash) > 64:
            raise CustomValidationError("share_hash cannot exceed 64 characters")

        # Should be alphanumeric
        if not re.match(r'^[a-zA-Z0-9]+$', share_hash):
            raise CustomValidationError("share_hash contains invalid characters")

        return share_hash

    @staticmethod
    def validate_country_code(country: str) -> str:
        """
        Validate country code format and constraints.

        Args:
            country: Country code to validate

        Returns:
            Validated country code

        Raises:
            CustomValidationError: If validation fails
        """
        if not country:
            return "ae"  # Default to UAE

        country = country.lower().strip()

        # Map common variations
        country_mapping = {
            'uae': 'ae',
            'united_arab_emirates': 'ae',
            'saudi': 'sa',
            'ksa': 'sa',
            'saudi_arabia': 'sa',
        }
        country = country_mapping.get(country, country)

        valid_countries = {"ae", "sa", "kw", "bh", "om", "qa"}
        if country not in valid_countries:
            raise CustomValidationError(
                f"Invalid country code '{country}'. "
                f"Supported countries: {', '.join(valid_countries)}"
            )

        return country

    @staticmethod
    def validate_language_code(language: str) -> str:
        """
        Validate language code format and constraints.

        Args:
            language: Language code to validate

        Returns:
            Validated language code

        Raises:
            CustomValidationError: If validation fails
        """
        if not language:
            return "en"  # Default to English

        language = language.lower().strip()

        # Map common variations
        language_mapping = {
            'eng': 'en',
            'english': 'en',
            'ara': 'ar',
            'arabic': 'ar',
        }
        language = language_mapping.get(language, language)

        valid_languages = {"en", "ar"}
        if language not in valid_languages:
            raise CustomValidationError(
                f"Invalid language code '{language}'. "
                f"Supported languages: {', '.join(valid_languages)}"
            )

        return language

    @staticmethod
    def validate_pagination(page: int, page_size: int) -> tuple[int, int]:
        """
        Validate pagination parameters.

        Args:
            page: Page number
            page_size: Number of items per page

        Returns:
            Tuple of validated (page, page_size)

        Raises:
            CustomValidationError: If validation fails
        """
        if page < 1:
            raise CustomValidationError("page must be greater than 0")

        if page_size < 1:
            raise CustomValidationError("page_size must be greater than 0")

        if page_size > 100:
            raise CustomValidationError("page_size cannot exceed 100")

        return page, page_size

    @staticmethod
    def validate_sort_parameters(sort_by: str, sort_order: str) -> tuple[str, str]:
        """
        Validate sorting parameters.

        Args:
            sort_by: Field to sort by
            sort_order: Sort order (asc/desc)

        Returns:
            Tuple of validated (sort_by, sort_order)

        Raises:
            CustomValidationError: If validation fails
        """
        valid_sort_fields = ["name", "created_at", "updated_at", "item_count"]
        if sort_by not in valid_sort_fields:
            raise CustomValidationError(
                f"sort_by must be one of: {', '.join(valid_sort_fields)}"
            )

        sort_order = sort_order.lower()
        if sort_order not in ["asc", "desc"]:
            raise CustomValidationError("sort_order must be 'asc' or 'desc'")

        return sort_by, sort_order

    @staticmethod
    def validate_bulk_operation_size(items: List[Any], max_size: int = 50) -> List[Any]:
        """
        Validate bulk operation size constraints.

        Args:
            items: List of items for bulk operation
            max_size: Maximum allowed size

        Returns:
            Validated items list

        Raises:
            CustomValidationError: If validation fails
        """
        if not items:
            raise CustomValidationError("items list cannot be empty")

        if len(items) > max_size:
            raise CustomValidationError(f"cannot process more than {max_size} items at once")

        return items

    @staticmethod
    def validate_product_ids_unique(product_ids: List[str]) -> List[str]:
        """
        Validate that product IDs in a list are unique.

        Args:
            product_ids: List of product IDs

        Returns:
            Validated product IDs list

        Raises:
            CustomValidationError: If validation fails
        """
        if len(product_ids) != len(set(product_ids)):
            raise CustomValidationError("duplicate product_ids found in request")

        return product_ids

    @staticmethod
    def validate_quantity(quantity: Optional[int]) -> int:
        """
        Validate quantity value.

        Args:
            quantity: Quantity to validate

        Returns:
            Validated quantity

        Raises:
            CustomValidationError: If validation fails
        """
        if quantity is None:
            return 1

        if quantity < 1 or quantity > 99:
            raise CustomValidationError("quantity must be between 1 and 99")

        return quantity

    @staticmethod
    def validate_priority(priority: Optional[int]) -> int:
        """
        Validate priority value.

        Args:
            priority: Priority to validate

        Returns:
            Validated priority

        Raises:
            CustomValidationError: If validation fails
        """
        if priority is None:
            return 1

        if priority < 1 or priority > 5:
            raise CustomValidationError("priority must be between 1 and 5")

        return priority

    @staticmethod
    def validate_request_size(content_length: Optional[int], max_size: int = 10 * 1024 * 1024) -> None:
        """
        Validate request content size.

        Args:
            content_length: Content length in bytes
            max_size: Maximum allowed size in bytes (default 10MB)

        Raises:
            CustomValidationError: If validation fails
        """
        if content_length and content_length > max_size:
            raise CustomValidationError(
                f"Request too large. Maximum size is {max_size // (1024 * 1024)}MB"
            )

    @staticmethod
    def sanitize_input(value: str) -> str:
        """
        Sanitize input string to prevent injection attacks.

        Args:
            value: Input string to sanitize

        Returns:
            Sanitized string
        """
        if not value:
            return value

        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '&', '"', "'", '\\', '/', '\x00']
        for char in dangerous_chars:
            value = value.replace(char, '')

        # Limit length to prevent DoS
        if len(value) > 1000:
            value = value[:1000]

        return value.strip()

    @staticmethod
    def validate_api_version(version: str) -> str:
        """
        Validate API version format.

        Args:
            version: API version string

        Returns:
            Validated version

        Raises:
            CustomValidationError: If validation fails
        """
        if not version:
            return "v1"  # Default version

        if not re.match(r'^v\d+$', version):
            raise CustomValidationError("Invalid API version format. Expected format: v1, v2, etc.")

        supported_versions = ["v1"]
        if version not in supported_versions:
            raise CustomValidationError(
                f"Unsupported API version '{version}'. "
                f"Supported versions: {', '.join(supported_versions)}"
            )

        return version


def validate_request_headers(headers: Dict[str, str]) -> Dict[str, str]:
    """
    Validate common request headers.

    Args:
        headers: Request headers dictionary

    Returns:
        Dictionary of validated headers

    Raises:
        CustomValidationError: If validation fails
    """
    validated_headers = {}

    # Validate Content-Type for POST/PUT requests
    content_type = headers.get("content-type", "").lower()
    if content_type and not content_type.startswith("application/json"):
        if "multipart/form-data" not in content_type:
            raise CustomValidationError("Unsupported Content-Type. Expected application/json")

    # Validate User-Agent
    user_agent = headers.get("user-agent", "")
    if user_agent and len(user_agent) > 500:
        raise CustomValidationError("User-Agent header too long")

    # Validate Accept header
    accept = headers.get("accept", "")
    if accept and "application/json" not in accept and "*/*" not in accept:
        raise CustomValidationError("Unsupported Accept header. Expected application/json")

    return validated_headers


def validate_business_rules(data: Dict[str, Any], operation: str) -> None:
    """
    Validate business-specific rules.

    Args:
        data: Request data dictionary
        operation: Type of operation (create, update, delete, etc.)

    Raises:
        CustomValidationError: If business rules are violated
    """
    if operation == "create_wishlist":
        # Business rule: User can have maximum 10 wishlists
        # This would be checked against the database in the service layer
        pass

    elif operation == "add_item":
        # Business rule: Wishlist can have maximum 100 items
        # This would be checked against the database in the service layer
        pass

    elif operation == "bulk_add_items":
        # Business rule: Bulk operations limited to 50 items
        items = data.get("items", [])
        if len(items) > 50:
            raise CustomValidationError("Cannot add more than 50 items in a single operation")

    elif operation == "update_privacy":
        # Business rule: Only wishlist owner can change privacy
        # This would be checked in the service layer with user authentication
        pass