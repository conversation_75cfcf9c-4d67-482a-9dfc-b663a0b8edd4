"""
API versioning utilities for backward compatibility and version management.

This module provides utilities for handling API versioning, deprecation warnings,
and backward compatibility support.
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse

from app.utils.helpers import generate_correlation_id


class APIVersion:
    """
    Represents an API version with metadata.
    """

    def __init__(
        self,
        version: str,
        release_date: datetime,
        deprecation_date: Optional[datetime] = None,
        sunset_date: Optional[datetime] = None,
        supported: bool = True,
        description: str = ""
    ):
        self.version = version
        self.release_date = release_date
        self.deprecation_date = deprecation_date
        self.sunset_date = sunset_date
        self.supported = supported
        self.description = description

    @property
    def is_deprecated(self) -> bool:
        """Check if this version is deprecated."""
        if not self.deprecation_date:
            return False
        return datetime.utcnow() >= self.deprecation_date

    @property
    def is_sunset(self) -> bool:
        """Check if this version has reached sunset (no longer supported)."""
        if not self.sunset_date:
            return False
        return datetime.utcnow() >= self.sunset_date

    @property
    def days_until_sunset(self) -> Optional[int]:
        """Calculate days until sunset."""
        if not self.sunset_date:
            return None
        delta = self.sunset_date - datetime.utcnow()
        return max(0, delta.days)


class APIVersionManager:
    """
    Manages API versions and compatibility.
    """

    def __init__(self):
        self.versions: Dict[str, APIVersion] = {}
        self._setup_versions()

    def _setup_versions(self):
        """Setup supported API versions."""
        # Version 1.0 - Current stable version
        self.versions["v1"] = APIVersion(
            version="v1",
            release_date=datetime(2024, 1, 1),
            description="Initial stable release with full wishlist functionality"
        )

        # Future versions can be added here
        # self.versions["v2"] = APIVersion(
        #     version="v2",
        #     release_date=datetime(2024, 6, 1),
        #     description="Enhanced version with additional features"
        # )

    def get_version(self, version: str) -> Optional[APIVersion]:
        """Get version information."""
        return self.versions.get(version)

    def get_supported_versions(self) -> List[str]:
        """Get list of currently supported versions."""
        return [
            version for version, info in self.versions.items()
            if info.supported and not info.is_sunset
        ]

    def get_latest_version(self) -> str:
        """Get the latest supported version."""
        supported = self.get_supported_versions()
        if not supported:
            return "v1"  # Fallback
        
        # Sort versions and return the latest
        # This is a simple implementation - you might want more sophisticated version sorting
        return sorted(supported)[-1]

    def validate_version(self, version: str) -> APIVersion:
        """
        Validate and return version information.

        Args:
            version: Version string to validate

        Returns:
            APIVersion object

        Raises:
            HTTPException: If version is invalid or unsupported
        """
        if not version:
            version = self.get_latest_version()

        api_version = self.get_version(version)
        if not api_version:
            raise HTTPException(
                status_code=400,
                detail={
                    "code": "INVALID_API_VERSION",
                    "message": f"Invalid API version '{version}'",
                    "supported_versions": self.get_supported_versions(),
                    "correlation_id": generate_correlation_id()
                }
            )

        if api_version.is_sunset:
            raise HTTPException(
                status_code=410,
                detail={
                    "code": "API_VERSION_SUNSET",
                    "message": f"API version '{version}' is no longer supported",
                    "supported_versions": self.get_supported_versions(),
                    "correlation_id": generate_correlation_id()
                }
            )

        return api_version

    def add_version_headers(self, response: Response, version: str) -> Response:
        """
        Add version-related headers to response.

        Args:
            response: FastAPI response object
            version: API version being used

        Returns:
            Response with version headers added
        """
        api_version = self.get_version(version)
        if not api_version:
            return response

        # Add version headers
        response.headers["API-Version"] = version
        response.headers["API-Supported-Versions"] = ",".join(self.get_supported_versions())
        response.headers["API-Latest-Version"] = self.get_latest_version()

        # Add deprecation warnings
        if api_version.is_deprecated:
            response.headers["Deprecation"] = "true"
            if api_version.sunset_date:
                response.headers["Sunset"] = api_version.sunset_date.strftime("%a, %d %b %Y %H:%M:%S GMT")
                
            # Add warning header
            days_left = api_version.days_until_sunset
            if days_left is not None:
                warning_msg = f"API version {version} is deprecated and will be sunset in {days_left} days"
                response.headers["Warning"] = f'299 - "{warning_msg}"'

        return response


class VersionCompatibilityHandler:
    """
    Handles backward compatibility between API versions.
    """

    def __init__(self, version_manager: APIVersionManager):
        self.version_manager = version_manager

    def transform_request(self, request_data: Dict[str, Any], from_version: str, to_version: str) -> Dict[str, Any]:
        """
        Transform request data between API versions.

        Args:
            request_data: Original request data
            from_version: Source version
            to_version: Target version

        Returns:
            Transformed request data
        """
        # This is where you would implement version-specific transformations
        # For now, we'll just return the data as-is since we only have v1
        
        if from_version == to_version:
            return request_data

        # Example transformations for future versions:
        # if from_version == "v1" and to_version == "v2":
        #     return self._transform_v1_to_v2_request(request_data)

        return request_data

    def transform_response(self, response_data: Dict[str, Any], from_version: str, to_version: str) -> Dict[str, Any]:
        """
        Transform response data between API versions.

        Args:
            response_data: Original response data
            from_version: Source version
            to_version: Target version

        Returns:
            Transformed response data
        """
        if from_version == to_version:
            return response_data

        # Example transformations for future versions:
        # if from_version == "v2" and to_version == "v1":
        #     return self._transform_v2_to_v1_response(response_data)

        return response_data

    def _transform_v1_to_v2_request(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform v1 request to v2 format (example for future use)."""
        # Example: Add new required fields with defaults
        # data.setdefault("new_field", "default_value")
        return data

    def _transform_v2_to_v1_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Transform v2 response to v1 format (example for future use)."""
        # Example: Remove fields that don't exist in v1
        # data.pop("new_field", None)
        return data


# Global version manager instance
version_manager = APIVersionManager()
compatibility_handler = VersionCompatibilityHandler(version_manager)


def get_api_version_from_request(request: Request) -> str:
    """
    Extract API version from request.

    Args:
        request: FastAPI request object

    Returns:
        API version string
    """
    # Check header first
    version = request.headers.get("API-Version")
    if version:
        return version

    # Check query parameter
    version = request.query_params.get("version")
    if version:
        return version

    # Check path prefix (already handled by router, but included for completeness)
    path = request.url.path
    if path.startswith("/api/v"):
        parts = path.split("/")
        if len(parts) >= 3 and parts[2].startswith("v"):
            return parts[2]

    # Default to latest version
    return version_manager.get_latest_version()


def validate_api_version_middleware(request: Request) -> str:
    """
    Middleware function to validate API version from request.

    Args:
        request: FastAPI request object

    Returns:
        Validated API version string

    Raises:
        HTTPException: If version is invalid
    """
    version = get_api_version_from_request(request)
    api_version = version_manager.validate_version(version)
    
    # Store version in request state for later use
    request.state.api_version = api_version.version
    
    return api_version.version


def add_version_info_to_response(response: JSONResponse, request: Request) -> JSONResponse:
    """
    Add version information to API response.

    Args:
        response: JSON response object
        request: FastAPI request object

    Returns:
        Response with version information added
    """
    version = getattr(request.state, "api_version", "v1")
    
    # Add version info to response body if it's a JSON response
    if hasattr(response, "body") and response.body:
        try:
            import json
            content = json.loads(response.body)
            if isinstance(content, dict):
                content["api_version"] = version
                response.body = json.dumps(content).encode()
        except (json.JSONDecodeError, AttributeError):
            # If we can't parse the response, just add headers
            pass

    # Add version headers
    return version_manager.add_version_headers(response, version)


def get_version_info() -> Dict[str, Any]:
    """
    Get comprehensive version information for status endpoints.

    Returns:
        Dictionary with version information
    """
    return {
        "current_version": version_manager.get_latest_version(),
        "supported_versions": version_manager.get_supported_versions(),
        "versions": {
            version: {
                "version": info.version,
                "release_date": info.release_date.isoformat(),
                "deprecation_date": info.deprecation_date.isoformat() if info.deprecation_date else None,
                "sunset_date": info.sunset_date.isoformat() if info.sunset_date else None,
                "is_deprecated": info.is_deprecated,
                "is_sunset": info.is_sunset,
                "days_until_sunset": info.days_until_sunset,
                "description": info.description,
                "supported": info.supported
            }
            for version, info in version_manager.versions.items()
        }
    }