"""
Response formatting utilities for consistent API responses.

This module provides utilities for formatting API responses with consistent
structure, proper error handling, and correlation ID tracking.
"""
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from fastapi import Request
from fastapi.responses import JSONResponse

from app.schemas.base import SuccessResponseSchema, ErrorResponseSchema
from app.utils.helpers import generate_correlation_id


class ResponseFormatter:
    """
    Utility class for formatting API responses consistently.
    """

    @staticmethod
    def success(
        data: Any = None,
        message: str = "Success",
        status_code: int = 200,
        correlation_id: Optional[str] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """
        Format a successful response.

        Args:
            data: The response data
            message: Success message
            status_code: HTTP status code
            correlation_id: Request correlation ID
            meta: Additional metadata

        Returns:
            JSONResponse with formatted success response
        """
        if correlation_id is None:
            correlation_id = generate_correlation_id()

        response_content = {
            "success": True,
            "message": message,
            "data": data,
            "meta": meta or {},
            "correlation_id": correlation_id,
            "timestamp": datetime.utcnow().isoformat()
        }

        return JSONResponse(
            status_code=status_code,
            content=response_content,
            headers={"X-Request-ID": correlation_id}
        )

    @staticmethod
    def error(
        message: str,
        code: str = "ERROR",
        status_code: int = 400,
        correlation_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        field_errors: Optional[List[Dict[str, Any]]] = None
    ) -> JSONResponse:
        """
        Format an error response.

        Args:
            message: Error message
            code: Error code for programmatic handling
            status_code: HTTP status code
            correlation_id: Request correlation ID
            details: Additional error details
            field_errors: Field-specific validation errors

        Returns:
            JSONResponse with formatted error response
        """
        if correlation_id is None:
            correlation_id = generate_correlation_id()

        error_content = {
            "code": code,
            "message": message,
            "correlation_id": correlation_id
        }

        if details:
            error_content["details"] = details

        if field_errors:
            error_content["field_errors"] = field_errors

        response_content = {
            "success": False,
            "error": error_content,
            "correlation_id": correlation_id,
            "timestamp": datetime.utcnow().isoformat()
        }

        return JSONResponse(
            status_code=status_code,
            content=response_content,
            headers={"X-Request-ID": correlation_id}
        )

    @staticmethod
    def paginated(
        items: List[Any],
        total: int,
        page: int,
        page_size: int,
        message: str = "Success",
        correlation_id: Optional[str] = None,
        meta: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """
        Format a paginated response.

        Args:
            items: List of items for current page
            total: Total number of items
            page: Current page number
            page_size: Number of items per page
            message: Success message
            correlation_id: Request correlation ID
            meta: Additional metadata

        Returns:
            JSONResponse with formatted paginated response
        """
        if correlation_id is None:
            correlation_id = generate_correlation_id()

        total_pages = (total + page_size - 1) // page_size
        has_next = page < total_pages
        has_previous = page > 1

        pagination_meta = {
            "pagination": {
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_previous": has_previous
            }
        }

        if meta:
            pagination_meta.update(meta)

        return ResponseFormatter.success(
            data=items,
            message=message,
            correlation_id=correlation_id,
            meta=pagination_meta
        )

    @staticmethod
    def validation_error(
        validation_errors: List[Dict[str, Any]],
        correlation_id: Optional[str] = None
    ) -> JSONResponse:
        """
        Format a validation error response.

        Args:
            validation_errors: List of validation errors
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with formatted validation error response
        """
        return ResponseFormatter.error(
            message="Request validation failed",
            code="VALIDATION_ERROR",
            status_code=422,
            correlation_id=correlation_id,
            field_errors=validation_errors
        )

    @staticmethod
    def not_found(
        resource: str = "Resource",
        correlation_id: Optional[str] = None
    ) -> JSONResponse:
        """
        Format a not found error response.

        Args:
            resource: Name of the resource that was not found
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with formatted not found response
        """
        return ResponseFormatter.error(
            message=f"{resource} not found",
            code="NOT_FOUND",
            status_code=404,
            correlation_id=correlation_id
        )

    @staticmethod
    def unauthorized(
        message: str = "Authentication required",
        correlation_id: Optional[str] = None
    ) -> JSONResponse:
        """
        Format an unauthorized error response.

        Args:
            message: Error message
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with formatted unauthorized response
        """
        return ResponseFormatter.error(
            message=message,
            code="UNAUTHORIZED",
            status_code=401,
            correlation_id=correlation_id
        )

    @staticmethod
    def forbidden(
        message: str = "Access forbidden",
        correlation_id: Optional[str] = None
    ) -> JSONResponse:
        """
        Format a forbidden error response.

        Args:
            message: Error message
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with formatted forbidden response
        """
        return ResponseFormatter.error(
            message=message,
            code="FORBIDDEN",
            status_code=403,
            correlation_id=correlation_id
        )

    @staticmethod
    def rate_limited(
        retry_after: int,
        correlation_id: Optional[str] = None
    ) -> JSONResponse:
        """
        Format a rate limited error response.

        Args:
            retry_after: Seconds to wait before retrying
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with formatted rate limited response
        """
        response = ResponseFormatter.error(
            message="Rate limit exceeded",
            code="RATE_LIMITED",
            status_code=429,
            correlation_id=correlation_id,
            details={"retry_after": retry_after}
        )

        # Add rate limit headers
        response.headers["Retry-After"] = str(retry_after)
        response.headers["X-RateLimit-Reset"] = str(int(datetime.utcnow().timestamp()) + retry_after)

        return response

    @staticmethod
    def service_unavailable(
        message: str = "Service temporarily unavailable",
        correlation_id: Optional[str] = None
    ) -> JSONResponse:
        """
        Format a service unavailable error response.

        Args:
            message: Error message
            correlation_id: Request correlation ID

        Returns:
            JSONResponse with formatted service unavailable response
        """
        return ResponseFormatter.error(
            message=message,
            code="SERVICE_UNAVAILABLE",
            status_code=503,
            correlation_id=correlation_id
        )


def get_correlation_id_from_request(request: Request) -> str:
    """
    Extract correlation ID from request state or generate a new one.

    Args:
        request: FastAPI request object

    Returns:
        Correlation ID string
    """
    return getattr(request.state, "request_id", generate_correlation_id())


def format_validation_errors(errors: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Format Pydantic validation errors for consistent API responses.

    Args:
        errors: List of Pydantic validation errors

    Returns:
        List of formatted validation errors
    """
    formatted_errors = []

    for error in errors:
        field_path = " -> ".join(str(loc) for loc in error.get("loc", []))
        formatted_error = {
            "field": field_path,
            "message": error.get("msg", "Validation error"),
            "type": error.get("type", "validation_error"),
            "input": error.get("input")
        }
        formatted_errors.append(formatted_error)

    return formatted_errors


def create_api_response(
    data: Any = None,
    message: str = "Success",
    status_code: int = 200,
    request: Optional[Request] = None,
    **kwargs
) -> JSONResponse:
    """
    Create a standardized API response.

    Args:
        data: Response data
        message: Response message
        status_code: HTTP status code
        request: FastAPI request object (for correlation ID)
        **kwargs: Additional arguments for ResponseFormatter

    Returns:
        JSONResponse with standardized format
    """
    correlation_id = None
    if request:
        correlation_id = get_correlation_id_from_request(request)

    if status_code >= 400:
        return ResponseFormatter.error(
            message=message,
            status_code=status_code,
            correlation_id=correlation_id,
            **kwargs
        )
    else:
        return ResponseFormatter.success(
            data=data,
            message=message,
            status_code=status_code,
            correlation_id=correlation_id,
            **kwargs
        )