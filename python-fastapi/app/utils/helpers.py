"""
Utility helper functions.

This module contains common utility functions used throughout the application
for generating IDs, hashes, and other common operations.
"""
import uuid
import hashlib
import secrets
import string
from datetime import datetime
from typing import Optional


def generate_correlation_id() -> str:
    """
    Generate a unique correlation ID for request tracking.
    
    Returns:
        A unique correlation ID string
    """
    return str(uuid.uuid4())


def generate_share_hash() -> str:
    """
    Generate a secure share hash for public wishlist sharing.
    
    Returns:
        A secure random hash string
    """
    # Generate a random string of 32 characters
    alphabet = string.ascii_letters + string.digits
    return ''.join(secrets.choice(alphabet) for _ in range(32))


def generate_api_key(user_id: str) -> str:
    """
    Generate an API key for a user (for testing purposes).
    
    Args:
        user_id: The user ID
        
    Returns:
        A simple API key format for testing
    """
    # Simple format for testing: user_id:random_key
    random_part = ''.join(secrets.choice(string.ascii_letters + string.digits) for _ in range(32))
    return f"{user_id}:{random_part}"


def hash_string(input_string: str, salt: Optional[str] = None) -> str:
    """
    Hash a string using SHA-256.
    
    Args:
        input_string: The string to hash
        salt: Optional salt to add to the hash
        
    Returns:
        The hashed string
    """
    if salt:
        input_string = f"{input_string}{salt}"
    
    return hashlib.sha256(input_string.encode()).hexdigest()


def format_datetime_iso(dt: datetime) -> str:
    """
    Format a datetime object to ISO format string.
    
    Args:
        dt: The datetime object
        
    Returns:
        ISO formatted datetime string
    """
    return dt.isoformat() + "Z" if dt.tzinfo is None else dt.isoformat()


def parse_datetime_iso(dt_string: str) -> datetime:
    """
    Parse an ISO format datetime string.
    
    Args:
        dt_string: The ISO datetime string
        
    Returns:
        A datetime object
    """
    # Remove 'Z' suffix if present
    if dt_string.endswith('Z'):
        dt_string = dt_string[:-1]
    
    return datetime.fromisoformat(dt_string)


def sanitize_string(input_string: str, max_length: int = 255) -> str:
    """
    Sanitize a string by removing potentially harmful characters and limiting length.
    
    Args:
        input_string: The string to sanitize
        max_length: Maximum allowed length
        
    Returns:
        The sanitized string
    """
    if not input_string:
        return ""
    
    # Remove null bytes and control characters
    sanitized = ''.join(char for char in input_string if ord(char) >= 32)
    
    # Limit length
    return sanitized[:max_length].strip()


def validate_product_id(product_id: str) -> bool:
    """
    Validate a product ID format.
    
    Args:
        product_id: The product ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not product_id or not isinstance(product_id, str):
        return False
    
    # Basic validation - alphanumeric with hyphens and underscores
    return product_id.replace('-', '').replace('_', '').isalnum() and len(product_id) <= 50


def validate_user_id(user_id: str) -> bool:
    """
    Validate a user ID format.
    
    Args:
        user_id: The user ID to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not user_id or not isinstance(user_id, str):
        return False
    
    # Basic validation - alphanumeric with hyphens and underscores
    return user_id.replace('-', '').replace('_', '').isalnum() and len(user_id) <= 50


def truncate_string(text: str, max_length: int, suffix: str = "...") -> str:
    """
    Truncate a string to a maximum length with optional suffix.
    
    Args:
        text: The text to truncate
        max_length: Maximum length including suffix
        suffix: Suffix to add when truncating
        
    Returns:
        The truncated string
    """
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix


def get_cache_key(*parts: str) -> str:
    """
    Generate a cache key from multiple parts.
    
    Args:
        *parts: Parts to join into a cache key
        
    Returns:
        A cache key string
    """
    return ":".join(str(part) for part in parts if part)


def is_valid_share_hash(share_hash: str) -> bool:
    """
    Validate a share hash format.
    
    Args:
        share_hash: The share hash to validate
        
    Returns:
        True if valid, False otherwise
    """
    if not share_hash or not isinstance(share_hash, str):
        return False
    
    # Should be 32 characters, alphanumeric
    return len(share_hash) == 32 and share_hash.isalnum()
