"""
Pydantic-based settings management with environment validation.
"""
import os
from functools import lru_cache
from typing import List, Optional, Union

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings
from pydantic.networks import AnyHttpUrl


class Settings(BaseSettings):
    """
    Application settings with environment variable validation.
    """
    
    # Application Configuration
    APP_NAME: str = "Mumzworld Wishlist Service"
    VERSION: str = "2.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    ENVIRONMENT: str = Field(default="development", env="ENVIRONMENT")
    
    # API Configuration
    API_V1_PREFIX: str = "/api/v1"
    ALLOWED_ORIGINS: List[str] = Field(
        default=["*"],
        env="ALLOWED_ORIGINS"
    )
    
    # Server Configuration
    HOST: str = Field(default="0.0.0.0", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    
    # Database Configuration - DynamoDB
    DYNAMODB_ENDPOINT: Optional[str] = Field(default=None, env="DYNAMODB_ENDPOINT")
    DYNAMODB_REGION: str = Field(default="us-east-1", env="DYNAMODB_REGION")
    DYNAMODB_WISHLIST_TABLE: str = Field(default="wishlists", env="DYNAMODB_WISHLIST_TABLE")
    AWS_ACCESS_KEY_ID: Optional[str] = Field(default=None, env="AWS_ACCESS_KEY_ID")
    AWS_SECRET_ACCESS_KEY: Optional[str] = Field(default=None, env="AWS_SECRET_ACCESS_KEY")
    
    # Redis Configuration
    REDIS_URL: str = Field(..., env="REDIS_URL")
    REDIS_MAX_CONNECTIONS: int = Field(default=100, env="REDIS_MAX_CONNECTIONS")
    REDIS_RETRY_ON_TIMEOUT: bool = Field(default=True, env="REDIS_RETRY_ON_TIMEOUT")
    REDIS_SOCKET_KEEPALIVE: bool = Field(default=True, env="REDIS_SOCKET_KEEPALIVE")
    REDIS_SOCKET_KEEPALIVE_OPTIONS: dict = {}
    
    # Algolia Configuration
    ALGOLIA_APP_ID: str = Field(..., env="ALGOLIA_APP_ID")
    ALGOLIA_API_KEY: str = Field(..., env="ALGOLIA_API_KEY")
    ALGOLIA_TIMEOUT: int = Field(default=5, env="ALGOLIA_TIMEOUT")
    ALGOLIA_MAX_RETRIES: int = Field(default=3, env="ALGOLIA_MAX_RETRIES")
    
    # CloudFront Configuration
    CLOUDFRONT_DISTRIBUTION_ID: Optional[str] = Field(default=None, env="CLOUDFRONT_DISTRIBUTION_ID")
    CLOUDFRONT_KEY_PAIR_ID: Optional[str] = Field(default=None, env="CLOUDFRONT_KEY_PAIR_ID")
    CLOUDFRONT_PRIVATE_KEY_PATH: Optional[str] = Field(default=None, env="CLOUDFRONT_PRIVATE_KEY_PATH")
    
    # Performance Configuration
    MAX_CONCURRENT_REQUESTS: int = Field(default=1000, env="MAX_CONCURRENT_REQUESTS")
    REQUEST_TIMEOUT: int = Field(default=30, env="REQUEST_TIMEOUT")
    CACHE_TTL_DEFAULT: int = Field(default=300, env="CACHE_TTL_DEFAULT")  # 5 minutes
    CACHE_TTL_PRODUCTS: int = Field(default=3600, env="CACHE_TTL_PRODUCTS")  # 1 hour
    CACHE_TTL_WISHLISTS: int = Field(default=300, env="CACHE_TTL_WISHLISTS")  # 5 minutes
    
    # Monitoring and Observability
    ENABLE_METRICS: bool = Field(default=True, env="ENABLE_METRICS")
    ENABLE_TRACING: bool = Field(default=True, env="ENABLE_TRACING")
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FORMAT: str = Field(default="json", env="LOG_FORMAT")  # json or text
    
    # Security Configuration
    API_KEY_HEADER: str = Field(default="X-API-Key", env="API_KEY_HEADER")
    RATE_LIMIT_REQUESTS: int = Field(default=1000, env="RATE_LIMIT_REQUESTS")
    RATE_LIMIT_WINDOW: int = Field(default=60, env="RATE_LIMIT_WINDOW")  # seconds
    
    # AWS Parameter Store Configuration
    AWS_PARAMETER_STORE_PREFIX: str = Field(default="/mumzworld/wishlist", env="AWS_PARAMETER_STORE_PREFIX")
    USE_PARAMETER_STORE: bool = Field(default=False, env="USE_PARAMETER_STORE")
    
    # Celery Configuration
    CELERY_BROKER_URL: Optional[str] = Field(default=None, env="CELERY_BROKER_URL")
    CELERY_RESULT_BACKEND: Optional[str] = Field(default=None, env="CELERY_RESULT_BACKEND")
    
    # Connection Management
    ENABLE_GRACEFUL_DEGRADATION: bool = Field(default=True, env="ENABLE_GRACEFUL_DEGRADATION")
    CIRCUIT_BREAKER_THRESHOLD: int = Field(default=5, env="CIRCUIT_BREAKER_THRESHOLD")
    CIRCUIT_BREAKER_TIMEOUT: int = Field(default=60, env="CIRCUIT_BREAKER_TIMEOUT")
    HEALTH_CHECK_INTERVAL: int = Field(default=30, env="HEALTH_CHECK_INTERVAL")
    
    @field_validator("ALLOWED_ORIGINS", mode="before")
    @classmethod
    def parse_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @field_validator("LOG_LEVEL")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level."""
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")
        return v.upper()
    
    @field_validator("LOG_FORMAT")
    @classmethod
    def validate_log_format(cls, v: str) -> str:
        """Validate log format."""
        valid_formats = ["json", "text"]
        if v.lower() not in valid_formats:
            raise ValueError(f"LOG_FORMAT must be one of {valid_formats}")
        return v.lower()
    
    @field_validator("ENVIRONMENT")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment."""
        valid_environments = ["development", "staging", "production"]
        if v.lower() not in valid_environments:
            raise ValueError(f"ENVIRONMENT must be one of {valid_environments}")
        return v.lower()
    
    @model_validator(mode="after")
    def validate_redis_config(self):
        """Validate Redis configuration."""
        if self.REDIS_URL and not self.REDIS_URL.startswith(("redis://", "rediss://")):
            raise ValueError("REDIS_URL must start with redis:// or rediss://")
        return self
    
    @model_validator(mode="after")
    def validate_celery_config(self):
        """Set default Celery configuration if not provided."""
        if not self.CELERY_BROKER_URL:
            self.CELERY_BROKER_URL = self.REDIS_URL
        if not self.CELERY_RESULT_BACKEND:
            self.CELERY_RESULT_BACKEND = self.REDIS_URL
        return self
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.ENVIRONMENT == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.ENVIRONMENT == "development"
    
    @property
    def is_staging(self) -> bool:
        """Check if running in staging environment."""
        return self.ENVIRONMENT == "staging"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        validate_assignment = True


class DevelopmentSettings(Settings):
    """Development environment specific settings."""
    
    DEBUG: bool = True
    LOG_LEVEL: str = "DEBUG"
    ENABLE_METRICS: bool = True
    ENABLE_TRACING: bool = True
    
    # More permissive CORS for development
    ALLOWED_ORIGINS: List[str] = [
        "*",  # Allow all origins in development
    ]


class StagingSettings(Settings):
    """Staging environment specific settings."""
    
    DEBUG: bool = False
    LOG_LEVEL: str = "INFO"
    ENABLE_METRICS: bool = True
    ENABLE_TRACING: bool = True


class ProductionSettings(Settings):
    """Production environment specific settings."""
    
    DEBUG: bool = False
    LOG_LEVEL: str = "WARNING"
    ENABLE_METRICS: bool = True
    ENABLE_TRACING: bool = True
    USE_PARAMETER_STORE: bool = True
    
    # Stricter CORS for production
    ALLOWED_ORIGINS: List[str] = [
        "https://mumzworld.com",
        "https://www.mumzworld.com",
        "https://api.mumzworld.com",
    ]


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.
    Settings are cached to avoid repeated environment variable parsing.
    """
    environment = os.getenv("ENVIRONMENT", "development").lower()
    
    if environment == "production":
        return ProductionSettings()
    elif environment == "staging":
        return StagingSettings()
    else:
        return DevelopmentSettings()


# AWS Parameter Store integration (placeholder for now)
async def load_secrets_from_parameter_store(settings: Settings) -> dict:
    """
    Load secrets from AWS Parameter Store.
    This will be implemented when AWS integration is added.
    """
    if not settings.USE_PARAMETER_STORE:
        return {}
    
    # TODO: Implement AWS Parameter Store integration
    # This would use boto3 to fetch parameters by path
    return {}