"""
Structured logging configuration with JSON formatting and correlation IDs.
"""
import json
import logging
import sys
import time
from typing import Any, Dict, Optional

import structlog
from pythonjsonlogger import jsonlogger

from app.config.settings import get_settings


class CorrelationIDProcessor:
    """Processor to add correlation ID to log records."""
    
    def __call__(self, logger, method_name, event_dict):
        # Try to get correlation ID from context
        # This will be enhanced when we add proper context management
        correlation_id = event_dict.get("correlation_id")
        if correlation_id:
            event_dict["correlation_id"] = correlation_id
        return event_dict


class TimestampProcessor:
    """Processor to add timestamp to log records."""
    
    def __call__(self, logger, method_name, event_dict):
        event_dict["timestamp"] = time.time()
        event_dict["iso_timestamp"] = time.strftime("%Y-%m-%dT%H:%M:%S.%fZ", time.gmtime())
        return event_dict


class ServiceInfoProcessor:
    """Processor to add service information to log records."""
    
    def __init__(self):
        self.settings = get_settings()
    
    def __call__(self, logger, method_name, event_dict):
        event_dict["service"] = self.settings.APP_NAME
        event_dict["version"] = self.settings.VERSION
        event_dict["environment"] = self.settings.ENVIRONMENT
        return event_dict


class CustomJSONFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter for structured logging."""
    
    def add_fields(self, log_record, record, message_dict):
        super().add_fields(log_record, record, message_dict)
        
        # Add standard fields
        log_record['level'] = record.levelname
        log_record['logger'] = record.name
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # Add process and thread info
        log_record['process_id'] = record.process
        log_record['thread_id'] = record.thread
        
        # Add timestamp if not present
        if 'timestamp' not in log_record:
            log_record['timestamp'] = record.created
            log_record['iso_timestamp'] = time.strftime(
                "%Y-%m-%dT%H:%M:%S.%fZ", 
                time.gmtime(record.created)
            )


def setup_logging() -> None:
    """
    Setup structured logging configuration.
    """
    settings = get_settings()
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            TimestampProcessor(),
            ServiceInfoProcessor(),
            CorrelationIDProcessor(),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer() if settings.LOG_FORMAT == "json" else structlog.dev.ConsoleRenderer(),
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Configure standard library logging
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL))
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    
    if settings.LOG_FORMAT == "json":
        # JSON formatter for structured logging
        formatter = CustomJSONFormatter(
            fmt='%(timestamp)s %(level)s %(name)s %(message)s'
        )
    else:
        # Text formatter for development
        formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    configure_logger_levels(settings)


def configure_logger_levels(settings) -> None:
    """Configure specific logger levels."""
    
    # Set levels for third-party libraries
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.INFO)
    logging.getLogger("fastapi").setLevel(logging.INFO)
    logging.getLogger("boto3").setLevel(logging.WARNING)
    logging.getLogger("botocore").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    # Set application logger levels
    if settings.is_development:
        logging.getLogger("app").setLevel(logging.DEBUG)
    else:
        logging.getLogger("app").setLevel(logging.INFO)


def get_logger(name: str) -> structlog.BoundLogger:
    """
    Get a structured logger instance.
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        Configured structlog logger
    """
    return structlog.get_logger(name)


class RequestLoggingMixin:
    """Mixin for adding request logging capabilities."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
    
    def log_request(
        self,
        method: str,
        path: str,
        status_code: int,
        duration: float,
        correlation_id: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs
    ) -> None:
        """Log request information."""
        log_data = {
            "event": "request_processed",
            "method": method,
            "path": path,
            "status_code": status_code,
            "duration_ms": round(duration * 1000, 2),
            "correlation_id": correlation_id,
            "user_id": user_id,
            **kwargs
        }
        
        if status_code >= 500:
            self.logger.error("Request failed with server error", **log_data)
        elif status_code >= 400:
            self.logger.warning("Request failed with client error", **log_data)
        else:
            self.logger.info("Request processed successfully", **log_data)
    
    def log_external_service_call(
        self,
        service: str,
        operation: str,
        duration: float,
        success: bool,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> None:
        """Log external service call information."""
        log_data = {
            "event": "external_service_call",
            "service": service,
            "operation": operation,
            "duration_ms": round(duration * 1000, 2),
            "success": success,
            "correlation_id": correlation_id,
            **kwargs
        }
        
        if success:
            self.logger.info("External service call successful", **log_data)
        else:
            self.logger.error("External service call failed", **log_data)


# CloudWatch integration (placeholder for now)
class CloudWatchHandler(logging.Handler):
    """
    Custom handler for sending logs to AWS CloudWatch.
    This is a placeholder - actual implementation would use boto3.
    """
    
    def __init__(self, log_group: str, log_stream: str):
        super().__init__()
        self.log_group = log_group
        self.log_stream = log_stream
        # TODO: Initialize CloudWatch client
    
    def emit(self, record):
        """Send log record to CloudWatch."""
        # TODO: Implement CloudWatch log sending
        pass


def setup_cloudwatch_logging(settings) -> None:
    """
    Setup CloudWatch logging integration.
    This is a placeholder for future implementation.
    """
    if not settings.is_production:
        return
    
    # TODO: Implement CloudWatch logging setup
    # This would create CloudWatch log groups and streams
    # and add CloudWatchHandler to the root logger
    pass


# Performance logging utilities
class PerformanceLogger:
    """Logger for performance metrics and monitoring."""
    
    def __init__(self):
        self.logger = get_logger("performance")
    
    def log_database_query(
        self,
        operation: str,
        table: str,
        duration: float,
        success: bool,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> None:
        """Log database query performance."""
        self.logger.info(
            "Database query executed",
            event="database_query",
            operation=operation,
            table=table,
            duration_ms=round(duration * 1000, 2),
            success=success,
            correlation_id=correlation_id,
            **kwargs
        )
    
    def log_cache_operation(
        self,
        operation: str,
        key: str,
        hit: bool,
        duration: float,
        correlation_id: Optional[str] = None,
        **kwargs
    ) -> None:
        """Log cache operation performance."""
        self.logger.info(
            "Cache operation executed",
            event="cache_operation",
            operation=operation,
            key=key,
            hit=hit,
            duration_ms=round(duration * 1000, 2),
            correlation_id=correlation_id,
            **kwargs
        )


# Initialize logging on module import
setup_logging()