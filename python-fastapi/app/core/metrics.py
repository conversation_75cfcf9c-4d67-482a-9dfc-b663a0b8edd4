"""
Prometheus metrics collection and management.
"""
import time
from typing import Dict, Any, Optional
from collections import defaultdict, Counter
from threading import Lock

from app.config.settings import get_settings


class MetricsCollector:
    """
    Simple metrics collector for application metrics.
    This is a basic implementation that will be enhanced with Prometheus integration.
    """
    
    def __init__(self):
        self._lock = Lock()
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = {}
        self._histograms: Dict[str, list] = defaultdict(list)
        self._timers: Dict[str, list] = defaultdict(list)
        
    def counter(self, name: str, labels: Optional[Dict[str, str]] = None) -> 'Counter':
        """Get or create a counter metric."""
        return Counter(self, name, labels or {})
    
    def gauge(self, name: str, labels: Optional[Dict[str, str]] = None) -> 'Gauge':
        """Get or create a gauge metric."""
        return Gauge(self, name, labels or {})
    
    def histogram(self, name: str, labels: Optional[Dict[str, str]] = None) -> 'Histogram':
        """Get or create a histogram metric."""
        return Histogram(self, name, labels or {})
    
    def timer(self, name: str, labels: Optional[Dict[str, str]] = None) -> 'Timer':
        """Get or create a timer metric."""
        return Timer(self, name, labels or {})
    
    def increment_counter(self, name: str, value: int = 1, labels: Optional[Dict[str, str]] = None):
        """Increment a counter."""
        key = self._make_key(name, labels)
        with self._lock:
            self._counters[key] += value
    
    def set_gauge(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Set a gauge value."""
        key = self._make_key(name, labels)
        with self._lock:
            self._gauges[key] = value
    
    def record_histogram(self, name: str, value: float, labels: Optional[Dict[str, str]] = None):
        """Record a histogram value."""
        key = self._make_key(name, labels)
        with self._lock:
            self._histograms[key].append(value)
            # Keep only last 1000 values to prevent memory issues
            if len(self._histograms[key]) > 1000:
                self._histograms[key] = self._histograms[key][-1000:]
    
    def record_timer(self, name: str, duration: float, labels: Optional[Dict[str, str]] = None):
        """Record a timer duration."""
        key = self._make_key(name, labels)
        with self._lock:
            self._timers[key].append(duration)
            # Keep only last 1000 values to prevent memory issues
            if len(self._timers[key]) > 1000:
                self._timers[key] = self._timers[key][-1000:]
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics."""
        with self._lock:
            metrics = {
                "counters": dict(self._counters),
                "gauges": dict(self._gauges),
                "histograms": {},
                "timers": {},
            }
            
            # Calculate histogram statistics
            for key, values in self._histograms.items():
                if values:
                    sorted_values = sorted(values)
                    count = len(sorted_values)
                    metrics["histograms"][key] = {
                        "count": count,
                        "sum": sum(sorted_values),
                        "min": min(sorted_values),
                        "max": max(sorted_values),
                        "mean": sum(sorted_values) / count,
                        "p50": sorted_values[int(count * 0.5)],
                        "p95": sorted_values[int(count * 0.95)],
                        "p99": sorted_values[int(count * 0.99)],
                    }
            
            # Calculate timer statistics
            for key, values in self._timers.items():
                if values:
                    sorted_values = sorted(values)
                    count = len(sorted_values)
                    metrics["timers"][key] = {
                        "count": count,
                        "sum": sum(sorted_values),
                        "min": min(sorted_values),
                        "max": max(sorted_values),
                        "mean": sum(sorted_values) / count,
                        "p50": sorted_values[int(count * 0.5)],
                        "p95": sorted_values[int(count * 0.95)],
                        "p99": sorted_values[int(count * 0.99)],
                    }
            
            return metrics
    
    def _make_key(self, name: str, labels: Dict[str, str]) -> str:
        """Create a key from metric name and labels."""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"


class Counter:
    """Counter metric wrapper."""
    
    def __init__(self, collector: MetricsCollector, name: str, labels: Dict[str, str]):
        self.collector = collector
        self.name = name
        self.labels = labels
    
    def inc(self, value: int = 1):
        """Increment the counter."""
        self.collector.increment_counter(self.name, value, self.labels)


class Gauge:
    """Gauge metric wrapper."""
    
    def __init__(self, collector: MetricsCollector, name: str, labels: Dict[str, str]):
        self.collector = collector
        self.name = name
        self.labels = labels
    
    def set(self, value: float):
        """Set the gauge value."""
        self.collector.set_gauge(self.name, value, self.labels)


class Histogram:
    """Histogram metric wrapper."""
    
    def __init__(self, collector: MetricsCollector, name: str, labels: Dict[str, str]):
        self.collector = collector
        self.name = name
        self.labels = labels
    
    def observe(self, value: float):
        """Record a histogram observation."""
        self.collector.record_histogram(self.name, value, self.labels)


class Timer:
    """Timer metric wrapper."""
    
    def __init__(self, collector: MetricsCollector, name: str, labels: Dict[str, str]):
        self.collector = collector
        self.name = name
        self.labels = labels
        self.start_time = None
    
    def start(self):
        """Start the timer."""
        self.start_time = time.time()
        return self
    
    def stop(self):
        """Stop the timer and record the duration."""
        if self.start_time is not None:
            duration = time.time() - self.start_time
            self.collector.record_timer(self.name, duration, self.labels)
            self.start_time = None
            return duration
        return 0
    
    def __enter__(self):
        """Context manager entry."""
        self.start()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.stop()


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def init_metrics() -> MetricsCollector:
    """Initialize the global metrics collector."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector."""
    if _metrics_collector is None:
        return init_metrics()
    return _metrics_collector


# Convenience functions for common metrics
def counter(name: str, labels: Optional[Dict[str, str]] = None) -> Counter:
    """Create a counter metric."""
    return get_metrics_collector().counter(name, labels)


def gauge(name: str, labels: Optional[Dict[str, str]] = None) -> Gauge:
    """Create a gauge metric."""
    return get_metrics_collector().gauge(name, labels)


def histogram(name: str, labels: Optional[Dict[str, str]] = None) -> Histogram:
    """Create a histogram metric."""
    return get_metrics_collector().histogram(name, labels)


def timer(name: str, labels: Optional[Dict[str, str]] = None) -> Timer:
    """Create a timer metric."""
    return get_metrics_collector().timer(name, labels)


# Pre-defined application metrics
class ApplicationMetrics:
    """Pre-defined application metrics."""
    
    def __init__(self):
        self.requests_total = counter("http_requests_total")
        self.request_duration = histogram("http_request_duration_seconds")
        self.requests_in_progress = gauge("http_requests_in_progress")
        
        # Database metrics
        self.db_queries_total = counter("db_queries_total")
        self.db_query_duration = histogram("db_query_duration_seconds")
        
        # Cache metrics
        self.cache_operations_total = counter("cache_operations_total")
        self.cache_hits_total = counter("cache_hits_total")
        self.cache_misses_total = counter("cache_misses_total")
        
        # External service metrics
        self.external_requests_total = counter("external_requests_total")
        self.external_request_duration = histogram("external_request_duration_seconds")
    
    def counter(self, name: str, labels: Optional[Dict[str, str]] = None) -> Counter:
        """Create a counter metric."""
        return get_metrics_collector().counter(name, labels)
    
    def gauge(self, name: str, labels: Optional[Dict[str, str]] = None) -> Gauge:
        """Create a gauge metric."""
        return get_metrics_collector().gauge(name, labels)
    
    def histogram(self, name: str, labels: Optional[Dict[str, str]] = None) -> Histogram:
        """Create a histogram metric."""
        return get_metrics_collector().histogram(name, labels)
    
    def timer(self, name: str, labels: Optional[Dict[str, str]] = None) -> Timer:
        """Create a timer metric."""
        return get_metrics_collector().timer(name, labels)


# Global application metrics instance
metrics = ApplicationMetrics()