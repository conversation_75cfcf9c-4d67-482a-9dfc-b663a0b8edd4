"""
Custom exceptions and exception handlers for the FastAPI application.
"""
from typing import Any, Dict, Optional

from fastapi import Fast<PERSON><PERSON>, HTTPException, Request, status
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from pydantic import ValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException


class WishlistServiceException(Exception):
    """Base exception for wishlist service."""
    
    def __init__(self, message: str, code: str = "INTERNAL_ERROR"):
        self.message = message
        self.code = code
        super().__init__(message)


class WishlistNotFoundError(WishlistServiceException):
    """Raised when a wishlist is not found."""
    
    def __init__(self, message: str = "Wishlist not found"):
        super().__init__(message, "WISHLIST_NOT_FOUND")


class ValidationError(WishlistServiceException):
    """Raised when validation fails."""
    
    def __init__(self, message: str = "Validation failed"):
        super().__init__(message, "VALIDATION_ERROR")


class ExternalServiceError(WishlistServiceException):
    """Raised when external service fails."""
    
    def __init__(self, message: str = "External service error"):
        super().__init__(message, "EXTERNAL_SERVICE_ERROR")


class RateLimitExceededError(WishlistServiceException):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, "RATE_LIMIT_EXCEEDED")


class DatabaseError(WishlistServiceException):
    """Raised when database operations fail."""
    
    def __init__(self, message: str = "Database operation failed"):
        super().__init__(message, "DATABASE_ERROR")


class ConnectionError(WishlistServiceException):
    """Raised when connection to external services fails."""
    
    def __init__(self, message: str = "Connection failed"):
        super().__init__(message, "CONNECTION_ERROR")


class TransformationError(WishlistServiceException):
    """Raised when data transformation fails."""
    
    def __init__(self, message: str = "Data transformation failed"):
        super().__init__(message, "TRANSFORMATION_ERROR")


class AlgoliaServiceError(ExternalServiceError):
    """Raised when Algolia service operations fail."""
    
    def __init__(self, message: str = "Algolia service error"):
        super().__init__(message)
        self.code = "ALGOLIA_SERVICE_ERROR"


class CloudFrontServiceError(ExternalServiceError):
    """Raised when CloudFront service operations fail."""
    
    def __init__(self, message: str = "CloudFront service error"):
        super().__init__(message)
        self.code = "CLOUDFRONT_SERVICE_ERROR"


def create_error_response(
    request: Request,
    status_code: int,
    error_code: str,
    message: str,
    details: Optional[Dict[str, Any]] = None
) -> JSONResponse:
    """Create a standardized error response."""
    error_data = {
        "error": {
            "code": error_code,
            "message": message,
            "request_id": getattr(request.state, "request_id", None),
            "timestamp": int(__import__("time").time()),
        }
    }
    
    if details:
        error_data["error"]["details"] = details
    
    return JSONResponse(
        status_code=status_code,
        content=error_data
    )


async def wishlist_not_found_handler(request: Request, exc: WishlistNotFoundError) -> JSONResponse:
    """Handle wishlist not found errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_404_NOT_FOUND,
        error_code=exc.code,
        message=exc.message
    )


async def validation_error_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """Handle validation errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_400_BAD_REQUEST,
        error_code=exc.code,
        message=exc.message
    )


async def external_service_error_handler(request: Request, exc: ExternalServiceError) -> JSONResponse:
    """Handle external service errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        error_code=exc.code,
        message=exc.message
    )


async def rate_limit_error_handler(request: Request, exc: RateLimitExceededError) -> JSONResponse:
    """Handle rate limit exceeded errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        error_code=exc.code,
        message=exc.message
    )


async def database_error_handler(request: Request, exc: DatabaseError) -> JSONResponse:
    """Handle database errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code=exc.code,
        message=exc.message
    )


async def connection_error_handler(request: Request, exc: ConnectionError) -> JSONResponse:
    """Handle connection errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        error_code=exc.code,
        message=exc.message
    )


async def transformation_error_handler(request: Request, exc: TransformationError) -> JSONResponse:
    """Handle transformation errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code=exc.code,
        message=exc.message
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions."""
    return create_error_response(
        request=request,
        status_code=exc.status_code,
        error_code="HTTP_ERROR",
        message=exc.detail
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """Handle general exceptions."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        error_code="INTERNAL_ERROR",
        message="An internal error occurred"
    )


async def pydantic_validation_error_handler(request: Request, exc: ValidationError) -> JSONResponse:
    """Handle Pydantic validation errors."""
    return create_error_response(
        request=request,
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        error_code="VALIDATION_ERROR",
        message="Request validation failed",
        details={"validation_errors": exc.errors()}
    )


def setup_exception_handlers(app: FastAPI) -> None:
    """Setup all exception handlers for the FastAPI application."""
    
    # Custom exception handlers
    app.add_exception_handler(WishlistNotFoundError, wishlist_not_found_handler)
    app.add_exception_handler(ValidationError, validation_error_handler)
    app.add_exception_handler(ExternalServiceError, external_service_error_handler)
    app.add_exception_handler(RateLimitExceededError, rate_limit_error_handler)
    app.add_exception_handler(DatabaseError, database_error_handler)
    app.add_exception_handler(ConnectionError, connection_error_handler)
    app.add_exception_handler(TransformationError, transformation_error_handler)
    
    # Standard exception handlers
    app.add_exception_handler(HTTPException, http_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)