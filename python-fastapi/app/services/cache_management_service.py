"""
Cache Management Service for intelligent cache operations.

This service provides advanced cache management features including:
- Intelligent cache invalidation based on data changes
- Cache warming strategies for frequently accessed data
- Cache statistics and monitoring for performance optimization
- Cache partitioning for better performance and isolation
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from app.repositories.cache_repo import CacheRepository
from app.core.metrics import metrics
from app.config.settings import get_settings
from app.utils.helpers import generate_correlation_id

logger = logging.getLogger(__name__)


class CachePartition(str, Enum):
    """Cache partitions for better organization and isolation."""
    WISHLISTS = "wishlists"
    PRODUCTS = "products"
    USERS = "users"
    SHARED = "shared"
    SESSIONS = "sessions"
    RATE_LIMITS = "rate_limits"
    ANALYTICS = "analytics"


@dataclass
class CacheStats:
    """Cache statistics data structure."""
    partition: str
    total_keys: int
    hit_count: int
    miss_count: int
    hit_rate: float
    memory_usage_mb: float
    avg_ttl_seconds: int
    expired_keys: int
    last_updated: datetime


@dataclass
class CacheInvalidationRule:
    """Cache invalidation rule configuration."""
    trigger_pattern: str
    invalidation_patterns: List[str]
    cascade_rules: List[str]
    delay_seconds: int = 0


class CacheManagementService:
    """
    Advanced cache management service with intelligent operations.
    """

    def __init__(self, cache_repo: CacheRepository):
        self.cache_repo = cache_repo
        self.settings = get_settings()
        
        # Cache statistics tracking
        self._stats_cache: Dict[str, CacheStats] = {}
        self._last_stats_update = datetime.utcnow()
        
        # Invalidation rules
        self._invalidation_rules = self._setup_invalidation_rules()
        
        # Cache warming configuration
        self._warming_enabled = getattr(self.settings, 'CACHE_WARMING_ENABLED', True)
        self._warming_batch_size = getattr(self.settings, 'CACHE_WARMING_BATCH_SIZE', 50)
        
        logger.info("CacheManagementService initialized", extra={
            "warming_enabled": self._warming_enabled,
            "invalidation_rules": len(self._invalidation_rules)
        })

    def _setup_invalidation_rules(self) -> List[CacheInvalidationRule]:
        """Setup cache invalidation rules based on data relationships."""
        return [
            # Wishlist creation/update invalidates user wishlists cache
            CacheInvalidationRule(
                trigger_pattern="wishlist:created:*",
                invalidation_patterns=[
                    "user_wishlists:{user_id}:*",
                    "analytics:user_activity:{user_id}:*"
                ],
                cascade_rules=[]
            ),
            
            # Wishlist update invalidates specific wishlist and user caches
            CacheInvalidationRule(
                trigger_pattern="wishlist:updated:*",
                invalidation_patterns=[
                    "wishlist:{user_id}:{wishlist_id}:*",
                    "user_wishlists:{user_id}:*"
                ],
                cascade_rules=[]
            ),
            
            # Wishlist deletion cascades to shared wishlist cache
            CacheInvalidationRule(
                trigger_pattern="wishlist:deleted:*",
                invalidation_patterns=[
                    "wishlist:{user_id}:{wishlist_id}:*",
                    "user_wishlists:{user_id}:*",
                    "shared:{share_hash}"
                ],
                cascade_rules=[]
            ),
            
            # Item addition/removal invalidates wishlist caches
            CacheInvalidationRule(
                trigger_pattern="wishlist:item:*",
                invalidation_patterns=[
                    "wishlist:{user_id}:{wishlist_id}:*",
                    "user_wishlists:{user_id}:*"
                ],
                cascade_rules=[]
            ),
            
            # Privacy changes invalidate shared caches
            CacheInvalidationRule(
                trigger_pattern="wishlist:privacy:*",
                invalidation_patterns=[
                    "wishlist:{user_id}:{wishlist_id}:*",
                    "shared:{share_hash}",
                    "user_wishlists:{user_id}:*"
                ],
                cascade_rules=[]
            ),
            
            # Product updates invalidate product caches
            CacheInvalidationRule(
                trigger_pattern="product:updated:*",
                invalidation_patterns=[
                    "algolia:product:{product_id}:*"
                ],
                cascade_rules=[
                    "wishlist:*:*:*"  # Invalidate all wishlists containing this product
                ]
            )
        ]

    async def invalidate_cache_intelligently(
        self,
        trigger_event: str,
        context: Dict[str, Any]
    ) -> Dict[str, int]:
        """
        Intelligently invalidate cache based on trigger event and context.
        
        Args:
            trigger_event: The event that triggered invalidation (e.g., "wishlist:updated")
            context: Context data for pattern substitution
            
        Returns:
            Dictionary with invalidation results per pattern
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Intelligent cache invalidation triggered", extra={
            "correlation_id": correlation_id,
            "trigger_event": trigger_event,
            "context": context
        })

        invalidation_results = {}
        
        try:
            with metrics.timer("cache_invalidation_duration"):
                # Find matching invalidation rules
                matching_rules = [
                    rule for rule in self._invalidation_rules
                    if self._pattern_matches(trigger_event, rule.trigger_pattern)
                ]

                if not matching_rules:
                    logger.debug(f"No invalidation rules match trigger: {trigger_event}")
                    return {}

                # Process each matching rule
                for rule in matching_rules:
                    logger.debug(f"Processing invalidation rule", extra={
                        "correlation_id": correlation_id,
                        "trigger_pattern": rule.trigger_pattern,
                        "invalidation_patterns": rule.invalidation_patterns
                    })

                    # Apply delay if specified
                    if rule.delay_seconds > 0:
                        logger.info(f"Delaying invalidation by {rule.delay_seconds} seconds")
                        await asyncio.sleep(rule.delay_seconds)

                    # Process invalidation patterns
                    for pattern in rule.invalidation_patterns:
                        # Substitute context variables in pattern
                        resolved_pattern = self._resolve_pattern(pattern, context)
                        
                        # Perform invalidation
                        count = await self.cache_repo.delete_pattern(resolved_pattern)
                        invalidation_results[resolved_pattern] = count
                        
                        logger.debug(f"Invalidated {count} keys for pattern: {resolved_pattern}")

                    # Process cascade rules
                    for cascade_pattern in rule.cascade_rules:
                        await self._process_cascade_invalidation(cascade_pattern, context)

                total_invalidated = sum(invalidation_results.values())
                metrics.counter("cache_keys_invalidated").inc(total_invalidated)
                
                logger.info(f"Cache invalidation completed", extra={
                    "correlation_id": correlation_id,
                    "total_invalidated": total_invalidated,
                    "patterns_processed": len(invalidation_results)
                })

                return invalidation_results

        except Exception as e:
            logger.error(f"Error during intelligent cache invalidation: {e}", extra={
                "correlation_id": correlation_id,
                "trigger_event": trigger_event,
                "error": str(e)
            })
            metrics.counter("cache_invalidation_error").inc()
            return {}

    async def warm_cache_strategically(
        self,
        partition: CachePartition,
        strategy: str = "popular",
        limit: int = 100
    ) -> int:
        """
        Warm cache using strategic approaches.
        
        Args:
            partition: Cache partition to warm
            strategy: Warming strategy ("popular", "recent", "predictive")
            limit: Maximum number of items to warm
            
        Returns:
            Number of items successfully warmed
        """
        if not self._warming_enabled:
            logger.info("Cache warming is disabled")
            return 0

        correlation_id = generate_correlation_id()
        logger.info(f"Strategic cache warming started", extra={
            "correlation_id": correlation_id,
            "partition": partition.value,
            "strategy": strategy,
            "limit": limit
        })

        try:
            with metrics.timer("cache_warming_duration"):
                warmed_count = 0
                
                if partition == CachePartition.PRODUCTS:
                    warmed_count = await self._warm_product_cache(strategy, limit)
                elif partition == CachePartition.WISHLISTS:
                    warmed_count = await self._warm_wishlist_cache(strategy, limit)
                elif partition == CachePartition.USERS:
                    warmed_count = await self._warm_user_cache(strategy, limit)
                else:
                    logger.warning(f"Cache warming not implemented for partition: {partition}")

                metrics.counter("cache_items_warmed", {"partition": partition.value}).inc(warmed_count)
                
                logger.info(f"Cache warming completed", extra={
                    "correlation_id": correlation_id,
                    "warmed_count": warmed_count
                })

                return warmed_count

        except Exception as e:
            logger.error(f"Error during cache warming: {e}", extra={
                "correlation_id": correlation_id,
                "partition": partition.value,
                "strategy": strategy,
                "error": str(e)
            })
            metrics.counter("cache_warming_error").inc()
            return 0

    async def get_cache_statistics(
        self,
        partition: Optional[CachePartition] = None,
        refresh: bool = False
    ) -> Dict[str, CacheStats]:
        """
        Get comprehensive cache statistics.
        
        Args:
            partition: Specific partition to get stats for (None for all)
            refresh: Whether to refresh stats from cache backend
            
        Returns:
            Dictionary of cache statistics by partition
        """
        correlation_id = generate_correlation_id()
        
        try:
            # Check if we need to refresh stats
            if (refresh or 
                datetime.utcnow() - self._last_stats_update > timedelta(minutes=5)):
                await self._refresh_cache_statistics()

            # Return requested stats
            if partition:
                return {partition.value: self._stats_cache.get(partition.value, self._empty_stats(partition.value))}
            else:
                return self._stats_cache.copy()

        except Exception as e:
            logger.error(f"Error getting cache statistics: {e}", extra={
                "correlation_id": correlation_id,
                "error": str(e)
            })
            return {}

    async def optimize_cache_performance(self) -> Dict[str, Any]:
        """
        Analyze and optimize cache performance.
        
        Returns:
            Dictionary with optimization results and recommendations
        """
        correlation_id = generate_correlation_id()
        logger.info("Starting cache performance optimization", extra={
            "correlation_id": correlation_id
        })

        try:
            optimization_results = {
                "timestamp": datetime.utcnow(),
                "actions_taken": [],
                "recommendations": [],
                "performance_metrics": {}
            }

            # Get current statistics
            stats = await self.get_cache_statistics(refresh=True)
            
            # Analyze each partition
            for partition_name, partition_stats in stats.items():
                partition_results = await self._optimize_partition(partition_name, partition_stats)
                optimization_results["actions_taken"].extend(partition_results["actions"])
                optimization_results["recommendations"].extend(partition_results["recommendations"])

            # Global optimizations
            global_results = await self._perform_global_optimizations(stats)
            optimization_results["actions_taken"].extend(global_results["actions"])
            optimization_results["recommendations"].extend(global_results["recommendations"])

            # Update performance metrics
            optimization_results["performance_metrics"] = await self._calculate_performance_metrics(stats)

            logger.info("Cache performance optimization completed", extra={
                "correlation_id": correlation_id,
                "actions_count": len(optimization_results["actions_taken"]),
                "recommendations_count": len(optimization_results["recommendations"])
            })

            return optimization_results

        except Exception as e:
            logger.error(f"Error during cache optimization: {e}", extra={
                "correlation_id": correlation_id,
                "error": str(e)
            })
            return {"error": str(e), "timestamp": datetime.utcnow()}

    # Helper methods

    def _pattern_matches(self, event: str, pattern: str) -> bool:
        """Check if event matches pattern (supports wildcards)."""
        import fnmatch
        return fnmatch.fnmatch(event, pattern)

    def _resolve_pattern(self, pattern: str, context: Dict[str, Any]) -> str:
        """Resolve pattern with context variables."""
        resolved = pattern
        for key, value in context.items():
            resolved = resolved.replace(f"{{{key}}}", str(value))
        return resolved

    async def _process_cascade_invalidation(self, pattern: str, context: Dict[str, Any]) -> None:
        """Process cascade invalidation rules."""
        try:
            # This is a simplified implementation
            # In production, you might want more sophisticated cascade logic
            resolved_pattern = self._resolve_pattern(pattern, context)
            count = await self.cache_repo.delete_pattern(resolved_pattern)
            logger.debug(f"Cascade invalidation: {count} keys for pattern {resolved_pattern}")
        except Exception as e:
            logger.error(f"Error in cascade invalidation: {e}")

    async def _warm_product_cache(self, strategy: str, limit: int) -> int:
        """Warm product cache based on strategy."""
        # This would integrate with your product popularity tracking
        # For now, return 0 as placeholder
        return 0

    async def _warm_wishlist_cache(self, strategy: str, limit: int) -> int:
        """Warm wishlist cache based on strategy."""
        # This would integrate with your wishlist access patterns
        # For now, return 0 as placeholder
        return 0

    async def _warm_user_cache(self, strategy: str, limit: int) -> int:
        """Warm user cache based on strategy."""
        # This would integrate with your user activity patterns
        # For now, return 0 as placeholder
        return 0

    async def _refresh_cache_statistics(self) -> None:
        """Refresh cache statistics from backend."""
        try:
            # This would integrate with your Redis monitoring
            # For now, create mock statistics
            for partition in CachePartition:
                self._stats_cache[partition.value] = self._empty_stats(partition.value)
            
            self._last_stats_update = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Error refreshing cache statistics: {e}")

    def _empty_stats(self, partition: str) -> CacheStats:
        """Create empty stats structure."""
        return CacheStats(
            partition=partition,
            total_keys=0,
            hit_count=0,
            miss_count=0,
            hit_rate=0.0,
            memory_usage_mb=0.0,
            avg_ttl_seconds=0,
            expired_keys=0,
            last_updated=datetime.utcnow()
        )

    async def _optimize_partition(self, partition_name: str, stats: CacheStats) -> Dict[str, List[str]]:
        """Optimize specific cache partition."""
        results = {"actions": [], "recommendations": []}
        
        # Low hit rate optimization
        if stats.hit_rate < 0.5:
            results["recommendations"].append(
                f"Consider cache warming for {partition_name} partition (hit rate: {stats.hit_rate:.2%})"
            )
        
        # High memory usage optimization
        if stats.memory_usage_mb > 100:  # Threshold in MB
            results["recommendations"].append(
                f"Consider TTL optimization for {partition_name} partition (memory: {stats.memory_usage_mb:.1f}MB)"
            )
        
        return results

    async def _perform_global_optimizations(self, stats: Dict[str, CacheStats]) -> Dict[str, List[str]]:
        """Perform global cache optimizations."""
        results = {"actions": [], "recommendations": []}
        
        total_memory = sum(stat.memory_usage_mb for stat in stats.values())
        if total_memory > 500:  # Total threshold in MB
            results["recommendations"].append(
                f"Consider implementing cache eviction policies (total memory: {total_memory:.1f}MB)"
            )
        
        return results

    async def _calculate_performance_metrics(self, stats: Dict[str, CacheStats]) -> Dict[str, float]:
        """Calculate overall performance metrics."""
        total_hits = sum(stat.hit_count for stat in stats.values())
        total_misses = sum(stat.miss_count for stat in stats.values())
        total_requests = total_hits + total_misses
        
        return {
            "overall_hit_rate": total_hits / total_requests if total_requests > 0 else 0.0,
            "total_memory_mb": sum(stat.memory_usage_mb for stat in stats.values()),
            "avg_ttl_seconds": sum(stat.avg_ttl_seconds for stat in stats.values()) / len(stats) if stats else 0
        }


# Singleton instance for dependency injection
_cache_management_service: Optional[CacheManagementService] = None


async def get_cache_management_service(cache_repo: CacheRepository) -> CacheManagementService:
    """Get or create cache management service instance."""
    global _cache_management_service
    
    if _cache_management_service is None:
        _cache_management_service = CacheManagementService(cache_repo)
    
    return _cache_management_service