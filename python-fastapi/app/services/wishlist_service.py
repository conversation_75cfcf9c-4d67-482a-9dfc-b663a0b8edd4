"""
Core wishlist business logic service.

This service implements all wishlist operations including CRUD operations,
product enrichment, caching, and background job dispatching.
"""
import asyncio
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import uuid4

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.repositories.wishlist_repo import WishlistRepository
from app.repositories.cache_repo import CacheRepository
from app.services.algolia_service import AlgoliaService
from app.services.cloudfront_service import CloudFrontService
from app.services.cache_management_service import CacheManagementService
from app.schemas.requests.wishlist import (
    CreateWishlistRequest,
    UpdateWishlistRequest
)
from app.schemas.requests.item import (
    AddItemRequest,
    BulkAddItemsRequest,
    BulkRemoveItemsRequest,
    MoveItemsRequest,
    UpdateItemRequest
)
from app.core.exceptions import (
    WishlistNotFoundError,
    ValidationError,
    ExternalServiceError
)
from app.core.metrics import metrics
from app.utils.helpers import generate_correlation_id

logger = logging.getLogger(__name__)


class WishlistService:
    """
    Core business logic service for wishlist operations.

    Handles all wishlist CRUD operations, product enrichment,
    caching strategies, and background job dispatching.
    """

    def __init__(
        self,
        wishlist_repo: WishlistRepository,
        cache_repo: CacheRepository,
        algolia_service: AlgoliaService,
        cloudfront_service: CloudFrontService,
        cache_management_service: Optional[CacheManagementService] = None,
    ):
        self.wishlist_repo = wishlist_repo
        self.cache_repo = cache_repo
        self.algolia_service = algolia_service
        self.cloudfront_service = cloudfront_service
        self.cache_management_service = cache_management_service

    async def create_wishlist(
        self,
        user_id: str,
        request: CreateWishlistRequest
    ) -> Wishlist:
        """
        Create a new wishlist for a user.

        Args:
            user_id: The ID of the user creating the wishlist
            request: The wishlist creation request data

        Returns:
            The created wishlist

        Raises:
            ValidationError: If the request data is invalid
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Creating wishlist for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_name": request.name
        })

        with metrics.timer("wishlist_create_duration"):
            try:
                # Handle default wishlist logic
                if request.is_default:
                    await self._unset_default_wishlist(user_id)

                # Create new wishlist using domain model factory
                wishlist = Wishlist.create_new(
                    user_id=user_id,
                    name=request.name,
                    is_default=request.is_default,
                    is_public=False  # New wishlists are private by default
                )

                # Save to database
                created_wishlist = await self.wishlist_repo.create(wishlist)

                # Invalidate user's wishlist cache
                await self._invalidate_user_cache(user_id)

                # Dispatch background notification (fire and forget)
                asyncio.create_task(
                    self._dispatch_wishlist_created_notification(created_wishlist)
                )

                metrics.counter("wishlist_created").inc()
                logger.info(f"Successfully created wishlist {created_wishlist.wishlist_id}", extra={
                    "correlation_id": correlation_id,
                    "wishlist_id": created_wishlist.wishlist_id
                })

                return created_wishlist

            except Exception as e:
                metrics.counter("wishlist_create_error").inc()
                logger.error(f"Failed to create wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "error": str(e)
                })
                raise

    async def get_user_wishlists(
        self,
        user_id: str,
        country: str = "ae",
        language: str = "en",
        include_products: bool = True
    ) -> List[Wishlist]:
        """
        Get all wishlists for a user with optional product enrichment.

        Args:
            user_id: The ID of the user
            country: Country code for product localization
            language: Language code for product localization
            include_products: Whether to enrich items with product data

        Returns:
            List of user's wishlists
        """
        import pdb; pdb.set_trace()
        correlation_id = generate_correlation_id()
        cache_key = f"user_wishlists:{user_id}:{country}:{language}:{include_products}"

        logger.info(f"Getting wishlists for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "country": country,
            "language": language
        })

        # Try cache first
        try:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("cache_hit", {"type": "user_wishlists"}).inc()
                logger.debug(f"Cache hit for user wishlists", extra={
                    "correlation_id": correlation_id,
                    "cache_key": cache_key
                })
                return cached_result
        except Exception as e:
            logger.warning(f"Cache lookup failed: {e}", extra={
                "correlation_id": correlation_id
            })


        with metrics.timer("get_user_wishlists_duration"):
            try:
                # Get from database
                wishlists = await self.wishlist_repo.get_by_user_id(user_id)

                # Enrich with product details if requested
                if include_products and wishlists:
                    enriched_wishlists = await self._enrich_wishlists_with_products(
                        wishlists, country, language
                    )
                else:
                    enriched_wishlists = wishlists

                # Cache the result
                try:
                    await self.cache_repo.set(
                        cache_key,
                        enriched_wishlists,
                        ttl=300  # 5 minutes
                    )
                except Exception as e:
                    logger.warning(f"Failed to cache result: {e}", extra={
                        "correlation_id": correlation_id
                    })

                metrics.counter("cache_miss", {"type": "user_wishlists"}).inc()
                logger.info(f"Retrieved {len(enriched_wishlists)} wishlists for user", extra={
                    "correlation_id": correlation_id,
                    "wishlist_count": len(enriched_wishlists)
                })

                return enriched_wishlists

            except Exception as e:
                metrics.counter("get_user_wishlists_error").inc()
                logger.error(f"Failed to get user wishlists: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "error": str(e)
                })
                raise

    async def get_wishlist_by_id(
        self,
        user_id: str,
        wishlist_id: str,
        country: str = "ae",
        language: str = "en",
        include_products: bool = True
    ) -> Optional[Wishlist]:
        """
        Get a specific wishlist by ID.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            country: Country code for product localization
            language: Language code for product localization
            include_products: Whether to enrich items with product data

        Returns:
            The wishlist if found, None otherwise
        """
        correlation_id = generate_correlation_id()
        cache_key = f"wishlist:{user_id}:{wishlist_id}:{country}:{language}:{include_products}"

        logger.info(f"Getting wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        # Try cache first
        try:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("cache_hit", {"type": "wishlist"}).inc()
                return cached_result
        except Exception as e:
            logger.warning(f"Cache lookup failed: {e}", extra={
                "correlation_id": correlation_id
            })

        try:
            # Get from database
            wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
            if not wishlist:
                logger.info(f"Wishlist {wishlist_id} not found", extra={
                    "correlation_id": correlation_id
                })
                return None

            # Enrich with product details if requested
            if include_products:
                enriched_wishlist = await self._enrich_wishlist_with_products(
                    wishlist, country, language
                )
            else:
                enriched_wishlist = wishlist

            # Cache the result
            try:
                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=300)
            except Exception as e:
                logger.warning(f"Failed to cache result: {e}", extra={
                    "correlation_id": correlation_id
                })

            metrics.counter("cache_miss", {"type": "wishlist"}).inc()
            return enriched_wishlist

        except Exception as e:
            metrics.counter("get_wishlist_error").inc()
            logger.error(f"Failed to get wishlist: {e}", extra={
                "correlation_id": correlation_id,
                "user_id": user_id,
                "wishlist_id": wishlist_id,
                "error": str(e)
            })
            raise

    async def update_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: UpdateWishlistRequest
    ) -> Wishlist:
        """
        Update a wishlist's properties.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist to update
            request: The update request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Updating wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        with metrics.timer("wishlist_update_duration"):
            try:
                # Get existing wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Handle default wishlist logic
                if request.is_default is not None and request.is_default and not wishlist.is_default:
                    await self._unset_default_wishlist(user_id)

                # Update fields
                if request.name is not None:
                    wishlist.name = request.name
                if request.is_default is not None:
                    wishlist.is_default = request.is_default

                wishlist.updated_at = datetime.utcnow()

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                metrics.counter("wishlist_updated").inc()
                logger.info(f"Successfully updated wishlist {wishlist_id}", extra={
                    "correlation_id": correlation_id
                })

                return updated_wishlist

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("wishlist_update_error").inc()
                logger.error(f"Failed to update wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def delete_wishlist(self, user_id: str, wishlist_id: str) -> bool:
        """
        Delete a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist to delete

        Returns:
            True if deleted successfully

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Deleting wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        with metrics.timer("wishlist_delete_duration"):
            try:
                # Verify wishlist exists
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Delete from database
                success = await self.wishlist_repo.delete(user_id, wishlist_id)

                if success:
                    # Invalidate cache
                    await self._invalidate_wishlist_cache(user_id, wishlist_id)

                    # Dispatch background notification
                    asyncio.create_task(
                        self._dispatch_wishlist_deleted_notification(wishlist)
                    )

                    metrics.counter("wishlist_deleted").inc()
                    logger.info(f"Successfully deleted wishlist {wishlist_id}", extra={
                        "correlation_id": correlation_id
                    })

                return success

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("wishlist_delete_error").inc()
                logger.error(f"Failed to delete wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def add_item_to_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: AddItemRequest
    ) -> Wishlist:
        """
        Add an item to a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The add item request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Adding item {request.product_id} to wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": request.product_id
        })

        with metrics.timer("add_item_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Verify product exists in Algolia (optional validation)
                try:
                    product_exists = await self.algolia_service.product_exists(request.product_id)
                    if not product_exists:
                        logger.warning(f"Product {request.product_id} not found in Algolia", extra={
                            "correlation_id": correlation_id,
                            "product_id": request.product_id
                        })
                        # Continue anyway - product might be temporarily unavailable
                except Exception as e:
                    logger.warning(f"Failed to verify product existence: {e}", extra={
                        "correlation_id": correlation_id
                    })

                # Add item using domain model method
                added_item = wishlist.add_item(request.product_id, request.notes)

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_item_added_notification(updated_wishlist, added_item)
                )

                metrics.counter("item_added").inc()
                logger.info(f"Successfully added item to wishlist", extra={
                    "correlation_id": correlation_id,
                    "item_count": len(updated_wishlist.items)
                })

                return updated_wishlist

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("add_item_error").inc()
                logger.error(f"Failed to add item to wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "product_id": request.product_id,
                    "error": str(e)
                })
                raise

    async def remove_item_from_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        product_id: str
    ) -> Wishlist:
        """
        Remove an item from a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            product_id: The ID of the product to remove

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If the item doesn't exist in the wishlist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Removing item {product_id} from wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })

        with metrics.timer("remove_item_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Remove item using domain model method
                removed = wishlist.remove_item(product_id)
                if not removed:
                    raise ValidationError(f"Product {product_id} not found in wishlist")

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_item_removed_notification(updated_wishlist, product_id)
                )

                metrics.counter("item_removed").inc()
                logger.info(f"Successfully removed item from wishlist", extra={
                    "correlation_id": correlation_id,
                    "item_count": len(updated_wishlist.items)
                })

                return updated_wishlist

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("remove_item_error").inc()
                logger.error(f"Failed to remove item from wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "product_id": product_id,
                    "error": str(e)
                })
                raise

    async def bulk_add_items_to_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: BulkAddItemsRequest
    ) -> Wishlist:
        """
        Bulk add items to a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The bulk add request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Bulk adding items to wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_ids": request.product_ids
        })
        with metrics.timer("bulk_add_items_duration"):
            try:
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")
                for product_id in request.product_ids:
                    wishlist.add_item(product_id)
                updated_wishlist = await self.wishlist_repo.update(wishlist)
                await self._invalidate_wishlist_cache(user_id, wishlist_id)
                metrics.counter("bulk_items_added").inc(len(request.product_ids))
                return updated_wishlist
            except Exception as e:
                metrics.counter("bulk_add_items_error").inc()
                logger.error(f"Failed to bulk add items: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def bulk_remove_items_from_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: BulkRemoveItemsRequest
    ) -> Wishlist:
        """
        Bulk remove items from a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The bulk remove request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If no items were found to remove and ignore_missing is False
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Bulk removing items from wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_ids": request.product_ids
        })
        with metrics.timer("bulk_remove_items_duration"):
            try:
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")
                for product_id in request.product_ids:
                    wishlist.remove_item(product_id)
                updated_wishlist = await self.wishlist_repo.update(wishlist)
                await self._invalidate_wishlist_cache(user_id, wishlist_id)
                metrics.counter("bulk_items_removed").inc(len(request.product_ids))
                return updated_wishlist
            except Exception as e:
                metrics.counter("bulk_remove_items_error").inc()
                logger.error(f"Failed to bulk remove items: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def reorder_items_in_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: MoveItemsRequest
    ) -> Wishlist:
        """
        Reorder items in a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The move items request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If items don't exist in wishlist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Reordering items in wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "new_order": request.new_order
        })
        with metrics.timer("reorder_items_duration"):
            try:
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")
                # Reorder items based on new_order list
                product_id_to_item = {item.product_id: item for item in wishlist.items}
                wishlist.items = [product_id_to_item[pid] for pid in request.new_order if pid in product_id_to_item]
                wishlist.updated_at = datetime.utcnow()
                updated_wishlist = await self.wishlist_repo.update(wishlist)
                await self._invalidate_wishlist_cache(user_id, wishlist_id)
                metrics.counter("items_reordered").inc()
                return updated_wishlist
            except Exception as e:
                metrics.counter("reorder_items_error").inc()
                logger.error(f"Failed to reorder items: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def get_shared_wishlist(
        self,
        share_hash: str,
        country: str = "ae",
        language: str = "en"
    ) -> Optional[Wishlist]:
        """
        Get a shared wishlist by its share hash.

        Args:
            share_hash: The share hash of the wishlist
            country: Country code for product localization
            language: Language code for product localization

        Returns:
            The shared wishlist if found and public, None otherwise
        """
        correlation_id = generate_correlation_id()
        cache_key = f"shared_wishlist:{share_hash}:{country}:{language}"

        logger.info(f"Getting shared wishlist {share_hash}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash
        })

        # Try cache first
        try:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("cache_hit", {"type": "shared_wishlist"}).inc()
                return cached_result
        except Exception as e:
            logger.warning(f"Cache lookup failed: {e}", extra={
                "correlation_id": correlation_id
            })

        try:
            # Get from database using share hash
            wishlist = await self.wishlist_repo.get_by_share_hash(share_hash)
            if not wishlist or not wishlist.is_public:
                logger.info(f"Shared wishlist not found or not public", extra={
                    "correlation_id": correlation_id,
                    "share_hash": share_hash
                })
                return None

            # Enrich with product details
            enriched_wishlist = await self._enrich_wishlist_with_products(
                wishlist, country, language
            )

            # Cache the result (longer TTL for shared wishlists)
            try:
                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=1800)  # 30 minutes
            except Exception as e:
                logger.warning(f"Failed to cache result: {e}", extra={
                    "correlation_id": correlation_id
                })

            metrics.counter("cache_miss", {"type": "shared_wishlist"}).inc()
            return enriched_wishlist

        except Exception as e:
            metrics.counter("get_shared_wishlist_error").inc()
            logger.error(f"Failed to get shared wishlist: {e}", extra={
                "correlation_id": correlation_id,
                "share_hash": share_hash,
                "error": str(e)
            })
            raise

    # Helper methods

    async def _enrich_wishlists_with_products(
        self,
        wishlists: List[Wishlist],
        country: str,
        language: str
    ) -> List[Wishlist]:
        """Enrich multiple wishlists with product data concurrently."""
        if not wishlists:
            return wishlists

        tasks = [
            self._enrich_wishlist_with_products(wishlist, country, language)
            for wishlist in wishlists
        ]
        return await asyncio.gather(*tasks, return_exceptions=False)

    async def _enrich_wishlist_with_products(
        self,
        wishlist: Wishlist,
        country: str,
        language: str
    ) -> Wishlist:
        """
        Enrich a single wishlist with product data using advanced caching and fallback strategies.

        Features:
        - Concurrent product fetching
        - Cache warming for frequently accessed products
        - Fallback to cached data on service failure
        - Graceful degradation with partial results
        """
        if not wishlist.items:
            return wishlist

        correlation_id = generate_correlation_id()
        product_ids = [item.product_id for item in wishlist.items]

        logger.debug(f"Enriching wishlist {wishlist.wishlist_id} with {len(product_ids)} products", extra={
            "correlation_id": correlation_id,
            "wishlist_id": wishlist.wishlist_id,
            "product_count": len(product_ids)
        })

        try:
            with metrics.timer("product_enrichment_duration"):
                # Get product data from Algolia with batching
                products = await self.algolia_service.get_products_by_ids(
                    product_ids, country, language
                )

                # Enrich items with product data
                enriched_count = 0
                missing_products = []

                for item in wishlist.items:
                    if item.product_id in products:
                        item.product = products[item.product_id]
                        enriched_count += 1
                    else:
                        missing_products.append(item.product_id)
                        # Try to get cached product data as fallback
                        if self.cache_repo:
                            fallback_product = await self._get_fallback_product_data(
                                item.product_id, country, language
                            )
                            if fallback_product:
                                item.product = fallback_product
                                enriched_count += 1
                                logger.info(f"Used fallback data for product {item.product_id}")

                # Log enrichment results
                if missing_products:
                    logger.warning(f"Products not found during enrichment: {missing_products}", extra={
                        "correlation_id": correlation_id,
                        "missing_count": len(missing_products)
                    })

                metrics.counter("products_enriched").inc(enriched_count)
                metrics.counter("products_missing").inc(len(missing_products))

                logger.debug(f"Enriched {enriched_count}/{len(product_ids)} products", extra={
                    "correlation_id": correlation_id,
                    "enriched_count": enriched_count,
                    "total_count": len(product_ids)
                })

                # Trigger cache warming for frequently accessed products (fire and forget)
                if enriched_count > 0:
                    asyncio.create_task(
                        self._warm_product_cache(product_ids, country, language)
                    )

                return wishlist

        except Exception as e:
            logger.error(f"Failed to enrich wishlist with products: {e}", extra={
                "correlation_id": correlation_id,
                "error": str(e)
            })
            metrics.counter("product_enrichment_error").inc()

            # Try to use cached data as complete fallback
            try:
                cached_products = await self._get_cached_products_batch(product_ids, country, language)
                if cached_products:
                    for item in wishlist.items:
                        if item.product_id in cached_products:
                            item.product = cached_products[item.product_id]

                    logger.info(f"Used cached fallback data for {len(cached_products)} products", extra={
                        "correlation_id": correlation_id
                    })
                    metrics.counter("product_enrichment_fallback_success").inc()
            except Exception as fallback_error:
                logger.error(f"Fallback enrichment also failed: {fallback_error}", extra={
                    "correlation_id": correlation_id
                })
                metrics.counter("product_enrichment_fallback_error").inc()

            # Return wishlist without product enrichment rather than failing
            return wishlist

    async def _unset_default_wishlist(self, user_id: str) -> None:
        """Unset any existing default wishlist for a user."""
        try:
            current_default = await self.wishlist_repo.get_default_wishlist(user_id)
            if current_default:
                current_default.is_default = False
                current_default.updated_at = datetime.utcnow()
                await self.wishlist_repo.update(current_default)
                logger.info(f"Unset default wishlist {current_default.wishlist_id} for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to unset default wishlist: {e}")
            # Don't raise - this is not critical for the main operation

    async def _invalidate_user_cache(self, user_id: str) -> None:
        """Invalidate all cache entries for a user using intelligent cache management."""
        try:
            if self.cache_management_service:
                # Use intelligent cache invalidation
                await self.cache_management_service.invalidate_cache_intelligently(
                    trigger_event="user:cache:invalidate",
                    context={"user_id": user_id}
                )
            else:
                # Fallback to direct pattern deletion
                patterns = [
                    f"user_wishlists:{user_id}:*",
                    f"wishlist:{user_id}:*"
                ]
                for pattern in patterns:
                    await self.cache_repo.delete_pattern(pattern)

            logger.debug(f"Invalidated cache for user {user_id}")
        except Exception as e:
            logger.warning(f"Failed to invalidate user cache: {e}")

    async def _invalidate_wishlist_cache(self, user_id: str, wishlist_id: str) -> None:
        """Invalidate cache entries for a specific wishlist using intelligent cache management."""
        try:
            if self.cache_management_service:
                # Use intelligent cache invalidation
                await self.cache_management_service.invalidate_cache_intelligently(
                    trigger_event="wishlist:updated",
                    context={
                        "user_id": user_id,
                        "wishlist_id": wishlist_id
                    }
                )
            else:
                # Fallback to direct pattern deletion
                patterns = [
                    f"wishlist:{user_id}:{wishlist_id}:*",
                    f"user_wishlists:{user_id}:*"
                ]
                for pattern in patterns:
                    await self.cache_repo.delete_pattern(pattern)

            logger.debug(f"Invalidated cache for wishlist {wishlist_id}")
        except Exception as e:
            logger.warning(f"Failed to invalidate wishlist cache: {e}")

    # Background notification methods (fire and forget)

    async def _dispatch_wishlist_created_notification(self, wishlist: Wishlist) -> None:
        """Dispatch background notification for wishlist creation."""
        try:
            # This would typically queue a Celery task
            # For now, we'll just log the event
            logger.info(f"Wishlist created notification", extra={
                "event": "wishlist_created",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "wishlist_name": wishlist.name
            })

            # Trigger CloudFront cache invalidation if needed
            if wishlist.is_public:
                await self.cloudfront_service.invalidate_cache([
                    f"/api/v1/wishlists/shared/{wishlist.share_hash}"
                ])

        except Exception as e:
            logger.error(f"Failed to dispatch wishlist created notification: {e}")

    async def _dispatch_wishlist_deleted_notification(self, wishlist: Wishlist) -> None:
        """Dispatch background notification for wishlist deletion."""
        try:
            logger.info(f"Wishlist deleted notification", extra={
                "event": "wishlist_deleted",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id
            })

            # Trigger CloudFront cache invalidation
            if wishlist.is_public:
                await self.cloudfront_service.invalidate_cache([
                    f"/api/v1/wishlists/shared/{wishlist.share_hash}"
                ])

        except Exception as e:
            logger.error(f"Failed to dispatch wishlist deleted notification: {e}")

    async def _dispatch_item_added_notification(self, wishlist: Wishlist, item: WishlistItem) -> None:
        """Dispatch background notification for item addition."""
        try:
            logger.info(f"Item added notification", extra={
                "event": "item_added",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "product_id": item.product_id
            })
        except Exception as e:
            logger.error(f"Failed to dispatch item added notification: {e}")

    async def _dispatch_item_removed_notification(self, wishlist: Wishlist, product_id: str) -> None:
        """Dispatch background notification for item removal."""
        try:
            logger.info(f"Item removed notification", extra={
                "event": "item_removed",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "product_id": product_id
            })
        except Exception as e:
            logger.error(f"Failed to dispatch item removed notification: {e}")

    # Bulk Operations (Subtask 6.2)

    async def bulk_add_items_to_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: BulkAddItemsRequest
    ) -> Wishlist:
        """
        Add multiple items to a wishlist in a single operation.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The bulk add request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Bulk adding {len(request.items)} items to wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "item_count": len(request.items)
        })

        with metrics.timer("bulk_add_items_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Verify products exist in Algolia (batch operation)
                product_ids = [item.product_id for item in request.items]
                try:
                    existing_products = await self.algolia_service.batch_check_products_exist(product_ids)
                    missing_products = [pid for pid in product_ids if not existing_products.get(pid, False)]
                    if missing_products:
                        logger.warning(f"Some products not found in Algolia: {missing_products}", extra={
                            "correlation_id": correlation_id,
                            "missing_products": missing_products
                        })
                except Exception as e:
                    logger.warning(f"Failed to verify product existence: {e}", extra={
                        "correlation_id": correlation_id
                    })

                # Add items using domain model methods
                added_items = []
                skipped_items = []

                for item_data in request.items:
                    # Check if item already exists and skip_duplicates is True
                    if request.skip_duplicates and wishlist.has_item(item_data.product_id):
                        skipped_items.append(item_data.product_id)
                        continue

                    added_item = wishlist.add_item(item_data.product_id, item_data.notes)
                    added_items.append(added_item)

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_bulk_items_added_notification(
                        updated_wishlist, added_items, skipped_items
                    )
                )

                metrics.counter("bulk_items_added").inc()
                metrics.counter("items_added_total").inc(len(added_items))
                logger.info(f"Successfully bulk added items to wishlist", extra={
                    "correlation_id": correlation_id,
                    "added_count": len(added_items),
                    "skipped_count": len(skipped_items),
                    "total_items": len(updated_wishlist.items)
                })

                return updated_wishlist

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("bulk_add_items_error").inc()
                logger.error(f"Failed to bulk add items to wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def bulk_remove_items_from_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: BulkRemoveItemsRequest
    ) -> Wishlist:
        """
        Remove multiple items from a wishlist in a single operation.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The bulk remove request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If no items were found to remove and ignore_missing is False
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Bulk removing {len(request.product_ids)} items from wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_ids": request.product_ids
        })

        with metrics.timer("bulk_remove_items_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Remove items using domain model methods
                removed_items = []
                missing_items = []

                for product_id in request.product_ids:
                    removed = wishlist.remove_item(product_id)
                    if removed:
                        removed_items.append(product_id)
                    else:
                        missing_items.append(product_id)

                # Check if any items were removed
                if not removed_items and not request.ignore_missing:
                    raise ValidationError("No items found to remove from wishlist")

                # Save to database if any items were removed
                if removed_items:
                    updated_wishlist = await self.wishlist_repo.update(wishlist)

                    # Invalidate cache
                    await self._invalidate_wishlist_cache(user_id, wishlist_id)

                    # Dispatch background notification
                    asyncio.create_task(
                        self._dispatch_bulk_items_removed_notification(
                            updated_wishlist, removed_items, missing_items
                        )
                    )
                else:
                    updated_wishlist = wishlist

                metrics.counter("bulk_items_removed").inc()
                metrics.counter("items_removed_total").inc(len(removed_items))
                logger.info(f"Successfully bulk removed items from wishlist", extra={
                    "correlation_id": correlation_id,
                    "removed_count": len(removed_items),
                    "missing_count": len(missing_items),
                    "total_items": len(updated_wishlist.items)
                })

                return updated_wishlist

            except (WishlistNotFoundError, ValidationError):
                raise
            except Exception as e:
                metrics.counter("bulk_remove_items_error").inc()
                logger.error(f"Failed to bulk remove items from wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def move_items_between_wishlists(
        self,
        user_id: str,
        request: MoveItemsRequest
    ) -> Dict[str, Wishlist]:
        """
        Move or copy items between wishlists.

        Args:
            user_id: The ID of the user
            request: The move items request data

        Returns:
            Dictionary with 'source' and 'target' wishlists

        Raises:
            WishlistNotFoundError: If either wishlist doesn't exist
            ValidationError: If items don't exist in source wishlist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Moving {len(request.product_ids)} items between wishlists", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "source_wishlist_id": request.source_wishlist_id,
            "target_wishlist_id": request.target_wishlist_id,
            "copy_mode": request.copy_instead_of_move
        })

        with metrics.timer("move_items_duration"):
            try:
                # Get both wishlists
                source_wishlist = await self.wishlist_repo.get_by_id(user_id, request.source_wishlist_id)
                if not source_wishlist:
                    raise WishlistNotFoundError(f"Source wishlist {request.source_wishlist_id} not found")

                target_wishlist = await self.wishlist_repo.get_by_id(user_id, request.target_wishlist_id)
                if not target_wishlist:
                    raise WishlistNotFoundError(f"Target wishlist {request.target_wishlist_id} not found")

                # Find items to move/copy
                items_to_move = []
                missing_items = []

                for product_id in request.product_ids:
                    item = source_wishlist.get_item(product_id)
                    if item:
                        items_to_move.append(item)
                    else:
                        missing_items.append(product_id)

                if not items_to_move:
                    raise ValidationError("No items found in source wishlist to move")

                # Add items to target wishlist
                for item in items_to_move:
                    target_wishlist.add_item(item.product_id, item.notes)

                # Remove items from source wishlist if moving (not copying)
                if not request.copy_instead_of_move:
                    for item in items_to_move:
                        source_wishlist.remove_item(item.product_id)

                # Save both wishlists
                updated_source = await self.wishlist_repo.update(source_wishlist)
                updated_target = await self.wishlist_repo.update(target_wishlist)

                # Invalidate cache for both wishlists
                await self._invalidate_wishlist_cache(user_id, request.source_wishlist_id)
                await self._invalidate_wishlist_cache(user_id, request.target_wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_items_moved_notification(
                        updated_source, updated_target, items_to_move, request.copy_instead_of_move
                    )
                )

                operation = "copied" if request.copy_instead_of_move else "moved"
                metrics.counter(f"items_{operation}").inc()
                metrics.counter(f"items_{operation}_total").inc(len(items_to_move))

                logger.info(f"Successfully {operation} items between wishlists", extra={
                    "correlation_id": correlation_id,
                    "moved_count": len(items_to_move),
                    "missing_count": len(missing_items)
                })

                return {
                    "source": updated_source,
                    "target": updated_target
                }

            except (WishlistNotFoundError, ValidationError):
                raise
            except Exception as e:
                metrics.counter("move_items_error").inc()
                logger.error(f"Failed to move items between wishlists: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "error": str(e)
                })
                raise

    async def update_item_in_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        product_id: str,
        request: UpdateItemRequest
    ) -> Wishlist:
        """
        Update an existing item in a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            product_id: The ID of the product to update
            request: The update request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If the item doesn't exist in the wishlist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Updating item {product_id} in wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })

        with metrics.timer("update_item_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Update item using domain model method
                updated = wishlist.update_item_notes(product_id, request.notes)
                if not updated:
                    raise ValidationError(f"Item {product_id} not found in wishlist")

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_item_updated_notification(updated_wishlist, product_id)
                )

                metrics.counter("item_updated").inc()
                logger.info(f"Successfully updated item in wishlist", extra={
                    "correlation_id": correlation_id
                })

                return updated_wishlist

            except (WishlistNotFoundError, ValidationError):
                raise
            except Exception as e:
                metrics.counter("update_item_error").inc()
                logger.error(f"Failed to update item in wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "product_id": product_id,
                    "error": str(e)
                })
                raise

    async def reorder_wishlist_items(
        self,
        user_id: str,
        wishlist_id: str,
        ordered_product_ids: List[str]
    ) -> Wishlist:
        """
        Reorder items in a wishlist based on provided product ID sequence.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            ordered_product_ids: List of product IDs in desired order

        Returns:
            The updated wishlist with reordered items

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If product IDs don't match wishlist items
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Reordering items in wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "item_count": len(ordered_product_ids)
        })

        with metrics.timer("reorder_items_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Validate that all product IDs exist in the wishlist
                existing_product_ids = set(wishlist.get_product_ids())
                provided_product_ids = set(ordered_product_ids)

                if existing_product_ids != provided_product_ids:
                    missing_ids = existing_product_ids - provided_product_ids
                    extra_ids = provided_product_ids - existing_product_ids
                    error_msg = "Product ID mismatch: "
                    if missing_ids:
                        error_msg += f"missing {list(missing_ids)}, "
                    if extra_ids:
                        error_msg += f"extra {list(extra_ids)}"
                    raise ValidationError(error_msg.rstrip(", "))

                # Create a mapping of product_id to item
                item_map = {item.product_id: item for item in wishlist.items}

                # Reorder items based on provided sequence
                reordered_items = [item_map[product_id] for product_id in ordered_product_ids]
                wishlist.items = reordered_items
                wishlist.updated_at = datetime.utcnow()

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_items_reordered_notification(updated_wishlist)
                )

                metrics.counter("items_reordered").inc()
                logger.info(f"Successfully reordered items in wishlist", extra={
                    "correlation_id": correlation_id
                })

                return updated_wishlist

            except (WishlistNotFoundError, ValidationError):
                raise
            except Exception as e:
                metrics.counter("reorder_items_error").inc()
                logger.error(f"Failed to reorder items in wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    # Additional background notification methods

    async def _dispatch_bulk_items_added_notification(
        self,
        wishlist: Wishlist,
        added_items: List[WishlistItem],
        skipped_items: List[str]
    ) -> None:
        """Dispatch background notification for bulk item addition."""
        try:
            logger.info(f"Bulk items added notification", extra={
                "event": "bulk_items_added",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "added_count": len(added_items),
                "skipped_count": len(skipped_items)
            })
        except Exception as e:
            logger.error(f"Failed to dispatch bulk items added notification: {e}")

    async def _dispatch_bulk_items_removed_notification(
        self,
        wishlist: Wishlist,
        removed_items: List[str],
        missing_items: List[str]
    ) -> None:
        """Dispatch background notification for bulk item removal."""
        try:
            logger.info(f"Bulk items removed notification", extra={
                "event": "bulk_items_removed",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "removed_count": len(removed_items),
                "missing_count": len(missing_items)
            })
        except Exception as e:
            logger.error(f"Failed to dispatch bulk items removed notification: {e}")

    async def _dispatch_items_moved_notification(
        self,
        source_wishlist: Wishlist,
        target_wishlist: Wishlist,
        moved_items: List[WishlistItem],
        is_copy: bool
    ) -> None:
        """Dispatch background notification for item movement."""
        try:
            operation = "copied" if is_copy else "moved"
            logger.info(f"Items {operation} notification", extra={
                "event": f"items_{operation}",
                "user_id": source_wishlist.user_id,
                "source_wishlist_id": source_wishlist.wishlist_id,
                "target_wishlist_id": target_wishlist.wishlist_id,
                "item_count": len(moved_items)
            })
        except Exception as e:
            logger.error(f"Failed to dispatch items moved notification: {e}")

    async def _dispatch_item_updated_notification(self, wishlist: Wishlist, product_id: str) -> None:
        """Dispatch background notification for item update."""
        try:
            logger.info(f"Item updated notification", extra={
                "event": "item_updated",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "product_id": product_id
            })
        except Exception as e:
            logger.error(f"Failed to dispatch item updated notification: {e}")

    async def _dispatch_items_reordered_notification(self, wishlist: Wishlist) -> None:
        """Dispatch background notification for item reordering."""
        try:
            logger.info(f"Items reordered notification", extra={
                "event": "items_reordered",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "item_count": len(wishlist.items)
            })
        except Exception as e:
            logger.error(f"Failed to dispatch items reordered notification: {e}")

    # Product Enrichment Helper Methods (Subtask 6.3)

    async def _get_fallback_product_data(
        self,
        product_id: str,
        country: str,
        language: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get fallback product data from cache with multiple fallback strategies.
        """
        if not self.cache_repo:
            return None

        try:
            # Try exact cache key first
            cache_key = f"algolia:product:{product_id}:{country}:{language}"
            cached_data = await self.cache_repo.get(cache_key)
            if cached_data:
                return cached_data

            # Try fallback to English if not already English
            if language != 'en':
                fallback_key = f"algolia:product:{product_id}:{country}:en"
                cached_data = await self.cache_repo.get(fallback_key)
                if cached_data:
                    logger.debug(f"Using English fallback for product {product_id}")
                    return cached_data

            # Try fallback to UAE if different country
            if country != 'ae':
                fallback_key = f"algolia:product:{product_id}:ae:{language}"
                cached_data = await self.cache_repo.get(fallback_key)
                if cached_data:
                    logger.debug(f"Using UAE fallback for product {product_id}")
                    return cached_data

            # Try UAE + English as final fallback
            if country != 'ae' or language != 'en':
                fallback_key = f"algolia:product:{product_id}:ae:en"
                cached_data = await self.cache_repo.get(fallback_key)
                if cached_data:
                    logger.debug(f"Using UAE+English fallback for product {product_id}")
                    return cached_data

            return None

        except Exception as e:
            logger.error(f"Error getting fallback product data for {product_id}: {e}")
            return None

    async def _get_cached_products_batch(
        self,
        product_ids: List[str],
        country: str,
        language: str
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get multiple products from cache with fallback strategies.
        """
        if not self.cache_repo or not product_ids:
            return {}

        try:
            cached_products = {}

            # Create tasks for concurrent cache lookups
            cache_tasks = []
            for product_id in product_ids:
                cache_tasks.append(
                    self._get_fallback_product_data(product_id, country, language)
                )

            # Execute all cache lookups concurrently
            results = await asyncio.gather(*cache_tasks, return_exceptions=True)

            # Process results
            for product_id, result in zip(product_ids, results):
                if isinstance(result, dict) and result:
                    cached_products[product_id] = result
                elif isinstance(result, Exception):
                    logger.warning(f"Cache lookup failed for product {product_id}: {result}")

            logger.debug(f"Retrieved {len(cached_products)}/{len(product_ids)} products from cache")
            return cached_products

        except Exception as e:
            logger.error(f"Error getting cached products batch: {e}")
            return {}

    async def _warm_product_cache(
        self,
        product_ids: List[str],
        country: str,
        language: str
    ) -> None:
        """
        Warm the cache for frequently accessed products (background task).

        This method runs in the background to pre-fetch and cache products
        that are likely to be accessed again soon.
        """
        try:
            if not self.cache_repo or not product_ids:
                return

            # Check which products are not in cache
            uncached_products = []
            for product_id in product_ids:
                cache_key = f"algolia:product:{product_id}:{country}:{language}"
                if not await self.cache_repo.exists(cache_key):
                    uncached_products.append(product_id)

            if not uncached_products:
                logger.debug("All products already cached, no warming needed")
                return

            # Limit cache warming to avoid overwhelming the system
            max_warm_products = 20
            if len(uncached_products) > max_warm_products:
                uncached_products = uncached_products[:max_warm_products]

            logger.info(f"Warming cache for {len(uncached_products)} products")

            # Fetch products from Algolia
            warmed_products = await self.algolia_service.get_products_by_ids(
                uncached_products, country, language
            )

            # Cache the results with extended TTL for warmed cache
            cache_tasks = []
            extended_ttl = self.settings.CACHE_TTL_PRODUCTS * 2  # Double TTL for warmed cache

            for product_id, product_data in warmed_products.items():
                cache_key = f"algolia:product:{product_id}:{country}:{language}"
                cache_tasks.append(
                    self.cache_repo.set(cache_key, product_data, ttl=extended_ttl)
                )

            if cache_tasks:
                await asyncio.gather(*cache_tasks, return_exceptions=True)
                metrics.counter("cache_warmed_products").inc(len(cache_tasks))
                logger.info(f"Successfully warmed cache for {len(cache_tasks)} products")

        except Exception as e:
            logger.error(f"Error warming product cache: {e}")
            metrics.counter("cache_warming_error").inc()

    async def prefetch_popular_products(
        self,
        country: str = 'ae',
        language: str = 'en',
        limit: int = 100
    ) -> int:
        """
        Prefetch popular products to warm the cache proactively.

        This method can be called periodically to cache frequently accessed products.

        Args:
            country: Country code for localization
            language: Language code for localization
            limit: Maximum number of products to prefetch

        Returns:
            Number of products successfully cached
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Prefetching popular products", extra={
            "correlation_id": correlation_id,
            "country": country,
            "language": language,
            "limit": limit
        })

        try:
            with metrics.timer("prefetch_popular_products_duration"):
                # Get popular product IDs from recent wishlists
                # This is a simplified implementation - in production you might want to
                # track product popularity metrics or use analytics data
                popular_product_ids = await self._get_popular_product_ids(limit)

                if not popular_product_ids:
                    logger.info("No popular products found to prefetch")
                    return 0

                # Check which products are not already cached
                uncached_products = []
                for product_id in popular_product_ids:
                    cache_key = f"algolia:product:{product_id}:{country}:{language}"
                    if not await self.cache_repo.exists(cache_key):
                        uncached_products.append(product_id)

                if not uncached_products:
                    logger.info("All popular products already cached")
                    return 0

                # Fetch and cache products
                products = await self.algolia_service.get_products_by_ids(
                    uncached_products, country, language
                )

                # Cache with extended TTL
                cache_tasks = []
                extended_ttl = self.settings.CACHE_TTL_PRODUCTS * 3  # Triple TTL for popular products

                for product_id, product_data in products.items():
                    cache_key = f"algolia:product:{product_id}:{country}:{language}"
                    cache_tasks.append(
                        self.cache_repo.set(cache_key, product_data, ttl=extended_ttl)
                    )

                if cache_tasks:
                    await asyncio.gather(*cache_tasks, return_exceptions=True)

                cached_count = len(cache_tasks)
                metrics.counter("popular_products_prefetched").inc(cached_count)
                logger.info(f"Successfully prefetched {cached_count} popular products", extra={
                    "correlation_id": correlation_id,
                    "cached_count": cached_count
                })

                return cached_count

        except Exception as e:
            logger.error(f"Error prefetching popular products: {e}", extra={
                "correlation_id": correlation_id,
                "error": str(e)
            })
            metrics.counter("prefetch_popular_products_error").inc()
            return 0

    async def _get_popular_product_ids(self, limit: int) -> List[str]:
        """
        Get popular product IDs based on wishlist data.

        This is a simplified implementation that gets products from recent wishlists.
        In production, you might want to use analytics data or maintain popularity metrics.
        """
        try:
            # This is a placeholder implementation
            # In a real system, you would:
            # 1. Query analytics data for popular products
            # 2. Use machine learning to predict popular products
            # 3. Maintain popularity counters in Redis
            # 4. Use business intelligence data

            # For now, we'll return an empty list
            # The actual implementation would depend on your analytics infrastructure
            return []

        except Exception as e:
            logger.error(f"Error getting popular product IDs: {e}")
            return []

    # Cache Management and Statistics Methods (Subtask 6.4)

    async def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive cache statistics for wishlist operations.

        Returns:
            Dictionary containing cache performance metrics
        """
        try:
            if not self.cache_management_service:
                logger.warning("Cache management service not available")
                return {"error": "Cache management service not available"}

            # Get cache statistics from cache management service
            stats = await self.cache_management_service.get_cache_statistics()

            # Add wishlist-specific metrics
            wishlist_stats = {
                "cache_statistics": stats,
                "service_metrics": {
                    "total_cache_operations": metrics.get_counter_value("cache_operations_total"),
                    "cache_hit_rate": self._calculate_cache_hit_rate(),
                    "avg_response_time_ms": metrics.get_histogram_value("wishlist_operation_duration"),
                    "last_updated": datetime.utcnow().isoformat()
                }
            }

            return wishlist_stats

        except Exception as e:
            logger.error(f"Error getting cache statistics: {e}")
            return {"error": str(e)}

    async def optimize_cache_performance(self) -> Dict[str, Any]:
        """
        Analyze and optimize cache performance for wishlist operations.

        Returns:
            Dictionary with optimization results and recommendations
        """
        try:
            if not self.cache_management_service:
                return {"error": "Cache management service not available"}

            # Run cache optimization
            optimization_results = await self.cache_management_service.optimize_cache_performance()

            # Add wishlist-specific optimizations
            wishlist_optimizations = await self._perform_wishlist_cache_optimizations()
            optimization_results["wishlist_optimizations"] = wishlist_optimizations

            return optimization_results

        except Exception as e:
            logger.error(f"Error optimizing cache performance: {e}")
            return {"error": str(e)}

    async def warm_wishlist_caches(
        self,
        strategy: str = "popular",
        limit: int = 100
    ) -> Dict[str, int]:
        """
        Warm wishlist caches using strategic approaches.

        Args:
            strategy: Warming strategy ("popular", "recent", "predictive")
            limit: Maximum number of items to warm

        Returns:
            Dictionary with warming results by cache type
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Starting wishlist cache warming", extra={
            "correlation_id": correlation_id,
            "strategy": strategy,
            "limit": limit
        })

        try:
            warming_results = {}

            # Warm product cache
            if self.cache_management_service:
                from app.services.cache_management_service import CachePartition
                product_count = await self.cache_management_service.warm_cache_strategically(
                    CachePartition.PRODUCTS, strategy, limit
                )
                warming_results["products"] = product_count

                # Warm wishlist cache
                wishlist_count = await self.cache_management_service.warm_cache_strategically(
                    CachePartition.WISHLISTS, strategy, limit
                )
                warming_results["wishlists"] = wishlist_count

            # Warm frequently accessed user wishlists
            user_warming_count = await self._warm_user_wishlist_cache(strategy, limit)
            warming_results["user_wishlists"] = user_warming_count

            total_warmed = sum(warming_results.values())
            logger.info(f"Cache warming completed", extra={
                "correlation_id": correlation_id,
                "total_warmed": total_warmed,
                "results": warming_results
            })

            return warming_results

        except Exception as e:
            logger.error(f"Error warming wishlist caches: {e}", extra={
                "correlation_id": correlation_id,
                "error": str(e)
            })
            return {"error": str(e)}

    async def invalidate_related_caches(
        self,
        event_type: str,
        context: Dict[str, Any]
    ) -> Dict[str, int]:
        """
        Intelligently invalidate related caches based on event type.

        Args:
            event_type: Type of event that triggered invalidation
            context: Context data for the event

        Returns:
            Dictionary with invalidation results
        """
        try:
            if not self.cache_management_service:
                # Fallback to basic invalidation
                return await self._basic_cache_invalidation(event_type, context)

            # Use intelligent cache invalidation
            return await self.cache_management_service.invalidate_cache_intelligently(
                trigger_event=event_type,
                context=context
            )

        except Exception as e:
            logger.error(f"Error invalidating related caches: {e}")
            return {"error": str(e)}

    # Helper methods for cache management

    def _calculate_cache_hit_rate(self) -> float:
        """Calculate overall cache hit rate for wishlist operations."""
        try:
            hit_count = metrics.get_counter_value("cache_hit_total") or 0
            miss_count = metrics.get_counter_value("cache_miss_total") or 0
            total_requests = hit_count + miss_count

            if total_requests == 0:
                return 0.0

            return hit_count / total_requests
        except Exception:
            return 0.0

    async def _perform_wishlist_cache_optimizations(self) -> Dict[str, Any]:
        """Perform wishlist-specific cache optimizations."""
        optimizations = {
            "actions_taken": [],
            "recommendations": []
        }

        try:
            # Check cache hit rates and recommend optimizations
            hit_rate = self._calculate_cache_hit_rate()

            if hit_rate < 0.7:  # Less than 70% hit rate
                optimizations["recommendations"].append(
                    f"Consider implementing cache warming for frequently accessed wishlists (current hit rate: {hit_rate:.2%})"
                )

            # Check for cache key patterns that might need optimization
            # This would analyze actual cache usage patterns in production
            optimizations["recommendations"].append(
                "Consider implementing cache key compression for large wishlist objects"
            )

            return optimizations

        except Exception as e:
            logger.error(f"Error performing wishlist cache optimizations: {e}")
            return {"error": str(e)}

    async def _warm_user_wishlist_cache(self, strategy: str, limit: int) -> int:
        """Warm cache for user wishlists based on strategy."""
        try:
            # This is a placeholder implementation
            # In production, you would:
            # 1. Identify frequently accessed users
            # 2. Pre-fetch their wishlists
            # 3. Cache the results with appropriate TTL

            warmed_count = 0

            if strategy == "popular":
                # Get popular users (placeholder)
                popular_users = []  # Would come from analytics

                for user_id in popular_users[:limit]:
                    try:
                        # Pre-fetch and cache user wishlists
                        wishlists = await self.get_user_wishlists(user_id)
                        if wishlists:
                            warmed_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to warm cache for user {user_id}: {e}")

            return warmed_count

        except Exception as e:
            logger.error(f"Error warming user wishlist cache: {e}")
            return 0

    async def _basic_cache_invalidation(
        self,
        event_type: str,
        context: Dict[str, Any]
    ) -> Dict[str, int]:
        """Basic cache invalidation fallback when cache management service is unavailable."""
        results = {}

        try:
            user_id = context.get("user_id")
            wishlist_id = context.get("wishlist_id")

            if event_type.startswith("wishlist:") and user_id:
                # Invalidate user and wishlist caches
                user_pattern = f"user_wishlists:{user_id}:*"
                user_count = await self.cache_repo.delete_pattern(user_pattern)
                results[user_pattern] = user_count

                if wishlist_id:
                    wishlist_pattern = f"wishlist:{user_id}:{wishlist_id}:*"
                    wishlist_count = await self.cache_repo.delete_pattern(wishlist_pattern)
                    results[wishlist_pattern] = wishlist_count

            return results

        except Exception as e:
            logger.error(f"Error in basic cache invalidation: {e}")
            return {"error": str(e)}
