"""
Async CloudFront service for cache invalidation with queue integration and retry logic.
"""
import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Union
import json
import uuid

import aioboto3
from botocore.exceptions import ClientError, BotoCoreError
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log
)

from app.config.settings import get_settings
from app.core.exceptions import CloudFrontServiceError
from app.core.metrics import metrics
from app.utils.circuit_breaker import CircuitBreaker

logger = logging.getLogger(__name__)


class CloudFrontInvalidationRequest:
    """Represents a CloudFront invalidation request."""
    
    def __init__(
        self,
        paths: Union[str, List[str]],
        invalidation_type: str = "manual",
        caller_reference: Optional[str] = None,
        priority: int = 0
    ):
        if isinstance(paths, str):
            paths = [paths]
        
        self.paths = self._normalize_paths(paths)
        self.invalidation_type = invalidation_type
        self.caller_reference = caller_reference or f"wishlist-{invalidation_type}-{uuid.uuid4().hex[:8]}-{int(datetime.utcnow().timestamp())}"
        self.priority = priority
        self.created_at = datetime.utcnow()
    
    def _normalize_paths(self, paths: List[str]) -> List[str]:
        """Ensure all paths start with a slash."""
        normalized = []
        for path in paths:
            if not path.startswith('/'):
                path = '/' + path
            normalized.append(path)
        return normalized
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "paths": self.paths,
            "invalidation_type": self.invalidation_type,
            "caller_reference": self.caller_reference,
            "priority": self.priority,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CloudFrontInvalidationRequest':
        """Create from dictionary."""
        request = cls(
            paths=data["paths"],
            invalidation_type=data["invalidation_type"],
            caller_reference=data["caller_reference"],
            priority=data.get("priority", 0)
        )
        if "created_at" in data:
            request.created_at = datetime.fromisoformat(data["created_at"])
        return request


class CloudFrontService:
    """
    Async CloudFront service for cache invalidation with batching and retry logic.
    """
    
    def __init__(self):
        self.settings = get_settings()
        self._session: Optional[aioboto3.Session] = None
        self._client = None
        
        # Configuration
        self.distribution_id = self.settings.CLOUDFRONT_DISTRIBUTION_ID
        self.region = self.settings.DYNAMODB_REGION  # Use same region as DynamoDB
        
        # Create fallback function for CloudFront service
        async def cloudfront_fallback(*args, **kwargs):
            """Fallback function for CloudFront invalidation failures."""
            logger.warning("Using CloudFront fallback - invalidation service unavailable")
            
            # Log the failed invalidation for manual processing
            if len(args) >= 2:
                paths, caller_reference = args[:2]
                logger.error(
                    "CloudFront invalidation failed - manual intervention may be required",
                    extra={
                        "paths": paths,
                        "caller_reference": caller_reference,
                        "paths_count": len(paths) if isinstance(paths, list) else 1
                    }
                )
            
            # Return a fallback response indicating the operation was queued for retry
            return {
                "status": "fallback",
                "message": "CloudFront service unavailable, invalidation queued for retry",
                "invalidation_id": f"fallback-{uuid.uuid4().hex[:8]}",
                "paths_count": len(args[0]) if args and isinstance(args[0], list) else 1
            }
        
        # Circuit breaker for resilience
        self._circuit_breaker = CircuitBreaker(
            failure_threshold=self.settings.CIRCUIT_BREAKER_THRESHOLD,
            recovery_timeout=self.settings.CIRCUIT_BREAKER_TIMEOUT,
            expected_exception=(ClientError, BotoCoreError, CloudFrontServiceError),
            name="cloudfront_service",
            fallback_function=cloudfront_fallback,
            success_threshold=1,  # Single success to close from half-open
            timeout=30  # 30 second timeout for CloudFront operations
        )
        
        # Batch processing configuration
        self.max_batch_size = 3000  # CloudFront limit
        self.batch_timeout = 5  # seconds
        self._pending_requests: List[CloudFrontInvalidationRequest] = []
        self._batch_lock = asyncio.Lock()
        self._batch_task: Optional[asyncio.Task] = None
        
        logger.info("CloudFrontService initialized", extra={
            "distribution_id": self.distribution_id,
            "region": self.region,
            "max_batch_size": self.max_batch_size
        })
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_client()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_client(self):
        """Ensure CloudFront client is created."""
        if self._client is None:
            self._session = aioboto3.Session()
            self._client = self._session.client(
                'cloudfront',
                region_name=self.region,
                aws_access_key_id=self.settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=self.settings.AWS_SECRET_ACCESS_KEY
            )
            logger.debug("Created CloudFront client")
    
    async def close(self):
        """Close the CloudFront client and cleanup resources."""
        if self._batch_task and not self._batch_task.done():
            self._batch_task.cancel()
            try:
                await self._batch_task
            except asyncio.CancelledError:
                pass
        
        if self._client:
            await self._client.close()
            self._client = None
            logger.debug("Closed CloudFront client")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((ClientError, BotoCoreError)),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    async def _create_invalidation(
        self, 
        paths: List[str], 
        caller_reference: str
    ) -> Dict[str, Any]:
        """Create CloudFront invalidation with retry logic."""
        if not self.distribution_id:
            logger.warning("CloudFront distribution ID not configured, skipping invalidation")
            return {"status": "skipped", "reason": "no_distribution_id"}
        
        await self._ensure_client()
        
        try:
            response = await self._client.create_invalidation(
                DistributionId=self.distribution_id,
                InvalidationBatch={
                    'Paths': {
                        'Quantity': len(paths),
                        'Items': paths
                    },
                    'CallerReference': caller_reference
                }
            )
            
            invalidation = response['Invalidation']
            logger.info("CloudFront invalidation created", extra={
                "invalidation_id": invalidation['Id'],
                "status": invalidation['Status'],
                "paths_count": len(paths),
                "caller_reference": caller_reference
            })
            
            return {
                "status": "success",
                "invalidation_id": invalidation['Id'],
                "invalidation_status": invalidation['Status'],
                "paths_count": len(paths)
            }
            
        except ClientError as e:
            error_code = e.response.get('Error', {}).get('Code', 'Unknown')
            error_message = e.response.get('Error', {}).get('Message', str(e))
            
            logger.error("CloudFront invalidation failed", extra={
                "error_code": error_code,
                "error_message": error_message,
                "paths_count": len(paths),
                "caller_reference": caller_reference
            })
            
            raise CloudFrontServiceError(f"CloudFront invalidation failed: {error_code} - {error_message}")
    
    async def invalidate_path(self, path: str, priority: int = 0) -> bool:
        """
        Invalidate a single path in CloudFront.
        
        Args:
            path: The path to invalidate
            priority: Priority for batching (higher = processed first)
        
        Returns:
            True if invalidation was queued/processed successfully
        """
        return await self.invalidate_paths([path], priority=priority)
    
    async def invalidate_paths(self, paths: List[str], priority: int = 0) -> bool:
        """
        Invalidate multiple paths in CloudFront with batching.
        
        Args:
            paths: List of paths to invalidate
            priority: Priority for batching (higher = processed first)
        
        Returns:
            True if invalidation was queued/processed successfully
        """
        if not paths:
            return True
        
        try:
            request = CloudFrontInvalidationRequest(
                paths=paths,
                invalidation_type="manual",
                priority=priority
            )
            
            # Add to batch queue
            await self._add_to_batch(request)
            
            metrics.counter("cloudfront_invalidation_queued", {"paths_count": len(paths)}).inc()
            logger.debug(f"Queued {len(paths)} paths for CloudFront invalidation")
            
            return True
            
        except Exception as e:
            logger.error(f"Error queuing CloudFront invalidation: {e}")
            metrics.counter("cloudfront_invalidation_error", {"operation": "queue"}).inc()
            return False
    
    async def invalidate_wishlist(self, wishlist_id: str, user_id: Optional[str] = None) -> bool:
        """
        Invalidate all wishlist-related paths for a specific wishlist.
        
        Args:
            wishlist_id: The wishlist ID
            user_id: Optional user ID for additional paths
        
        Returns:
            True if invalidation was queued successfully
        """
        paths = [
            f"/api/v1/wishlists/{wishlist_id}",
            f"/api/v1/wishlists/{wishlist_id}/*",
        ]
        
        # Add user-specific paths if user_id provided
        if user_id:
            paths.extend([
                f"/api/v1/wishlists?user_id={user_id}",
                f"/api/v1/wishlists?user_id={user_id}&*",
            ])
        
        return await self.invalidate_paths(paths, priority=1)
    
    async def invalidate_shared_wishlist(self, share_hash: str) -> bool:
        """
        Invalidate shared wishlist paths for a specific hash.
        
        Args:
            share_hash: The share hash
        
        Returns:
            True if invalidation was queued successfully
        """
        paths = [f"/api/v1/wishlists/shared/{share_hash}"]
        return await self.invalidate_paths(paths, priority=2)
    
    async def invalidate_user_wishlists(self, user_id: str) -> bool:
        """
        Invalidate all wishlists for a user.
        
        Args:
            user_id: The user ID
        
        Returns:
            True if invalidation was queued successfully
        """
        paths = [
            f"/api/v1/wishlists?user_id={user_id}",
            f"/api/v1/wishlists?user_id={user_id}&*",
        ]
        return await self.invalidate_paths(paths, priority=1)
    
    async def _add_to_batch(self, request: CloudFrontInvalidationRequest):
        """Add invalidation request to batch queue."""
        async with self._batch_lock:
            self._pending_requests.append(request)
            
            # Start batch processing task if not running
            if self._batch_task is None or self._batch_task.done():
                self._batch_task = asyncio.create_task(self._process_batch_queue())
    
    async def _process_batch_queue(self):
        """Process batch queue with timeout and size limits."""
        try:
            while True:
                await asyncio.sleep(self.batch_timeout)
                
                async with self._batch_lock:
                    if not self._pending_requests:
                        break
                    
                    # Sort by priority (higher first) and creation time
                    self._pending_requests.sort(
                        key=lambda r: (-r.priority, r.created_at)
                    )
                    
                    # Process batch
                    await self._process_current_batch()
                    
        except asyncio.CancelledError:
            logger.debug("Batch processing task cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
    
    async def _process_current_batch(self):
        """Process current batch of invalidation requests."""
        if not self._pending_requests:
            return
        
        # Collect all paths from pending requests
        all_paths = []
        processed_requests = []
        
        for request in self._pending_requests:
            if len(all_paths) + len(request.paths) <= self.max_batch_size:
                all_paths.extend(request.paths)
                processed_requests.append(request)
            else:
                break
        
        if not all_paths:
            return
        
        # Remove processed requests from queue
        for request in processed_requests:
            self._pending_requests.remove(request)
        
        # Create batch invalidation
        try:
            caller_reference = f"batch-{uuid.uuid4().hex[:8]}-{int(datetime.utcnow().timestamp())}"
            
            result = await self._circuit_breaker.call(
                self._create_invalidation,
                all_paths,
                caller_reference
            )
            
            if result.get("status") == "success":
                metrics.counter("cloudfront_invalidation_success", {
                    "paths_count": len(all_paths),
                    "batch_size": len(processed_requests)
                }).inc()
                
                logger.info("Batch CloudFront invalidation completed", extra={
                    "paths_count": len(all_paths),
                    "requests_count": len(processed_requests),
                    "invalidation_id": result.get("invalidation_id")
                })
            else:
                logger.warning("CloudFront invalidation skipped", extra={
                    "reason": result.get("reason"),
                    "paths_count": len(all_paths)
                })
                
        except Exception as e:
            logger.error(f"Batch CloudFront invalidation failed: {e}")
            metrics.counter("cloudfront_invalidation_error", {"operation": "batch"}).inc()
            
            # Re-queue failed requests with lower priority
            for request in processed_requests:
                request.priority = max(0, request.priority - 1)
                self._pending_requests.append(request)
    
    async def flush_pending_invalidations(self) -> bool:
        """
        Force process all pending invalidations immediately.
        
        Returns:
            True if all pending invalidations were processed successfully
        """
        try:
            async with self._batch_lock:
                if self._pending_requests:
                    await self._process_current_batch()
            
            return True
            
        except Exception as e:
            logger.error(f"Error flushing pending invalidations: {e}")
            return False
    
    async def get_invalidation_status(self, invalidation_id: str) -> Dict[str, Any]:
        """
        Get the status of a CloudFront invalidation.
        
        Args:
            invalidation_id: The invalidation ID
        
        Returns:
            Dictionary with invalidation status information
        """
        if not self.distribution_id:
            return {"status": "error", "message": "Distribution ID not configured"}
        
        try:
            await self._ensure_client()
            
            response = await self._client.get_invalidation(
                DistributionId=self.distribution_id,
                Id=invalidation_id
            )
            
            invalidation = response['Invalidation']
            return {
                "status": "success",
                "invalidation_id": invalidation['Id'],
                "invalidation_status": invalidation['Status'],
                "create_time": invalidation['CreateTime'].isoformat(),
                "paths_count": invalidation['InvalidationBatch']['Paths']['Quantity']
            }
            
        except ClientError as e:
            error_message = e.response.get('Error', {}).get('Message', str(e))
            return {
                "status": "error",
                "message": error_message
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on CloudFront service.
        
        Returns:
            Dictionary with health status information
        """
        try:
            if not self.distribution_id:
                return {
                    "status": "degraded",
                    "message": "Distribution ID not configured",
                    "circuit_breaker_state": self._circuit_breaker.state
                }
            
            await self._ensure_client()
            
            # Try to get distribution info as a health check
            start_time = datetime.utcnow()
            await self._client.get_distribution(Id=self.distribution_id)
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "status": "healthy",
                "response_time_seconds": response_time,
                "circuit_breaker_state": self._circuit_breaker.state,
                "pending_requests": len(self._pending_requests),
                "distribution_id": self.distribution_id
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "circuit_breaker_state": self._circuit_breaker.state,
                "pending_requests": len(self._pending_requests)
            }


# Singleton instance for dependency injection
_cloudfront_service: Optional[CloudFrontService] = None


async def get_cloudfront_service() -> CloudFrontService:
    """Get or create CloudFront service instance."""
    global _cloudfront_service
    
    if _cloudfront_service is None:
        _cloudfront_service = CloudFrontService()
    
    return _cloudfront_service


async def close_cloudfront_service():
    """Close CloudFront service and cleanup resources."""
    global _cloudfront_service
    
    if _cloudfront_service:
        await _cloudfront_service.close()
        _cloudfront_service = None