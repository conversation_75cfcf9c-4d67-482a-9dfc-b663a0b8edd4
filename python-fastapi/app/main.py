"""
FastAPI application entry point with proper lifecycle management.
"""
from contextlib import asynccontextmanager
from typing import Async<PERSON><PERSON>ator

import uvicorn
from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.openapi.docs import (
    get_redoc_html,
    get_swagger_ui_html,
    get_swagger_ui_oauth2_redirect_html,
)

from app.api.v1.router import api_router
from app.api.middleware import (
    RequestLoggingMiddleware,
    MetricsMiddleware,
    RateLimitMiddleware,
    SecurityHeadersMiddleware,
)
from app.config.settings import get_settings
from app.api.error_handlers import register_error_handlers


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager for startup and shutdown events.
    """
    settings = get_settings()

    # Startup
    print(f"Starting {settings.APP_NAME} v{settings.VERSION}")

    # Initialize connections and resources here
    # This will be expanded in later tasks for database connections

    yield

    # Shutdown
    print(f"Shutting down {settings.APP_NAME}")
    # Cleanup connections and resources here


def create_app() -> FastAPI:
    """
    Application factory pattern for creating FastAPI app instance.
    This pattern improves testability and allows for different configurations.
    """
    settings = get_settings()

    app = FastAPI(
        title=settings.APP_NAME,
        description="High-performance wishlist microservice built with FastAPI",
        version=settings.VERSION,
        lifespan=lifespan,
        docs_url=None,  # We'll create custom docs endpoints
        redoc_url=None,  # We'll create custom redoc endpoints
        openapi_url="/openapi.json" if settings.DEBUG else None,
    )

    # Configure middleware stack (order matters - last added is executed first)

    # Compression middleware (should be one of the last)
    app.add_middleware(
        GZipMiddleware,
        minimum_size=1000
    )

    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        allow_headers=["*"],
        expose_headers=["X-Request-ID", "X-RateLimit-Remaining", "X-RateLimit-Reset"],
    )

    # Security headers middleware
    app.add_middleware(SecurityHeadersMiddleware)

    # Rate limiting middleware
    app.add_middleware(RateLimitMiddleware)

    # Metrics collection middleware
    app.add_middleware(MetricsMiddleware)

    # Request logging middleware (should be early in the chain)
    app.add_middleware(RequestLoggingMiddleware)

    # Setup exception handlers
    register_error_handlers(app)

    # Include API routers with versioning
    app.include_router(
        api_router,
        prefix=settings.API_V1_PREFIX
    )

    # Custom documentation endpoints (only in debug mode)
    if settings.DEBUG:
        @app.get("/docs", include_in_schema=False)
        async def custom_swagger_ui_html():
            return get_swagger_ui_html(
                openapi_url=app.openapi_url,
                title=app.title + " - Swagger UI",
                oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
                swagger_js_url="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js",
                swagger_css_url="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css",
                swagger_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
            )

        @app.get("/docs-simple", include_in_schema=False)
        async def simple_docs():
            """Simple API documentation without external CDN dependencies."""
            from fastapi.responses import HTMLResponse

            html_content = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <title>{app.title} - API Documentation</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .endpoint {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
                    .method {{ font-weight: bold; color: #fff; padding: 5px 10px; border-radius: 3px; }}
                    .get {{ background-color: #61affe; }}
                    .post {{ background-color: #49cc90; }}
                    .put {{ background-color: #fca130; }}
                    .delete {{ background-color: #f93e3e; }}
                    .path {{ font-family: monospace; font-size: 16px; margin: 10px 0; }}
                    .description {{ color: #666; margin: 10px 0; }}
                    pre {{ background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }}
                </style>
            </head>
            <body>
                <h1>{app.title}</h1>
                <p>{app.description}</p>
                <p><strong>Version:</strong> {app.version}</p>

                <h2>Available Endpoints</h2>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/</div>
                    <div class="description">Root endpoint - Service information</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/health/</div>
                    <div class="description">Basic health check</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/health/detailed</div>
                    <div class="description">Detailed health check with dependency status</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/health/ready</div>
                    <div class="description">Kubernetes readiness probe</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/health/live</div>
                    <div class="description">Kubernetes liveness probe</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/health/metrics</div>
                    <div class="description">Prometheus-compatible metrics</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/health/status</div>
                    <div class="description">Status dashboard</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/wishlists/</div>
                    <div class="description">Get wishlists (placeholder)</div>
                </div>

                <div class="endpoint">
                    <span class="method post">POST</span>
                    <div class="path">/api/v1/wishlists/{{wishlist_id}}/items</div>
                    <div class="description">Add item to wishlist (placeholder)</div>
                </div>

                <div class="endpoint">
                    <span class="method get">GET</span>
                    <div class="path">/api/v1/wishlists/shared/{{hash}}</div>
                    <div class="description">Get shared wishlist (placeholder)</div>
                </div>

                <h2>OpenAPI Schema</h2>
                <p><a href="/openapi.json" target="_blank">View OpenAPI JSON Schema</a></p>

                <h2>Alternative Documentation</h2>
                <p><a href="/docs" target="_blank">Swagger UI (requires CDN access)</a></p>
                <p><a href="/redoc" target="_blank">ReDoc (requires CDN access)</a></p>

                <h2>Example Usage</h2>
                <pre>
# Check service health
curl http://localhost:8000/api/v1/health/

# Get detailed health information
curl http://localhost:8000/api/v1/health/detailed

# Get service metrics
curl http://localhost:8000/api/v1/health/metrics
                </pre>
            </body>
            </html>
            """
            return HTMLResponse(content=html_content)

        @app.get(app.swagger_ui_oauth2_redirect_url, include_in_schema=False)
        async def swagger_ui_redirect():
            return get_swagger_ui_oauth2_redirect_html()

        @app.get("/redoc", include_in_schema=False)
        async def redoc_html():
            return get_redoc_html(
                openapi_url=app.openapi_url,
                title=app.title + " - ReDoc",
                redoc_js_url="https://unpkg.com/redoc@2.1.3/bundles/redoc.standalone.js",
                redoc_favicon_url="https://fastapi.tiangolo.com/img/favicon.png",
            )

    # Root endpoint
    @app.get("/", include_in_schema=False)
    async def root():
        return JSONResponse({
            "service": settings.APP_NAME,
            "version": settings.VERSION,
            "status": "running"
        })

    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        workers=1,  # App Runner handles scaling, single worker per instance
        # loop="uvloop",  # High-performance event loop (optional)
        # http="httptools",  # High-performance HTTP parser (optional)
        log_level=settings.LOG_LEVEL.lower(),
    )
