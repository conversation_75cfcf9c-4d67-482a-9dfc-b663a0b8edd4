"""
Wishlist request schemas.

This module contains Pydantic schemas for validating wishlist-related API requests.
All schemas include comprehensive validation rules and custom validators.
"""

from pydantic import Field, field_validator, model_validator
from typing import Optional, List
from datetime import datetime

from ..base import (
    BaseRequestSchema,
    PaginationSchema,
    validate_user_id,
    validate_wishlist_name,
    CountryCode,
    LanguageCode
)


class CreateWishlistRequest(BaseRequestSchema):
    """
    Schema for creating a new wishlist.
    
    Validates all required fields and business rules for wishlist creation.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user creating the wishlist",
        example="user_12345"
    )
    name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Name of the wishlist",
        example="My Birthday Wishlist"
    )
    is_default: bool = Field(
        default=False,
        description="Whether this should be the user's default wishlist"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    @field_validator('name')
    @classmethod
    def validate_name_field(cls, v):
        return validate_wishlist_name(v)
    
    @model_validator(mode='after')
    def validate_request(self):
        """Perform cross-field validation."""
        # Additional business logic validation can be added here
        return self
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "name": "My Birthday Wishlist",
                    "is_default": False,
                    "country": "ae",
                    "language": "en"
                },
                {
                    "user_id": "user_67890",
                    "name": "قائمة أمنياتي",
                    "is_default": True,
                    "country": "sa",
                    "language": "ar"
                }
            ]
        }


class UpdateWishlistRequest(BaseRequestSchema):
    """
    Schema for updating an existing wishlist.
    
    All fields are optional to support partial updates.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user updating the wishlist",
        example="user_12345"
    )
    name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=255,
        description="New name for the wishlist",
        example="Updated Wishlist Name"
    )
    is_default: Optional[bool] = Field(
        default=None,
        description="Whether this should be the user's default wishlist"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    @field_validator('name')
    @classmethod
    def validate_name_field(cls, v):
        if v is not None:
            return validate_wishlist_name(v)
        return v
    
    @model_validator(mode='after')
    def validate_update_request(self):
        """Ensure at least one field is being updated."""
        if self.name is None and self.is_default is None:
            raise ValueError("At least one field must be provided for update")
        return self
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "name": "Updated Wishlist Name",
                    "country": "ae",
                    "language": "en"
                },
                {
                    "user_id": "user_12345",
                    "is_default": True,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class UpdateWishlistPrivacyRequest(BaseRequestSchema):
    """
    Schema for updating wishlist privacy settings.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user updating the wishlist",
        example="user_12345"
    )
    is_public: bool = Field(
        ...,
        description="Whether the wishlist should be publicly accessible"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "is_public": True,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class RegenerateHashRequest(BaseRequestSchema):
    """
    Schema for regenerating a wishlist's share hash.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user regenerating the hash",
        example="user_12345"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class GetWishlistsRequest(BaseRequestSchema, PaginationSchema):
    """
    Schema for getting user's wishlists with optional filtering and pagination.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user whose wishlists to retrieve",
        example="user_12345"
    )
    include_items: bool = Field(
        default=True,
        description="Whether to include wishlist items in the response"
    )
    include_product_details: bool = Field(
        default=True,
        description="Whether to include enriched product details"
    )
    default_only: bool = Field(
        default=False,
        description="Whether to return only the default wishlist"
    )
    public_only: bool = Field(
        default=False,
        description="Whether to return only public wishlists"
    )
    sort_by: Optional[str] = Field(
        default="updated_at",
        description="Field to sort by",
        pattern="^(name|created_at|updated_at|item_count)$"
    )
    sort_order: Optional[str] = Field(
        default="desc",
        description="Sort order",
        pattern="^(asc|desc)$"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    @field_validator('sort_by')
    @classmethod
    def validate_sort_by(cls, v):
        """Validate sort field."""
        if v is None:
            return "updated_at"
        
        allowed_fields = ["name", "created_at", "updated_at", "item_count"]
        if v not in allowed_fields:
            raise ValueError(f"sort_by must be one of: {', '.join(allowed_fields)}")
        
        return v
    
    @field_validator('sort_order')
    @classmethod
    def validate_sort_order(cls, v):
        """Validate sort order."""
        if v is None:
            return "desc"
        
        if v.lower() not in ["asc", "desc"]:
            raise ValueError("sort_order must be 'asc' or 'desc'")
        
        return v.lower()
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "include_items": True,
                    "include_product_details": True,
                    "page": 1,
                    "page_size": 20,
                    "country": "ae",
                    "language": "en"
                },
                {
                    "user_id": "user_12345",
                    "default_only": True,
                    "include_product_details": False,
                    "country": "sa",
                    "language": "ar"
                }
            ]
        }


class GetWishlistRequest(BaseRequestSchema):
    """
    Schema for getting a specific wishlist by ID.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user who owns the wishlist",
        example="user_12345"
    )
    include_items: bool = Field(
        default=True,
        description="Whether to include wishlist items in the response"
    )
    include_product_details: bool = Field(
        default=True,
        description="Whether to include enriched product details"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "include_items": True,
                    "include_product_details": True,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class DeleteWishlistRequest(BaseRequestSchema):
    """
    Schema for deleting a wishlist.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user deleting the wishlist",
        example="user_12345"
    )
    force: bool = Field(
        default=False,
        description="Whether to force delete even if it's the default wishlist"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "force": False,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }