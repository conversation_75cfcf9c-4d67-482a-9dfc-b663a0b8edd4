"""
Request schemas package.

This package contains all Pydantic schemas for API request validation.
"""

from .wishlist import (
    CreateWishlistRequest,
    UpdateWishlistRequest,
    UpdateWishlistPrivacyRequest,
    RegenerateHashRequest,
    GetWishlistsRequest,
    GetWishlistRequest
)
from .item import (
    AddItemRequest,
    UpdateItemRequest,
    RemoveItemRequest,
    BulkAddItemsRequest,
    BulkRemoveItemsRequest
)
from .shared import (
    GetSharedWishlistRequest
)

__all__ = [
    # Wishlist requests
    'CreateWishlistRequest',
    'UpdateWishlistRequest',
    'UpdateWishlistPrivacyRequest',
    'RegenerateHashRequest',
    'GetWishlistsRequest',
    'GetWishlistRequest',
    
    # Item requests
    'AddItemRequest',
    'UpdateItemRequest',
    'RemoveItemRequest',
    'BulkAddItemsRequest',
    'BulkRemoveItemsRequest',
    
    # Shared wishlist requests
    'GetSharedWishlistRequest',
]