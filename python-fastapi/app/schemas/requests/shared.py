"""
Shared wishlist request schemas.

This module contains Pydantic schemas for validating shared/public wishlist requests.
"""

from pydantic import Field, field_validator
from typing import Optional

from ..base import (
    BaseRequestSchema,
    validate_share_hash,
    CountryCode,
    LanguageCode
)


class GetSharedWishlistRequest(BaseRequestSchema):
    """
    Schema for accessing a shared/public wishlist via share hash.
    
    This endpoint doesn't require user authentication since it's for public access.
    """
    
    include_items: bool = Field(
        default=True,
        description="Whether to include wishlist items in the response"
    )
    include_product_details: bool = Field(
        default=True,
        description="Whether to include enriched product details"
    )
    visitor_id: Optional[str] = Field(
        default=None,
        max_length=100,
        description="Optional visitor ID for analytics tracking",
        example="visitor_abc123"
    )
    
    @field_validator('visitor_id')
    @classmethod
    def validate_visitor_id(cls, v):
        """Validate visitor ID format."""
        if v is None:
            return None
        
        v = v.strip()
        if not v:
            return None
        
        if len(v) > 100:
            raise ValueError("visitor_id cannot exceed 100 characters")
        
        return v
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "include_items": True,
                    "include_product_details": True,
                    "visitor_id": "visitor_abc123",
                    "country": "ae",
                    "language": "en"
                },
                {
                    "include_items": True,
                    "include_product_details": False,
                    "country": "sa",
                    "language": "ar"
                }
            ]
        }


class ShareWishlistRequest(BaseRequestSchema):
    """
    Schema for sharing a wishlist (making it public).
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user sharing the wishlist",
        example="user_12345"
    )
    message: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Optional message to include with the shared wishlist",
        example="Check out my wishlist for my birthday!"
    )
    expires_at: Optional[str] = Field(
        default=None,
        description="Optional expiration date for the shared link (ISO format)",
        example="2024-12-31T23:59:59Z"
    )
    
    @field_validator('message')
    @classmethod
    def validate_message(cls, v):
        """Validate sharing message."""
        if v is None:
            return None
        
        v = v.strip()
        if not v:
            return None
        
        if len(v) > 500:
            raise ValueError("message cannot exceed 500 characters")
        
        return v
    
    @field_validator('expires_at')
    @classmethod
    def validate_expires_at(cls, v):
        """Validate expiration date format."""
        if v is None:
            return None
        
        # Basic ISO format validation - more detailed validation would be done in the service layer
        if not isinstance(v, str) or len(v) < 10:
            raise ValueError("expires_at must be a valid ISO date string")
        
        return v
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "message": "Check out my wishlist for my birthday!",
                    "expires_at": "2024-12-31T23:59:59Z",
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class UnshareWishlistRequest(BaseRequestSchema):
    """
    Schema for unsharing a wishlist (making it private).
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user unsharing the wishlist",
        example="user_12345"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class GetSharedWishlistStatsRequest(BaseRequestSchema):
    """
    Schema for getting statistics about a shared wishlist.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user who owns the wishlist",
        example="user_12345"
    )
    date_from: Optional[str] = Field(
        default=None,
        description="Start date for statistics (ISO format)",
        example="2024-01-01T00:00:00Z"
    )
    date_to: Optional[str] = Field(
        default=None,
        description="End date for statistics (ISO format)",
        example="2024-12-31T23:59:59Z"
    )
    
    @field_validator('date_from', 'date_to')
    @classmethod
    def validate_date_format(cls, v):
        """Validate date format."""
        if v is None:
            return None
        
        if not isinstance(v, str) or len(v) < 10:
            raise ValueError("date must be a valid ISO date string")
        
        return v
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "date_from": "2024-01-01T00:00:00Z",
                    "date_to": "2024-12-31T23:59:59Z",
                    "country": "ae",
                    "language": "en"
                }
            ]
        }