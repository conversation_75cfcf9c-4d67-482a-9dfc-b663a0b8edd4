"""
Common response schemas.

This module contains Pydantic schemas for common API responses like health checks,
metrics, and system status information.
"""

from pydantic import Field
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..base import BaseResponseSchema, SuccessResponseSchema


class ServiceStatus(BaseResponseSchema):
    """
    Schema for individual service status information.
    """
    
    name: str = Field(
        description="Service name",
        example="dynamodb"
    )
    status: str = Field(
        description="Service status",
        example="healthy"  # healthy, unhealthy, degraded
    )
    response_time_ms: Optional[float] = Field(
        default=None,
        description="Response time in milliseconds",
        example=12.5
    )
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional service details"
    )
    last_checked: datetime = Field(
        description="When the service was last checked",
        example="2024-01-16T16:00:00Z"
    )


class HealthCheckResponse(BaseResponseSchema):
    """
    Schema for health check responses.
    """
    
    status: str = Field(
        description="Overall system status",
        example="healthy"  # healthy, unhealthy, degraded
    )
    version: str = Field(
        description="Application version",
        example="2.0.0"
    )
    timestamp: datetime = Field(
        description="Health check timestamp",
        default_factory=datetime.utcnow
    )
    uptime_seconds: float = Field(
        description="Application uptime in seconds",
        example=3600.5
    )
    services: List[ServiceStatus] = Field(
        description="Status of individual services"
    )
    system_info: Dict[str, Any] = Field(
        description="System information",
        example={
            "python_version": "3.11.0",
            "platform": "linux",
            "memory_usage_mb": 128.5,
            "cpu_usage_percent": 15.2
        }
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "status": "healthy",
                    "version": "2.0.0",
                    "timestamp": "2024-01-16T16:00:00Z",
                    "uptime_seconds": 3600.5,
                    "services": [
                        {
                            "name": "dynamodb",
                            "status": "healthy",
                            "response_time_ms": 12.5,
                            "details": {
                                "region": "us-east-1",
                                "tables_accessible": 2
                            },
                            "last_checked": "2024-01-16T16:00:00Z"
                        },
                        {
                            "name": "redis",
                            "status": "healthy",
                            "response_time_ms": 3.2,
                            "details": {
                                "connected_clients": 5,
                                "memory_usage_mb": 45.2
                            },
                            "last_checked": "2024-01-16T16:00:00Z"
                        },
                        {
                            "name": "algolia",
                            "status": "healthy",
                            "response_time_ms": 89.1,
                            "details": {
                                "indexes_accessible": 4
                            },
                            "last_checked": "2024-01-16T16:00:00Z"
                        }
                    ],
                    "system_info": {
                        "python_version": "3.11.0",
                        "platform": "linux",
                        "memory_usage_mb": 128.5,
                        "cpu_usage_percent": 15.2
                    }
                }
            ]
        }


class MetricValue(BaseResponseSchema):
    """
    Schema for individual metric values.
    """
    
    name: str = Field(
        description="Metric name",
        example="requests_total"
    )
    value: float = Field(
        description="Metric value",
        example=1234.0
    )
    labels: Optional[Dict[str, str]] = Field(
        default=None,
        description="Metric labels",
        example={"method": "GET", "endpoint": "/wishlists"}
    )
    timestamp: datetime = Field(
        description="When the metric was recorded",
        example="2024-01-16T16:00:00Z"
    )


class MetricsResponse(BaseResponseSchema):
    """
    Schema for metrics endpoint responses.
    """
    
    metrics: List[MetricValue] = Field(
        description="List of application metrics"
    )
    collection_time: datetime = Field(
        description="When the metrics were collected",
        default_factory=datetime.utcnow
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "metrics": [
                        {
                            "name": "requests_total",
                            "value": 1234.0,
                            "labels": {"method": "GET", "endpoint": "/wishlists"},
                            "timestamp": "2024-01-16T16:00:00Z"
                        },
                        {
                            "name": "request_duration_seconds",
                            "value": 0.125,
                            "labels": {"method": "POST", "endpoint": "/wishlists"},
                            "timestamp": "2024-01-16T16:00:00Z"
                        },
                        {
                            "name": "cache_hits_total",
                            "value": 856.0,
                            "labels": {"cache_type": "redis"},
                            "timestamp": "2024-01-16T16:00:00Z"
                        }
                    ],
                    "collection_time": "2024-01-16T16:00:00Z"
                }
            ]
        }


class CacheStats(BaseResponseSchema):
    """
    Schema for cache statistics.
    """
    
    cache_type: str = Field(
        description="Type of cache",
        example="redis"
    )
    total_keys: int = Field(
        description="Total number of keys in cache",
        example=1250
    )
    hit_rate: float = Field(
        description="Cache hit rate (0.0 to 1.0)",
        example=0.85
    )
    miss_rate: float = Field(
        description="Cache miss rate (0.0 to 1.0)",
        example=0.15
    )
    total_hits: int = Field(
        description="Total cache hits",
        example=8560
    )
    total_misses: int = Field(
        description="Total cache misses",
        example=1510
    )
    memory_usage_mb: Optional[float] = Field(
        default=None,
        description="Memory usage in MB",
        example=45.2
    )
    evictions: Optional[int] = Field(
        default=None,
        description="Number of evicted keys",
        example=23
    )


class CacheStatsResponse(BaseResponseSchema):
    """
    Schema for cache statistics responses.
    """
    
    caches: List[CacheStats] = Field(
        description="Statistics for different cache types"
    )
    collection_time: datetime = Field(
        description="When the statistics were collected",
        default_factory=datetime.utcnow
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "caches": [
                        {
                            "cache_type": "redis",
                            "total_keys": 1250,
                            "hit_rate": 0.85,
                            "miss_rate": 0.15,
                            "total_hits": 8560,
                            "total_misses": 1510,
                            "memory_usage_mb": 45.2,
                            "evictions": 23
                        },
                        {
                            "cache_type": "in_memory",
                            "total_keys": 150,
                            "hit_rate": 0.92,
                            "miss_rate": 0.08,
                            "total_hits": 2300,
                            "total_misses": 200,
                            "memory_usage_mb": 12.8,
                            "evictions": 5
                        }
                    ],
                    "collection_time": "2024-01-16T16:00:00Z"
                }
            ]
        }


class SystemInfoResponse(BaseResponseSchema):
    """
    Schema for system information responses.
    """
    
    application: Dict[str, Any] = Field(
        description="Application information",
        example={
            "name": "Mumzworld Wishlist Service",
            "version": "2.0.0",
            "environment": "production",
            "build_date": "2024-01-15T10:00:00Z",
            "git_commit": "abc123def456"
        }
    )
    runtime: Dict[str, Any] = Field(
        description="Runtime information",
        example={
            "python_version": "3.11.0",
            "platform": "linux",
            "architecture": "x86_64",
            "uptime_seconds": 3600.5
        }
    )
    resources: Dict[str, Any] = Field(
        description="Resource usage information",
        example={
            "memory_usage_mb": 128.5,
            "cpu_usage_percent": 15.2,
            "disk_usage_percent": 45.0,
            "open_file_descriptors": 125
        }
    )
    dependencies: Dict[str, str] = Field(
        description="Key dependency versions",
        example={
            "fastapi": "0.104.1",
            "pydantic": "2.5.0",
            "uvicorn": "0.24.0",
            "redis": "5.0.1"
        }
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "application": {
                        "name": "Mumzworld Wishlist Service",
                        "version": "2.0.0",
                        "environment": "production",
                        "build_date": "2024-01-15T10:00:00Z",
                        "git_commit": "abc123def456"
                    },
                    "runtime": {
                        "python_version": "3.11.0",
                        "platform": "linux",
                        "architecture": "x86_64",
                        "uptime_seconds": 3600.5
                    },
                    "resources": {
                        "memory_usage_mb": 128.5,
                        "cpu_usage_percent": 15.2,
                        "disk_usage_percent": 45.0,
                        "open_file_descriptors": 125
                    },
                    "dependencies": {
                        "fastapi": "0.104.1",
                        "pydantic": "2.5.0",
                        "uvicorn": "0.24.0",
                        "redis": "5.0.1"
                    }
                }
            ]
        }