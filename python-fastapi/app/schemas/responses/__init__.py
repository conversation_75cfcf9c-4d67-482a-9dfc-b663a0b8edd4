"""
Response schemas package.

This package contains all Pydantic schemas for API response serialization.
"""

from .wishlist import (
    WishlistItemResponse,
    WishlistResponse,
    WishlistListResponse,
    WishlistCreatedResponse,
    WishlistUpdatedResponse,
    WishlistDeletedResponse,
    ShareHashResponse
)
from .item import (
    ItemAddedResponse,
    ItemUpdatedResponse,
    ItemRemovedResponse,
    BulkItemOperationResponse,
    ItemMoveResponse
)
from .shared import (
    SharedWishlistResponse,
    SharedWishlistStatsResponse
)
from .common import (
    HealthCheckResponse,
    MetricsResponse,
    CacheStatsResponse
)

__all__ = [
    # Wishlist responses
    'WishlistItemResponse',
    'WishlistResponse',
    'WishlistListResponse',
    'WishlistCreatedResponse',
    'WishlistUpdatedResponse',
    'WishlistDeletedResponse',
    'ShareHashResponse',
    
    # Item responses
    'ItemAddedResponse',
    'ItemUpdatedResponse',
    'ItemRemovedResponse',
    'BulkItemOperationResponse',
    'ItemMoveResponse',
    
    # Shared wishlist responses
    'SharedWishlistResponse',
    'SharedWishlistStatsResponse',
    
    # Common responses
    'HealthCheckResponse',
    'MetricsResponse',
    'CacheStatsResponse',
]