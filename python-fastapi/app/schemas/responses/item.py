"""
Wishlist item response schemas.

This module contains Pydantic schemas for serializing item-related API responses.
"""

from pydantic import Field
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..base import BaseResponseSchema, SuccessResponseSchema
from .wishlist import WishlistItemResponse, ProductDataResponse


class ItemAddedResponse(SuccessResponseSchema):
    """
    Schema for item addition success responses.
    """
    
    item: WishlistItemResponse = Field(
        description="The added item with product details"
    )
    wishlist_id: str = Field(
        description="ID of the wishlist the item was added to",
        example="wishlist_abc123"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Product added to wishlist successfully",
                    "item": {
                        "product_id": "product_abc123",
                        "notes": "Size M, Blue color preferred",
                        "quantity": 1,
                        "priority": 3,
                        "added_at": "2024-01-16T15:30:00Z",
                        "product": {
                            "product_id": "product_abc123",
                            "title": "Wireless Bluetooth Headphones",
                            "price": 99.99,
                            "currency": "AED",
                            "image_url": "https://example.com/images/product_abc123.jpg"
                        }
                    },
                    "wishlist_id": "wishlist_abc123",
                    "request_id": "req_abc123",
                    "timestamp": "2024-01-16T15:30:00Z"
                }
            ]
        }


class ItemUpdatedResponse(SuccessResponseSchema):
    """
    Schema for item update success responses.
    """
    
    item: WishlistItemResponse = Field(
        description="The updated item with product details"
    )
    wishlist_id: str = Field(
        description="ID of the wishlist containing the item",
        example="wishlist_abc123"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Wishlist item updated successfully",
                    "item": {
                        "product_id": "product_abc123",
                        "notes": "Updated: Size L, Red color preferred",
                        "quantity": 2,
                        "priority": 5,
                        "added_at": "2024-01-15T10:30:00Z",
                        "updated_at": "2024-01-16T15:45:00Z",
                        "product": {
                            "product_id": "product_abc123",
                            "title": "Wireless Bluetooth Headphones",
                            "price": 99.99,
                            "currency": "AED"
                        }
                    },
                    "wishlist_id": "wishlist_abc123",
                    "request_id": "req_def456",
                    "timestamp": "2024-01-16T15:45:00Z"
                }
            ]
        }


class ItemRemovedResponse(SuccessResponseSchema):
    """
    Schema for item removal success responses.
    """
    
    removed_product_id: str = Field(
        description="ID of the removed product",
        example="product_abc123"
    )
    wishlist_id: str = Field(
        description="ID of the wishlist the item was removed from",
        example="wishlist_abc123"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Product removed from wishlist successfully",
                    "removed_product_id": "product_abc123",
                    "wishlist_id": "wishlist_abc123",
                    "request_id": "req_ghi789",
                    "timestamp": "2024-01-16T16:00:00Z"
                }
            ]
        }


class BulkOperationResult(BaseResponseSchema):
    """
    Schema for individual operation results in bulk operations.
    """
    
    product_id: str = Field(
        description="Product ID that was processed"
    )
    success: bool = Field(
        description="Whether the operation was successful"
    )
    error: Optional[str] = Field(
        default=None,
        description="Error message if the operation failed"
    )
    item: Optional[WishlistItemResponse] = Field(
        default=None,
        description="Item data if the operation was successful"
    )


class BulkItemOperationResponse(SuccessResponseSchema):
    """
    Schema for bulk item operation responses.
    """
    
    wishlist_id: str = Field(
        description="ID of the wishlist that was modified",
        example="wishlist_abc123"
    )
    total_requested: int = Field(
        description="Total number of operations requested",
        example=5
    )
    successful: int = Field(
        description="Number of successful operations",
        example=4
    )
    failed: int = Field(
        description="Number of failed operations",
        example=1
    )
    results: List[BulkOperationResult] = Field(
        description="Detailed results for each operation"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Bulk operation completed",
                    "wishlist_id": "wishlist_abc123",
                    "total_requested": 3,
                    "successful": 2,
                    "failed": 1,
                    "results": [
                        {
                            "product_id": "product_abc123",
                            "success": True,
                            "item": {
                                "product_id": "product_abc123",
                                "notes": "Size M",
                                "quantity": 1,
                                "priority": 3,
                                "added_at": "2024-01-16T15:30:00Z"
                            }
                        },
                        {
                            "product_id": "product_xyz789",
                            "success": True,
                            "item": {
                                "product_id": "product_xyz789",
                                "notes": "Blue color",
                                "quantity": 2,
                                "priority": 1,
                                "added_at": "2024-01-16T15:30:00Z"
                            }
                        },
                        {
                            "product_id": "product_invalid",
                            "success": False,
                            "error": "Product not found"
                        }
                    ],
                    "request_id": "req_bulk123",
                    "timestamp": "2024-01-16T15:30:00Z"
                }
            ]
        }


class ItemMoveResult(BaseResponseSchema):
    """
    Schema for individual item move results.
    """
    
    product_id: str = Field(
        description="Product ID that was moved"
    )
    success: bool = Field(
        description="Whether the move was successful"
    )
    error: Optional[str] = Field(
        default=None,
        description="Error message if the move failed"
    )


class ItemMoveResponse(SuccessResponseSchema):
    """
    Schema for item move operation responses.
    """
    
    source_wishlist_id: str = Field(
        description="ID of the source wishlist",
        example="wishlist_source123"
    )
    target_wishlist_id: str = Field(
        description="ID of the target wishlist",
        example="wishlist_target456"
    )
    operation_type: str = Field(
        description="Type of operation performed",
        example="move"  # or "copy"
    )
    total_requested: int = Field(
        description="Total number of items requested to move",
        example=3
    )
    successful: int = Field(
        description="Number of items successfully moved",
        example=2
    )
    failed: int = Field(
        description="Number of items that failed to move",
        example=1
    )
    results: List[ItemMoveResult] = Field(
        description="Detailed results for each item"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Items moved successfully",
                    "source_wishlist_id": "wishlist_source123",
                    "target_wishlist_id": "wishlist_target456",
                    "operation_type": "move",
                    "total_requested": 2,
                    "successful": 2,
                    "failed": 0,
                    "results": [
                        {
                            "product_id": "product_abc123",
                            "success": True
                        },
                        {
                            "product_id": "product_xyz789",
                            "success": True
                        }
                    ],
                    "request_id": "req_move123",
                    "timestamp": "2024-01-16T16:15:00Z"
                }
            ]
        }


class ItemStatsResponse(BaseResponseSchema):
    """
    Schema for item statistics responses.
    """
    
    wishlist_id: str = Field(
        description="ID of the wishlist",
        example="wishlist_abc123"
    )
    total_items: int = Field(
        description="Total number of items in the wishlist",
        example=15
    )
    items_by_priority: Dict[str, int] = Field(
        description="Count of items by priority level",
        example={"1": 3, "2": 5, "3": 4, "4": 2, "5": 1}
    )
    items_with_notes: int = Field(
        description="Number of items that have notes",
        example=8
    )
    items_with_product_data: int = Field(
        description="Number of items with enriched product data",
        example=12
    )
    most_recent_addition: Optional[datetime] = Field(
        default=None,
        description="Timestamp of the most recently added item",
        example="2024-01-16T15:30:00Z"
    )
    oldest_item: Optional[datetime] = Field(
        default=None,
        description="Timestamp of the oldest item",
        example="2024-01-01T10:00:00Z"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "wishlist_id": "wishlist_abc123",
                    "total_items": 15,
                    "items_by_priority": {
                        "1": 3,
                        "2": 5,
                        "3": 4,
                        "4": 2,
                        "5": 1
                    },
                    "items_with_notes": 8,
                    "items_with_product_data": 12,
                    "most_recent_addition": "2024-01-16T15:30:00Z",
                    "oldest_item": "2024-01-01T10:00:00Z"
                }
            ]
        }