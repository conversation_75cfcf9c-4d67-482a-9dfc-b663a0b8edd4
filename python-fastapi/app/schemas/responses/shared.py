"""
Shared wishlist response schemas.

This module contains Pydantic schemas for serializing shared/public wishlist responses.
"""

from pydantic import Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..base import BaseResponseSchema, SuccessResponseSchema
from .wishlist import WishlistR<PERSON>ponse, WishlistItemResponse


class SharedWishlistResponse(BaseResponseSchema):
    """
    Schema for shared/public wishlist responses.
    
    Similar to WishlistResponse but excludes sensitive information
    and includes sharing-specific metadata.
    """
    
    wishlist_id: str = Field(
        description="Unique wishlist identifier",
        example="wishlist_abc123"
    )
    name: str = Field(
        description="Wishlist name",
        example="My Birthday Wishlist"
    )
    share_hash: str = Field(
        description="Hash used to access this shared wishlist",
        example="abc123def456ghi789"
    )
    owner_name: Optional[str] = Field(
        default=None,
        description="Display name of the wishlist owner (if provided)",
        example="John D."
    )
    description: Optional[str] = Field(
        default=None,
        description="Optional description of the wishlist",
        example="Items I'd love for my 30th birthday!"
    )
    item_count: int = Field(
        description="Total number of items in the wishlist",
        example=5
    )
    items: Optional[List[WishlistItemResponse]] = Field(
        default=None,
        description="List of items in the wishlist (if requested)"
    )
    created_at: datetime = Field(
        description="When the wishlist was created",
        example="2024-01-10T09:00:00Z"
    )
    last_updated: datetime = Field(
        description="When the wishlist was last updated",
        example="2024-01-16T14:20:00Z"
    )
    shared_at: Optional[datetime] = Field(
        default=None,
        description="When the wishlist was made public",
        example="2024-01-12T11:00:00Z"
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        description="When the shared link expires (if set)",
        example="2024-12-31T23:59:59Z"
    )
    view_count: Optional[int] = Field(
        default=None,
        description="Number of times this shared wishlist has been viewed",
        example=42
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "wishlist_id": "wishlist_abc123",
                    "name": "My Birthday Wishlist",
                    "share_hash": "abc123def456ghi789",
                    "owner_name": "John D.",
                    "description": "Items I'd love for my 30th birthday!",
                    "item_count": 3,
                    "items": [
                        {
                            "product_id": "product_abc123",
                            "notes": "Size M preferred",
                            "quantity": 1,
                            "priority": 5,
                            "added_at": "2024-01-15T10:30:00Z",
                            "product": {
                                "product_id": "product_abc123",
                                "title": "Wireless Bluetooth Headphones",
                                "price": 99.99,
                                "currency": "AED",
                                "image_url": "https://example.com/images/product_abc123.jpg"
                            }
                        }
                    ],
                    "created_at": "2024-01-10T09:00:00Z",
                    "last_updated": "2024-01-16T14:20:00Z",
                    "shared_at": "2024-01-12T11:00:00Z",
                    "expires_at": "2024-12-31T23:59:59Z",
                    "view_count": 42
                }
            ]
        }


class SharedWishlistViewEvent(BaseResponseSchema):
    """
    Schema for tracking shared wishlist view events.
    """
    
    share_hash: str = Field(
        description="Hash of the viewed wishlist"
    )
    visitor_id: Optional[str] = Field(
        default=None,
        description="Visitor ID (if provided)"
    )
    viewed_at: datetime = Field(
        description="When the wishlist was viewed",
        default_factory=datetime.utcnow
    )
    user_agent: Optional[str] = Field(
        default=None,
        description="User agent of the viewer"
    )
    ip_address: Optional[str] = Field(
        default=None,
        description="IP address of the viewer (anonymized)"
    )
    country: Optional[str] = Field(
        default=None,
        description="Country of the viewer"
    )
    referrer: Optional[str] = Field(
        default=None,
        description="Referrer URL"
    )


class SharedWishlistStatsResponse(BaseResponseSchema):
    """
    Schema for shared wishlist statistics responses.
    """
    
    wishlist_id: str = Field(
        description="ID of the wishlist",
        example="wishlist_abc123"
    )
    share_hash: str = Field(
        description="Share hash of the wishlist",
        example="abc123def456ghi789"
    )
    total_views: int = Field(
        description="Total number of views",
        example=156
    )
    unique_visitors: int = Field(
        description="Number of unique visitors",
        example=89
    )
    views_by_date: Dict[str, int] = Field(
        description="Views grouped by date",
        example={
            "2024-01-15": 12,
            "2024-01-16": 18,
            "2024-01-17": 25
        }
    )
    views_by_country: Dict[str, int] = Field(
        description="Views grouped by country",
        example={
            "AE": 45,
            "SA": 32,
            "US": 15,
            "UK": 8
        }
    )
    top_referrers: List[Dict[str, Any]] = Field(
        description="Top referrer sources",
        example=[
            {"url": "https://facebook.com", "count": 23},
            {"url": "https://instagram.com", "count": 18},
            {"url": "direct", "count": 45}
        ]
    )
    first_view: Optional[datetime] = Field(
        default=None,
        description="Timestamp of the first view",
        example="2024-01-12T11:30:00Z"
    )
    last_view: Optional[datetime] = Field(
        default=None,
        description="Timestamp of the most recent view",
        example="2024-01-17T16:45:00Z"
    )
    average_views_per_day: float = Field(
        description="Average views per day",
        example=12.5
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "wishlist_id": "wishlist_abc123",
                    "share_hash": "abc123def456ghi789",
                    "total_views": 156,
                    "unique_visitors": 89,
                    "views_by_date": {
                        "2024-01-15": 12,
                        "2024-01-16": 18,
                        "2024-01-17": 25
                    },
                    "views_by_country": {
                        "AE": 45,
                        "SA": 32,
                        "US": 15,
                        "UK": 8
                    },
                    "top_referrers": [
                        {"url": "https://facebook.com", "count": 23},
                        {"url": "https://instagram.com", "count": 18},
                        {"url": "direct", "count": 45}
                    ],
                    "first_view": "2024-01-12T11:30:00Z",
                    "last_view": "2024-01-17T16:45:00Z",
                    "average_views_per_day": 12.5
                }
            ]
        }


class ShareWishlistResponse(SuccessResponseSchema):
    """
    Schema for wishlist sharing success responses.
    """
    
    wishlist_id: str = Field(
        description="ID of the shared wishlist",
        example="wishlist_abc123"
    )
    share_hash: str = Field(
        description="Hash for accessing the shared wishlist",
        example="abc123def456ghi789"
    )
    share_url: str = Field(
        description="Full URL for sharing the wishlist",
        example="https://api.example.com/api/v1/wishlists/shared/abc123def456ghi789"
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        description="When the shared link expires (if set)",
        example="2024-12-31T23:59:59Z"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Wishlist shared successfully",
                    "wishlist_id": "wishlist_abc123",
                    "share_hash": "abc123def456ghi789",
                    "share_url": "https://api.example.com/api/v1/wishlists/shared/abc123def456ghi789",
                    "expires_at": "2024-12-31T23:59:59Z",
                    "request_id": "req_share123",
                    "timestamp": "2024-01-16T16:30:00Z"
                }
            ]
        }


class UnshareWishlistResponse(SuccessResponseSchema):
    """
    Schema for wishlist unsharing success responses.
    """
    
    wishlist_id: str = Field(
        description="ID of the unshared wishlist",
        example="wishlist_abc123"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Wishlist made private successfully",
                    "wishlist_id": "wishlist_abc123",
                    "request_id": "req_unshare123",
                    "timestamp": "2024-01-16T16:45:00Z"
                }
            ]
        }