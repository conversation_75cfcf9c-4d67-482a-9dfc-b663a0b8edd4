"""
Schemas package.

This package contains all Pydantic schemas for request validation and response serialization.
The schemas are organized into logical modules with inheritance hierarchies for code reuse.
"""

# Base schemas and utilities
from .base import (
    BaseSchema,
    BaseRequestSchema,
    BaseResponseSchema,
    PaginationSchema,
    PaginatedResponseSchema,
    ErrorDetailSchema,
    ErrorResponseSchema,
    SuccessResponseSchema,
    LanguageCode,
    CountryCode,
    validate_user_id,
    validate_product_id,
    validate_wishlist_name,
    validate_notes,
    validate_share_hash
)

# Request schemas
from .requests import (
    # Wishlist requests
    CreateWishlistRequest,
    UpdateWishlistRequest,
    UpdateWishlistPrivacyRequest,
    RegenerateHashRequest,
    GetWishlistsRequest,
    GetWishlistRequest,
    
    # Item requests
    AddItemRequest,
    UpdateItemRequest,
    RemoveItemRequest,
    BulkAddItemsRequest,
    BulkRemoveItemsRequest,
    
    # Shared wishlist requests
    GetSharedWishlistRequest,
)

# Response schemas
from .responses import (
    # Wishlist responses
    WishlistItemResponse,
    WishlistResponse,
    WishlistListResponse,
    WishlistCreatedResponse,
    WishlistUpdatedResponse,
    WishlistDeletedResponse,
    ShareHashResponse,
    
    # Item responses
    ItemAddedResponse,
    ItemUpdatedResponse,
    ItemRemovedResponse,
    BulkItemOperationResponse,
    ItemMoveResponse,
    
    # Shared wishlist responses
    SharedWishlistResponse,
    SharedWishlistStatsResponse,
    
    # Common responses
    HealthCheckResponse,
    MetricsResponse,
    CacheStatsResponse,
)

__all__ = [
    # Base schemas
    'BaseSchema',
    'BaseRequestSchema',
    'BaseResponseSchema',
    'PaginationSchema',
    'PaginatedResponseSchema',
    'ErrorDetailSchema',
    'ErrorResponseSchema',
    'SuccessResponseSchema',
    'LanguageCode',
    'CountryCode',
    
    # Validators
    'validate_user_id',
    'validate_product_id',
    'validate_wishlist_name',
    'validate_notes',
    'validate_share_hash',
    
    # Request schemas
    'CreateWishlistRequest',
    'UpdateWishlistRequest',
    'UpdateWishlistPrivacyRequest',
    'RegenerateHashRequest',
    'GetWishlistsRequest',
    'GetWishlistRequest',
    'AddItemRequest',
    'UpdateItemRequest',
    'RemoveItemRequest',
    'BulkAddItemsRequest',
    'BulkRemoveItemsRequest',
    'GetSharedWishlistRequest',
    
    # Response schemas
    'WishlistItemResponse',
    'WishlistResponse',
    'WishlistListResponse',
    'WishlistCreatedResponse',
    'WishlistUpdatedResponse',
    'WishlistDeletedResponse',
    'ShareHashResponse',
    'ItemAddedResponse',
    'ItemUpdatedResponse',
    'ItemRemovedResponse',
    'BulkItemOperationResponse',
    'ItemMoveResponse',
    'SharedWishlistResponse',
    'SharedWishlistStatsResponse',
    'HealthCheckResponse',
    'MetricsResponse',
    'CacheStatsResponse',
]