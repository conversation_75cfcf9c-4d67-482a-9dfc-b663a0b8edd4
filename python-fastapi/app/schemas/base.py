"""
Base schemas and common validation utilities.

This module provides base classes and common validators that are shared
across different schema types to promote code reuse and consistency.
"""

from pydantic import BaseModel, Field, field_validator, model_validator, ConfigDict
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone
from enum import Enum
import re


class LanguageCode(str, Enum):
    """Supported language codes."""
    ENGLISH = "en"
    ARABIC = "ar"


class CountryCode(str, Enum):
    """Supported country codes."""
    UAE = "ae"
    SAUDI_ARABIA = "sa"
    KUWAIT = "kw"
    BAHRAIN = "bh"
    OMAN = "om"
    QATAR = "qa"


class BaseSchema(BaseModel):
    """
    Base schema class with common configuration.

    Provides consistent configuration for all schemas including
    JSON encoding settings and validation behavior.
    """

    model_config = ConfigDict(
        # Use enum values instead of names in JSON
        use_enum_values=True,
        # Validate assignment to fields
        validate_assignment=True,
        # Allow population by field name or alias
        populate_by_name=True,
        # Generate schema with examples
        json_schema_extra={
            "examples": []
        }
    )


class BaseRequestSchema(BaseSchema):
    """
    Base class for all request schemas.

    Provides common validation and configuration for API requests.
    """

    # Common query parameters
    country: Optional[CountryCode] = Field(
        default=CountryCode.UAE,
        description="Country code for localization"
    )
    language: Optional[LanguageCode] = Field(
        default=LanguageCode.ENGLISH,
        description="Language code for localization"
    )

    @field_validator('country', mode='before')
    @classmethod
    def validate_country(cls, v):
        """Validate and normalize country code."""
        if v is None:
            return CountryCode.UAE

        if isinstance(v, str):
            v = v.lower().strip()
            # Map common variations
            country_mapping = {
                'uae': 'ae',
                'united_arab_emirates': 'ae',
                'saudi': 'sa',
                'ksa': 'sa',
                'saudi_arabia': 'sa',
            }
            v = country_mapping.get(v, v)

        try:
            return CountryCode(v)
        except ValueError:
            # Default to UAE for invalid codes
            return CountryCode.UAE

    @field_validator('language', mode='before')
    @classmethod
    def validate_language(cls, v):
        """Validate and normalize language code."""
        if v is None:
            return LanguageCode.ENGLISH

        if isinstance(v, str):
            v = v.lower().strip()
            # Map common variations
            language_mapping = {
                'eng': 'en',
                'english': 'en',
                'ara': 'ar',
                'arabic': 'ar',
            }
            v = language_mapping.get(v, v)

        try:
            return LanguageCode(v)
        except ValueError:
            # Default to English for invalid codes
            return LanguageCode.ENGLISH


class BaseResponseSchema(BaseSchema):
    """
    Base class for all response schemas.

    Provides common fields and configuration for API responses.
    """
    pass


class PaginationSchema(BaseSchema):
    """Schema for pagination parameters."""

    page: int = Field(
        default=1,
        ge=1,
        description="Page number (1-based)"
    )
    page_size: int = Field(
        default=20,
        ge=1,
        le=100,
        description="Number of items per page (max 100)"
    )

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size


class PaginatedResponseSchema(BaseResponseSchema):
    """Base schema for paginated responses."""

    total: int = Field(
        description="Total number of items"
    )
    page: int = Field(
        description="Current page number"
    )
    page_size: int = Field(
        description="Number of items per page"
    )
    total_pages: int = Field(
        description="Total number of pages"
    )
    has_next: bool = Field(
        description="Whether there are more pages"
    )
    has_previous: bool = Field(
        description="Whether there are previous pages"
    )


class ErrorDetailSchema(BaseSchema):
    """Schema for detailed error information."""

    code: str = Field(
        description="Error code for programmatic handling"
    )
    message: str = Field(
        description="Human-readable error message"
    )
    field: Optional[str] = Field(
        default=None,
        description="Field name if this is a validation error"
    )
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional error details"
    )


class ErrorResponseSchema(BaseResponseSchema):
    """Schema for error responses."""

    code: str = Field(
        description="Error code for programmatic handling"
    )
    message: str = Field(
        description="Human-readable error message"
    )
    correlation_id: Optional[str] = Field(
        default=None,
        description="Correlation ID for request tracing"
    )
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Additional error details"
    )
    field_errors: Optional[List[Dict[str, Any]]] = Field(
        default=None,
        description="Field-specific validation errors"
    )
    timestamp: str = Field(
        default_factory=lambda: datetime.now(timezone.utc).isoformat(),
        description="Error timestamp"
    )


class SuccessResponseSchema(BaseResponseSchema):
    """Schema for success responses with message."""

    message: str = Field(
        description="Success message"
    )
    correlation_id: Optional[str] = Field(
        default=None,
        description="Correlation ID for request tracing"
    )
    timestamp: datetime = Field(
        default_factory=datetime.now(timezone.utc),
        description="Response timestamp"
    )


# Custom validators
def validate_user_id(v: str) -> str:
    """Validate user ID format."""
    if not v or not v.strip():
        raise ValueError("user_id cannot be empty")

    v = v.strip()

    # Basic format validation (adjust based on your user ID format)
    if len(v) < 3:
        raise ValueError("user_id must be at least 3 characters long")

    if len(v) > 50:
        raise ValueError("user_id cannot exceed 50 characters")

    # Allow alphanumeric, hyphens, and underscores
    if not re.match(r'^[a-zA-Z0-9_-]+$', v):
        raise ValueError("user_id contains invalid characters")

    return v


def validate_product_id(v: str) -> str:
    """Validate product ID format."""
    if not v or not v.strip():
        raise ValueError("product_id cannot be empty")

    v = v.strip()

    # Basic format validation
    if len(v) < 1:
        raise ValueError("product_id cannot be empty")

    if len(v) > 100:
        raise ValueError("product_id cannot exceed 100 characters")

    return v


def validate_wishlist_name(v: str) -> str:
    """Validate wishlist name."""
    if not v or not v.strip():
        raise ValueError("name cannot be empty")

    v = v.strip()

    if len(v) > 255:
        raise ValueError("name cannot exceed 255 characters")

    # Check for potentially harmful content
    if any(char in v for char in ['<', '>', '&', '"', "'"]):
        raise ValueError("name contains invalid characters")

    return v


def validate_notes(v: Optional[str]) -> Optional[str]:
    """Validate item notes."""
    if v is None:
        return None

    v = v.strip()

    if not v:
        return None

    if len(v) > 500:
        raise ValueError("notes cannot exceed 500 characters")

    return v


def validate_share_hash(v: str) -> str:
    """Validate share hash format."""
    if not v or not v.strip():
        raise ValueError("share_hash cannot be empty")

    v = v.strip()

    if len(v) < 16:
        raise ValueError("share_hash must be at least 16 characters long")

    if len(v) > 64:
        raise ValueError("share_hash cannot exceed 64 characters")

    # Should be alphanumeric
    if not re.match(r'^[a-zA-Z0-9]+$', v):
        raise ValueError("share_hash contains invalid characters")

    return v
