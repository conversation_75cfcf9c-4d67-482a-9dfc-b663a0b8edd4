"""
Unit tests for domain models.

Tests the business logic, validation, and methods of the Wishlist and WishlistItem
domain models.
"""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.models.domain.factories import WishlistFactory, WishlistItemFactory


class TestWishlistItem:
    """Test cases for WishlistItem domain model."""
    
    def test_create_wishlist_item_with_defaults(self):
        """Test creating a wishlist item with default values."""
        item = WishlistItem(product_id="PROD001")
        
        assert item.product_id == "PROD001"
        assert item.notes is None
        assert item.added_at is not None
        assert isinstance(item.added_at, datetime)
        assert item.product is None
    
    def test_create_wishlist_item_with_all_fields(self):
        """Test creating a wishlist item with all fields specified."""
        added_at = datetime.utcnow()
        product_data = {"title": "Test Product", "price": 99.99}
        
        item = WishlistItem(
            product_id="PROD001",
            notes="Test notes",
            added_at=added_at,
            product=product_data
        )
        
        assert item.product_id == "PROD001"
        assert item.notes == "Test notes"
        assert item.added_at == added_at
        assert item.product == product_data
    
    def test_empty_product_id_raises_error(self):
        """Test that empty product_id raises ValueError."""
        with pytest.raises(ValueError, match="product_id cannot be empty"):
            WishlistItem(product_id="")
        
        with pytest.raises(ValueError, match="product_id cannot be empty"):
            WishlistItem(product_id="   ")
    
    def test_notes_too_long_raises_error(self):
        """Test that notes longer than 500 characters raise ValueError."""
        long_notes = "x" * 501
        
        with pytest.raises(ValueError, match="notes cannot exceed 500 characters"):
            WishlistItem(product_id="PROD001", notes=long_notes)
    
    def test_update_notes(self):
        """Test updating notes on an existing item."""
        item = WishlistItem(product_id="PROD001")
        
        item.update_notes("New notes")
        assert item.notes == "New notes"
        
        item.update_notes(None)
        assert item.notes is None
    
    def test_update_notes_too_long_raises_error(self):
        """Test that updating with notes too long raises ValueError."""
        item = WishlistItem(product_id="PROD001")
        long_notes = "x" * 501
        
        with pytest.raises(ValueError, match="notes cannot exceed 500 characters"):
            item.update_notes(long_notes)
    
    def test_has_product_data(self):
        """Test checking if item has enriched product data."""
        item = WishlistItem(product_id="PROD001")
        assert not item.has_product_data()
        
        item.product = {"title": "Test Product"}
        assert item.has_product_data()
        
        item.product = {}
        assert not item.has_product_data()
    
    def test_get_product_title(self):
        """Test getting product title from enriched data."""
        item = WishlistItem(product_id="PROD001")
        assert item.get_product_title() is None
        
        item.product = {"title": "Test Product"}
        assert item.get_product_title() == "Test Product"
        
        item.product = {"name": "Test Name"}
        assert item.get_product_title() == "Test Name"
        
        item.product = {"title": "Test Title", "name": "Test Name"}
        assert item.get_product_title() == "Test Title"  # title takes precedence


class TestWishlist:
    """Test cases for Wishlist domain model."""
    
    def test_create_wishlist_with_required_fields(self):
        """Test creating a wishlist with all required fields."""
        now = datetime.utcnow()
        
        wishlist = Wishlist(
            user_id="user123",
            wishlist_id="wish123",
            name="My Wishlist",
            is_default=True,
            is_public=False,
            share_hash="abcd1234567890abcd1234567890abcd",
            items=[],
            created_at=now,
            updated_at=now
        )
        
        assert wishlist.user_id == "user123"
        assert wishlist.wishlist_id == "wish123"
        assert wishlist.name == "My Wishlist"
        assert wishlist.is_default is True
        assert wishlist.is_public is False
        assert wishlist.share_hash == "abcd1234567890abcd1234567890abcd"
        assert wishlist.items == []
        assert wishlist.created_at == now
        assert wishlist.updated_at == now
    
    def test_empty_user_id_raises_error(self):
        """Test that empty user_id raises ValueError."""
        now = datetime.utcnow()
        
        with pytest.raises(ValueError, match="user_id cannot be empty"):
            Wishlist(
                user_id="",
                wishlist_id="wish123",
                name="My Wishlist",
                is_default=False,
                is_public=False,
                share_hash="abcd1234567890abcd1234567890abcd",
                items=[],
                created_at=now,
                updated_at=now
            )
    
    def test_empty_name_raises_error(self):
        """Test that empty name raises ValueError."""
        now = datetime.utcnow()
        
        with pytest.raises(ValueError, match="name cannot be empty"):
            Wishlist(
                user_id="user123",
                wishlist_id="wish123",
                name="",
                is_default=False,
                is_public=False,
                share_hash="abcd1234567890abcd1234567890abcd",
                items=[],
                created_at=now,
                updated_at=now
            )
    
    def test_name_too_long_raises_error(self):
        """Test that name longer than 255 characters raises ValueError."""
        now = datetime.utcnow()
        long_name = "x" * 256
        
        with pytest.raises(ValueError, match="name cannot exceed 255 characters"):
            Wishlist(
                user_id="user123",
                wishlist_id="wish123",
                name=long_name,
                is_default=False,
                is_public=False,
                share_hash="abcd1234567890abcd1234567890abcd",
                items=[],
                created_at=now,
                updated_at=now
            )
    
    def test_short_share_hash_raises_error(self):
        """Test that share_hash shorter than 16 characters raises ValueError."""
        now = datetime.utcnow()
        
        with pytest.raises(ValueError, match="share_hash must be at least 16 characters"):
            Wishlist(
                user_id="user123",
                wishlist_id="wish123",
                name="My Wishlist",
                is_default=False,
                is_public=False,
                share_hash="short",
                items=[],
                created_at=now,
                updated_at=now
            )
    
    def test_add_item(self):
        """Test adding an item to the wishlist."""
        wishlist = WishlistFactory.create()
        original_updated_at = wishlist.updated_at
        
        item = wishlist.add_item("PROD001", "Test notes")
        
        assert len(wishlist.items) == 1
        assert wishlist.items[0].product_id == "PROD001"
        assert wishlist.items[0].notes == "Test notes"
        assert wishlist.updated_at > original_updated_at
        assert isinstance(item, WishlistItem)
    
    def test_add_item_replaces_existing(self):
        """Test that adding an existing item replaces it."""
        wishlist = WishlistFactory.create()
        
        # Add first item
        wishlist.add_item("PROD001", "First notes")
        assert len(wishlist.items) == 1
        assert wishlist.items[0].notes == "First notes"
        
        # Add same product again with different notes
        wishlist.add_item("PROD001", "Second notes")
        assert len(wishlist.items) == 1
        assert wishlist.items[0].notes == "Second notes"
    
    def test_add_item_empty_product_id_raises_error(self):
        """Test that adding item with empty product_id raises ValueError."""
        wishlist = WishlistFactory.create()
        
        with pytest.raises(ValueError, match="product_id cannot be empty"):
            wishlist.add_item("")
    
    def test_remove_item(self):
        """Test removing an item from the wishlist."""
        wishlist = WishlistFactory.create()
        wishlist.add_item("PROD001")
        wishlist.add_item("PROD002")
        original_updated_at = wishlist.updated_at
        
        result = wishlist.remove_item("PROD001")
        
        assert result is True
        assert len(wishlist.items) == 1
        assert wishlist.items[0].product_id == "PROD002"
        assert wishlist.updated_at > original_updated_at
    
    def test_remove_nonexistent_item(self):
        """Test removing an item that doesn't exist."""
        wishlist = WishlistFactory.create()
        wishlist.add_item("PROD001")
        original_updated_at = wishlist.updated_at
        
        result = wishlist.remove_item("PROD999")
        
        assert result is False
        assert len(wishlist.items) == 1
        assert wishlist.updated_at == original_updated_at
    
    def test_has_item(self):
        """Test checking if wishlist contains a specific item."""
        wishlist = WishlistFactory.create()
        
        assert not wishlist.has_item("PROD001")
        
        wishlist.add_item("PROD001")
        assert wishlist.has_item("PROD001")
        assert not wishlist.has_item("PROD002")
    
    def test_get_item(self):
        """Test getting a specific item from the wishlist."""
        wishlist = WishlistFactory.create()
        
        assert wishlist.get_item("PROD001") is None
        
        wishlist.add_item("PROD001", "Test notes")
        item = wishlist.get_item("PROD001")
        
        assert item is not None
        assert item.product_id == "PROD001"
        assert item.notes == "Test notes"
    
    def test_update_item_notes(self):
        """Test updating notes for a specific item."""
        wishlist = WishlistFactory.create()
        wishlist.add_item("PROD001", "Original notes")
        original_updated_at = wishlist.updated_at
        
        result = wishlist.update_item_notes("PROD001", "Updated notes")
        
        assert result is True
        assert wishlist.get_item("PROD001").notes == "Updated notes"
        assert wishlist.updated_at > original_updated_at
    
    def test_update_nonexistent_item_notes(self):
        """Test updating notes for an item that doesn't exist."""
        wishlist = WishlistFactory.create()
        original_updated_at = wishlist.updated_at
        
        result = wishlist.update_item_notes("PROD999", "New notes")
        
        assert result is False
        assert wishlist.updated_at == original_updated_at
    
    def test_clear_items(self):
        """Test clearing all items from the wishlist."""
        wishlist = WishlistFactory.create()
        wishlist.add_item("PROD001")
        wishlist.add_item("PROD002")
        original_updated_at = wishlist.updated_at
        
        count = wishlist.clear_items()
        
        assert count == 2
        assert len(wishlist.items) == 0
        assert wishlist.updated_at > original_updated_at
    
    def test_clear_empty_wishlist(self):
        """Test clearing items from an empty wishlist."""
        wishlist = WishlistFactory.create()
        original_updated_at = wishlist.updated_at
        
        count = wishlist.clear_items()
        
        assert count == 0
        assert len(wishlist.items) == 0
        assert wishlist.updated_at == original_updated_at
    
    def test_get_item_count(self):
        """Test getting the total number of items."""
        wishlist = WishlistFactory.create()
        
        assert wishlist.get_item_count() == 0
        
        wishlist.add_item("PROD001")
        assert wishlist.get_item_count() == 1
        
        wishlist.add_item("PROD002")
        assert wishlist.get_item_count() == 2
    
    def test_update_privacy(self):
        """Test updating the privacy status."""
        wishlist = WishlistFactory.create(is_public=False)
        original_updated_at = wishlist.updated_at
        
        wishlist.update_privacy(True)
        
        assert wishlist.is_public is True
        assert wishlist.updated_at > original_updated_at
    
    def test_update_privacy_no_change(self):
        """Test updating privacy to the same value doesn't change updated_at."""
        wishlist = WishlistFactory.create(is_public=False)
        original_updated_at = wishlist.updated_at
        
        wishlist.update_privacy(False)
        
        assert wishlist.is_public is False
        assert wishlist.updated_at == original_updated_at
    
    def test_regenerate_share_hash(self):
        """Test regenerating the share hash."""
        wishlist = WishlistFactory.create()
        original_hash = wishlist.share_hash
        original_updated_at = wishlist.updated_at
        
        new_hash = wishlist.regenerate_share_hash()
        
        assert new_hash != original_hash
        assert wishlist.share_hash == new_hash
        assert len(wishlist.share_hash) == 32
        assert wishlist.updated_at > original_updated_at
    
    def test_update_name(self):
        """Test updating the wishlist name."""
        wishlist = WishlistFactory.create(name="Old Name")
        original_updated_at = wishlist.updated_at
        
        wishlist.update_name("New Name")
        
        assert wishlist.name == "New Name"
        assert wishlist.updated_at > original_updated_at
    
    def test_update_name_strips_whitespace(self):
        """Test that updating name strips whitespace."""
        wishlist = WishlistFactory.create()
        
        wishlist.update_name("  New Name  ")
        
        assert wishlist.name == "New Name"
    
    def test_update_name_empty_raises_error(self):
        """Test that updating to empty name raises ValueError."""
        wishlist = WishlistFactory.create()
        
        with pytest.raises(ValueError, match="name cannot be empty"):
            wishlist.update_name("")
    
    def test_set_as_default(self):
        """Test setting wishlist as default."""
        wishlist = WishlistFactory.create(is_default=False)
        original_updated_at = wishlist.updated_at
        
        wishlist.set_as_default()
        
        assert wishlist.is_default is True
        assert wishlist.updated_at > original_updated_at
    
    def test_unset_as_default(self):
        """Test unsetting wishlist as default."""
        wishlist = WishlistFactory.create(is_default=True)
        original_updated_at = wishlist.updated_at
        
        wishlist.unset_as_default()
        
        assert wishlist.is_default is False
        assert wishlist.updated_at > original_updated_at
    
    def test_get_product_ids(self):
        """Test getting list of all product IDs."""
        wishlist = WishlistFactory.create()
        
        assert wishlist.get_product_ids() == []
        
        wishlist.add_item("PROD001")
        wishlist.add_item("PROD002")
        
        product_ids = wishlist.get_product_ids()
        assert len(product_ids) == 2
        assert "PROD001" in product_ids
        assert "PROD002" in product_ids
    
    def test_is_empty(self):
        """Test checking if wishlist is empty."""
        wishlist = WishlistFactory.create()
        
        assert wishlist.is_empty() is True
        
        wishlist.add_item("PROD001")
        assert wishlist.is_empty() is False
    
    def test_get_items_added_since(self):
        """Test getting items added after a specific datetime."""
        wishlist = WishlistFactory.create()
        
        # Add item in the past
        past_time = datetime.utcnow() - timedelta(hours=2)
        old_item = WishlistItem(product_id="PROD001", added_at=past_time)
        wishlist.items.append(old_item)
        
        # Add recent item
        recent_time = datetime.utcnow() - timedelta(minutes=30)
        new_item = WishlistItem(product_id="PROD002", added_at=recent_time)
        wishlist.items.append(new_item)
        
        # Get items added in the last hour
        since = datetime.utcnow() - timedelta(hours=1)
        recent_items = wishlist.get_items_added_since(since)
        
        assert len(recent_items) == 1
        assert recent_items[0].product_id == "PROD002"
    
    def test_create_new_factory_method(self):
        """Test the create_new factory method."""
        wishlist = Wishlist.create_new(
            user_id="user123",
            name="Test Wishlist",
            is_default=True,
            is_public=False
        )
        
        assert wishlist.user_id == "user123"
        assert wishlist.name == "Test Wishlist"
        assert wishlist.is_default is True
        assert wishlist.is_public is False
        assert len(wishlist.wishlist_id) > 0
        assert len(wishlist.share_hash) == 32
        assert wishlist.items == []
        assert isinstance(wishlist.created_at, datetime)
        assert isinstance(wishlist.updated_at, datetime)


class TestFactories:
    """Test cases for model factories."""
    
    def test_wishlist_item_factory_create(self):
        """Test WishlistItemFactory.create()."""
        item = WishlistItemFactory.create()
        
        assert item.product_id is not None
        assert isinstance(item.added_at, datetime)
    
    def test_wishlist_item_factory_create_with_params(self):
        """Test WishlistItemFactory.create() with specific parameters."""
        added_at = datetime.utcnow()
        
        item = WishlistItemFactory.create(
            product_id="CUSTOM001",
            notes="Custom notes",
            added_at=added_at,
            with_product_data=True
        )
        
        assert item.product_id == "CUSTOM001"
        assert item.notes == "Custom notes"
        assert item.added_at == added_at
    
    def test_wishlist_item_factory_create_batch(self):
        """Test WishlistItemFactory.create_batch()."""
        items = WishlistItemFactory.create_batch(count=5, unique_products=True)
        
        assert len(items) == 5
        product_ids = [item.product_id for item in items]
        assert len(set(product_ids)) == 5  # All unique
    
    def test_wishlist_factory_create(self):
        """Test WishlistFactory.create()."""
        wishlist = WishlistFactory.create()
        
        assert wishlist.user_id is not None
        assert wishlist.name is not None
        assert len(wishlist.wishlist_id) > 0
        assert len(wishlist.share_hash) == 32
        assert isinstance(wishlist.created_at, datetime)
        assert isinstance(wishlist.updated_at, datetime)
    
    def test_wishlist_factory_create_with_items(self):
        """Test WishlistFactory.create() with items."""
        wishlist = WishlistFactory.create(item_count=3)
        
        assert len(wishlist.items) == 3
        assert all(isinstance(item, WishlistItem) for item in wishlist.items)
    
    def test_wishlist_factory_create_default_wishlist(self):
        """Test WishlistFactory.create_default_wishlist()."""
        wishlist = WishlistFactory.create_default_wishlist("user123")
        
        assert wishlist.user_id == "user123"
        assert wishlist.is_default is True
        assert wishlist.is_public is False
        assert len(wishlist.items) == 3
    
    def test_wishlist_factory_create_public_wishlist(self):
        """Test WishlistFactory.create_public_wishlist()."""
        wishlist = WishlistFactory.create_public_wishlist("user123")
        
        assert wishlist.user_id == "user123"
        assert wishlist.is_public is True
        assert len(wishlist.items) == 10
    
    def test_wishlist_factory_create_user_wishlists(self):
        """Test WishlistFactory.create_user_wishlists()."""
        wishlists = WishlistFactory.create_user_wishlists("user123", count=3)
        
        assert len(wishlists) == 3
        assert all(w.user_id == "user123" for w in wishlists)
        assert sum(1 for w in wishlists if w.is_default) == 1  # Exactly one default