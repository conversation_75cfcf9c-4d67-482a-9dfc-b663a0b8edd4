"""
Tests for database model representations.
"""

import pytest
from datetime import datetime
from typing import Dict, Any

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.models.database.dynamodb import (
    DynamoDBWishlist,
    DynamoDBWishlistItem,
    DynamoDBWishlistMapper
)
from app.models.database.base import (
    DynamoDBTypeConverter,
    DataTransformationPipeline,
    SerializationError,
    DeserializationError
)


class TestDynamoDBTypeConverter:
    """Test DynamoDB type converter utilities."""
    
    def test_to_dynamodb_type_basic_types(self):
        """Test converting basic Python types to DynamoDB format."""
        converter = DynamoDBTypeConverter()
        
        # String
        assert converter.to_dynamodb_type("hello") == {'S': 'hello'}
        
        # Number
        assert converter.to_dynamodb_type(42) == {'N': '42'}
        assert converter.to_dynamodb_type(3.14) == {'N': '3.14'}
        
        # Boolean
        assert converter.to_dynamodb_type(True) == {'BOOL': True}
        assert converter.to_dynamodb_type(False) == {'BOOL': False}
        
        # None
        assert converter.to_dynamodb_type(None) == {'NULL': True}
    
    def test_to_dynamodb_type_collections(self):
        """Test converting collections to DynamoDB format."""
        converter = DynamoDBTypeConverter()
        
        # List of strings (string set)
        assert converter.to_dynamodb_type(['a', 'b', 'c']) == {'SS': ['a', 'b', 'c']}
        
        # List of numbers (number set)
        assert converter.to_dynamodb_type([1, 2, 3]) == {'NS': ['1', '2', '3']}
        
        # Mixed list (list)
        result = converter.to_dynamodb_type(['a', 1, True])
        assert result['L'][0] == {'S': 'a'}
        assert result['L'][1] == {'N': '1'}
        assert result['L'][2] == {'BOOL': True}
        
        # Dictionary (map)
        result = converter.to_dynamodb_type({'key': 'value', 'num': 42})
        assert result['M']['key'] == {'S': 'value'}
        assert result['M']['num'] == {'N': '42'}
    
    def test_to_dynamodb_type_datetime(self):
        """Test converting datetime to DynamoDB format."""
        converter = DynamoDBTypeConverter()
        
        dt = datetime(2024, 1, 15, 10, 30, 0)
        result = converter.to_dynamodb_type(dt)
        
        assert result == {'S': '2024-01-15T10:30:00'}
    
    def test_from_dynamodb_type_basic_types(self):
        """Test converting DynamoDB types back to Python values."""
        converter = DynamoDBTypeConverter()
        
        # String
        assert converter.from_dynamodb_type({'S': 'hello'}) == 'hello'
        
        # Number
        assert converter.from_dynamodb_type({'N': '42'}) == 42
        assert converter.from_dynamodb_type({'N': '3.14'}) == 3.14
        
        # Boolean
        assert converter.from_dynamodb_type({'BOOL': True}) is True
        assert converter.from_dynamodb_type({'BOOL': False}) is False
        
        # None
        assert converter.from_dynamodb_type({'NULL': True}) is None
    
    def test_from_dynamodb_type_collections(self):
        """Test converting DynamoDB collections back to Python values."""
        converter = DynamoDBTypeConverter()
        
        # String set
        assert converter.from_dynamodb_type({'SS': ['a', 'b', 'c']}) == ['a', 'b', 'c']
        
        # Number set
        assert converter.from_dynamodb_type({'NS': ['1', '2', '3']}) == [1, 2, 3]
        
        # List
        result = converter.from_dynamodb_type({
            'L': [{'S': 'a'}, {'N': '1'}, {'BOOL': True}]
        })
        assert result == ['a', 1, True]
        
        # Map
        result = converter.from_dynamodb_type({
            'M': {'key': {'S': 'value'}, 'num': {'N': '42'}}
        })
        assert result == {'key': 'value', 'num': 42}
    
    def test_serialize_deserialize_item(self):
        """Test serializing and deserializing complete items."""
        converter = DynamoDBTypeConverter()
        
        original_item = {
            'user_id': 'user_123',
            'name': 'Test Wishlist',
            'is_default': True,
            'item_count': 5,
            'created_at': datetime(2024, 1, 15, 10, 30, 0),
            'tags': ['electronics', 'gifts'],
            'metadata': {'source': 'web', 'version': 1}
        }
        
        # Serialize
        serialized = converter.serialize_item(original_item)
        
        # Deserialize
        deserialized = converter.deserialize_item(serialized)
        
        # Compare (datetime will be string after round-trip)
        assert deserialized['user_id'] == 'user_123'
        assert deserialized['name'] == 'Test Wishlist'
        assert deserialized['is_default'] is True
        assert deserialized['item_count'] == 5
        assert deserialized['created_at'] == '2024-01-15T10:30:00'
        assert deserialized['tags'] == ['electronics', 'gifts']
        assert deserialized['metadata'] == {'source': 'web', 'version': 1}


class TestDynamoDBWishlistItem:
    """Test DynamoDB wishlist item model."""
    
    def test_create_from_dict(self):
        """Test creating DynamoDB wishlist item from dictionary."""
        data = {
            'product_id': 'product_123',
            'notes': 'Test notes',
            'added_at': '2024-01-15T10:30:00',
            'quantity': 2,
            'priority': 3
        }
        
        item = DynamoDBWishlistItem.from_dict(data)
        
        assert item.product_id == 'product_123'
        assert item.notes == 'Test notes'
        assert item.quantity == 2
        assert item.priority == 3
        assert isinstance(item.added_at, datetime)
    
    def test_to_dict(self):
        """Test converting DynamoDB wishlist item to dictionary."""
        item = DynamoDBWishlistItem(
            product_id='product_123',
            notes='Test notes',
            quantity=2,
            priority=3
        )
        
        data = item.to_dict()
        
        assert data['product_id'] == 'product_123'
        assert data['notes'] == 'Test notes'
        assert data['quantity'] == 2
        assert data['priority'] == 3
        assert 'added_at' in data
        assert 'updated_at' in data
    
    def test_to_dynamodb_format(self):
        """Test converting to DynamoDB attribute format."""
        item = DynamoDBWishlistItem(
            product_id='product_123',
            notes='Test notes'
        )
        
        dynamodb_data = item.to_dynamodb_format()
        
        assert dynamodb_data['product_id'] == {'S': 'product_123'}
        assert dynamodb_data['notes'] == {'S': 'Test notes'}
        assert 'added_at' in dynamodb_data
        assert 'quantity' in dynamodb_data
        assert 'priority' in dynamodb_data
    
    def test_from_dynamodb_format(self):
        """Test creating from DynamoDB attribute format."""
        dynamodb_data = {
            'product_id': {'S': 'product_123'},
            'notes': {'S': 'Test notes'},
            'added_at': {'S': '2024-01-15T10:30:00'},
            'quantity': {'N': '2'},
            'priority': {'N': '3'}
        }
        
        item = DynamoDBWishlistItem.from_dynamodb_format(dynamodb_data)
        
        assert item.product_id == 'product_123'
        assert item.notes == 'Test notes'
        assert item.quantity == 2
        assert item.priority == 3
    
    def test_to_domain_model(self):
        """Test converting to domain model."""
        db_item = DynamoDBWishlistItem(
            product_id='product_123',
            notes='Test notes'
        )
        
        domain_item = db_item.to_domain_model()
        
        assert isinstance(domain_item, WishlistItem)
        assert domain_item.product_id == 'product_123'
        assert domain_item.notes == 'Test notes'
        assert domain_item.added_at is not None
    
    def test_from_domain_model(self):
        """Test creating from domain model."""
        domain_item = WishlistItem(
            product_id='product_123',
            notes='Test notes'
        )
        
        db_item = DynamoDBWishlistItem.from_domain_model(domain_item)
        
        assert db_item.product_id == 'product_123'
        assert db_item.notes == 'Test notes'
        assert db_item.quantity == 1  # Default value
        assert db_item.priority == 1  # Default value
    
    def test_validation_errors(self):
        """Test validation errors."""
        # Missing product_id
        with pytest.raises(DeserializationError) as exc_info:
            DynamoDBWishlistItem(product_id="")
        assert "product_id is required" in str(exc_info.value)
        
        # Notes too long
        with pytest.raises(DeserializationError):
            DynamoDBWishlistItem(
                product_id="product_123",
                notes="x" * 501  # Exceeds 500 character limit
            )
        
        # Invalid quantity
        with pytest.raises(DeserializationError):
            DynamoDBWishlistItem(
                product_id="product_123",
                quantity=0
            )
        
        # Invalid priority
        with pytest.raises(DeserializationError):
            DynamoDBWishlistItem(
                product_id="product_123",
                priority=6  # Must be 1-5
            )


class TestDynamoDBWishlist:
    """Test DynamoDB wishlist model."""
    
    def test_create_from_dict(self):
        """Test creating DynamoDB wishlist from dictionary."""
        data = {
            'user_id': 'user_123',
            'wishlist_id': 'wishlist_456',
            'name': 'Test Wishlist',
            'share_hash': 'abcd1234efgh5678ijkl9012mnop3456',
            'is_public': False,
            'is_default': True,
            'items': [
                {
                    'product_id': 'product_1',
                    'notes': 'Test notes'
                }
            ],
            'created_at': '2024-01-15T10:30:00',
            'updated_at': '2024-01-15T11:30:00'
        }
        
        wishlist = DynamoDBWishlist.from_dict(data)
        
        assert wishlist.user_id == 'user_123'
        assert wishlist.wishlist_id == 'wishlist_456'
        assert wishlist.name == 'Test Wishlist'
        assert wishlist.is_default is True
        assert len(wishlist.items) == 1
        assert wishlist.item_count == 1
    
    def test_to_dict(self):
        """Test converting DynamoDB wishlist to dictionary."""
        wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        data = wishlist.to_dict()
        
        assert data['user_id'] == 'user_123'
        assert data['wishlist_id'] == 'wishlist_456'
        assert data['name'] == 'Test Wishlist'
        assert data['item_count'] == 0
        assert 'created_at' in data
        assert 'updated_at' in data
    
    def test_to_dynamodb_format(self):
        """Test converting to DynamoDB item format."""
        wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        dynamodb_data = wishlist.to_dynamodb_format()
        
        assert dynamodb_data['user_id'] == {'S': 'user_123'}
        assert dynamodb_data['wishlist_id'] == {'S': 'wishlist_456'}
        assert dynamodb_data['name'] == {'S': 'Test Wishlist'}
        assert dynamodb_data['is_public'] == {'BOOL': False}
        assert dynamodb_data['items'] == {'L': []}
    
    def test_from_dynamodb_format(self):
        """Test creating from DynamoDB item format."""
        dynamodb_data = {
            'user_id': {'S': 'user_123'},
            'wishlist_id': {'S': 'wishlist_456'},
            'name': {'S': 'Test Wishlist'},
            'share_hash': {'S': 'abcd1234efgh5678ijkl9012mnop3456'},
            'is_public': {'BOOL': False},
            'is_default': {'BOOL': True},
            'items': {'L': []},
            'created_at': {'S': '2024-01-15T10:30:00'},
            'updated_at': {'S': '2024-01-15T11:30:00'},
            'item_count': {'N': '0'},
            'version': {'N': '1'}
        }
        
        wishlist = DynamoDBWishlist.from_dynamodb_format(dynamodb_data)
        
        assert wishlist.user_id == 'user_123'
        assert wishlist.wishlist_id == 'wishlist_456'
        assert wishlist.name == 'Test Wishlist'
        assert wishlist.is_default is True
        assert wishlist.item_count == 0
    
    def test_to_domain_model(self):
        """Test converting to domain model."""
        db_wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        domain_wishlist = db_wishlist.to_domain_model()
        
        assert isinstance(domain_wishlist, Wishlist)
        assert domain_wishlist.user_id == 'user_123'
        assert domain_wishlist.wishlist_id == 'wishlist_456'
        assert domain_wishlist.name == 'Test Wishlist'
        assert domain_wishlist.items == []
    
    def test_from_domain_model(self):
        """Test creating from domain model."""
        domain_wishlist = Wishlist.create_new(
            user_id='user_123',
            name='Test Wishlist'
        )
        domain_wishlist.add_item('product_1', 'Test notes')
        
        db_wishlist = DynamoDBWishlist.from_domain_model(domain_wishlist)
        
        assert db_wishlist.user_id == 'user_123'
        assert db_wishlist.name == 'Test Wishlist'
        assert len(db_wishlist.items) == 1
        assert db_wishlist.item_count == 1
        assert db_wishlist.version == 1
    
    def test_get_composite_key(self):
        """Test getting DynamoDB composite key."""
        wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        key = wishlist.get_composite_key()
        
        assert key == {
            'user_id': {'S': 'user_123'},
            'wishlist_id': {'S': 'wishlist_456'}
        }
    
    def test_get_gsi_key(self):
        """Test getting GSI key."""
        wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        key = wishlist.get_gsi_key('share_hash-index')
        
        assert key == {
            'share_hash': {'S': 'abcd1234efgh5678ijkl9012mnop3456'}
        }
        
        # Test unknown index
        with pytest.raises(ValueError):
            wishlist.get_gsi_key('unknown-index')
    
    def test_add_remove_items(self):
        """Test adding and removing items."""
        wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        # Add item
        item = DynamoDBWishlistItem(product_id='product_1', notes='Test notes')
        wishlist.add_item(item)
        
        assert len(wishlist.items) == 1
        assert wishlist.item_count == 1
        assert wishlist.version == 2  # Incremented
        
        # Remove item
        removed = wishlist.remove_item('product_1')
        
        assert removed is True
        assert len(wishlist.items) == 0
        assert wishlist.item_count == 0
        assert wishlist.version == 3  # Incremented again
        
        # Try to remove non-existent item
        removed = wishlist.remove_item('product_999')
        assert removed is False
    
    def test_validation_errors(self):
        """Test validation errors."""
        # Missing required fields
        with pytest.raises(DeserializationError):
            DynamoDBWishlist(
                user_id="",  # Empty user_id
                wishlist_id="wishlist_456",
                name="Test",
                share_hash="abcd1234efgh5678ijkl9012mnop3456"
            )
        
        # Share hash too short
        with pytest.raises(DeserializationError):
            DynamoDBWishlist(
                user_id="user_123",
                wishlist_id="wishlist_456",
                name="Test",
                share_hash="short"  # Too short
            )
        
        # Name too long
        with pytest.raises(DeserializationError):
            DynamoDBWishlist(
                user_id="user_123",
                wishlist_id="wishlist_456",
                name="x" * 256,  # Too long
                share_hash="abcd1234efgh5678ijkl9012mnop3456"
            )


class TestDynamoDBWishlistMapper:
    """Test DynamoDB wishlist mapper."""
    
    def test_domain_to_dynamodb(self):
        """Test converting domain model to DynamoDB representation."""
        mapper = DynamoDBWishlistMapper()
        
        domain_wishlist = Wishlist.create_new(
            user_id='user_123',
            name='Test Wishlist'
        )
        domain_wishlist.add_item('product_1', 'Test notes')
        
        db_wishlist = mapper.domain_to_dynamodb(domain_wishlist)
        
        assert isinstance(db_wishlist, DynamoDBWishlist)
        assert db_wishlist.user_id == 'user_123'
        assert db_wishlist.name == 'Test Wishlist'
        assert len(db_wishlist.items) == 1
    
    def test_dynamodb_to_domain(self):
        """Test converting DynamoDB representation to domain model."""
        mapper = DynamoDBWishlistMapper()
        
        db_wishlist = DynamoDBWishlist(
            user_id='user_123',
            wishlist_id='wishlist_456',
            name='Test Wishlist',
            share_hash='abcd1234efgh5678ijkl9012mnop3456'
        )
        
        domain_wishlist = mapper.dynamodb_to_domain(db_wishlist)
        
        assert isinstance(domain_wishlist, Wishlist)
        assert domain_wishlist.user_id == 'user_123'
        assert domain_wishlist.name == 'Test Wishlist'
    
    def test_serialize_for_dynamodb(self):
        """Test serializing domain model for DynamoDB storage."""
        mapper = DynamoDBWishlistMapper()
        
        domain_wishlist = Wishlist.create_new(
            user_id='user_123',
            name='Test Wishlist'
        )
        
        serialized = mapper.serialize_for_dynamodb(domain_wishlist)
        
        assert isinstance(serialized, dict)
        assert serialized['user_id'] == {'S': 'user_123'}
        assert serialized['name'] == {'S': 'Test Wishlist'}
    
    def test_deserialize_from_dynamodb(self):
        """Test deserializing DynamoDB item to domain model."""
        mapper = DynamoDBWishlistMapper()
        
        dynamodb_item = {
            'user_id': {'S': 'user_123'},
            'wishlist_id': {'S': 'wishlist_456'},
            'name': {'S': 'Test Wishlist'},
            'share_hash': {'S': 'abcd1234efgh5678ijkl9012mnop3456'},
            'is_public': {'BOOL': False},
            'is_default': {'BOOL': True},
            'items': {'L': []},
            'created_at': {'S': '2024-01-15T10:30:00'},
            'updated_at': {'S': '2024-01-15T11:30:00'},
            'item_count': {'N': '0'},
            'version': {'N': '1'}
        }
        
        domain_wishlist = mapper.deserialize_from_dynamodb(dynamodb_item)
        
        assert isinstance(domain_wishlist, Wishlist)
        assert domain_wishlist.user_id == 'user_123'
        assert domain_wishlist.name == 'Test Wishlist'
        assert domain_wishlist.is_default is True
    
    def test_batch_operations(self):
        """Test batch serialization and deserialization."""
        mapper = DynamoDBWishlistMapper()
        
        # Create multiple domain wishlists
        wishlists = [
            Wishlist.create_new(user_id='user_1', name='Wishlist 1'),
            Wishlist.create_new(user_id='user_2', name='Wishlist 2')
        ]
        
        # Batch serialize
        serialized_items = mapper.batch_serialize(wishlists)
        
        assert len(serialized_items) == 2
        assert all(isinstance(item, dict) for item in serialized_items)
        
        # Batch deserialize
        deserialized_wishlists = mapper.batch_deserialize(serialized_items)
        
        assert len(deserialized_wishlists) == 2
        assert all(isinstance(w, Wishlist) for w in deserialized_wishlists)
        assert deserialized_wishlists[0].name == 'Wishlist 1'
        assert deserialized_wishlists[1].name == 'Wishlist 2'