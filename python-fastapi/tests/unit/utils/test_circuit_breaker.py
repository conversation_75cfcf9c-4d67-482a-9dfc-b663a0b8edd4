"""
Tests for circuit breaker implementation.
"""
import asyncio
import pytest
from datetime import datetime, timedelta
from unittest.mock import As<PERSON><PERSON><PERSON>, Mock

from app.utils.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerState,
    CircuitBreakerError,
    register_circuit_breaker,
    get_circuit_breaker_stats,
    reset_all_circuit_breakers,
    empty_list_fallback,
    empty_dict_fallback,
    none_fallback,
    default_value_fallback,
    Fallback<PERSON>hain,
    create_service_fallback,
    get_circuit_breaker_summary,
    force_open_circuit_breaker,
    reset_circuit_breaker
)


class TestCircuitBreaker:
    """Test circuit breaker functionality."""

    @pytest.fixture
    def breaker(self):
        """Create a circuit breaker for testing."""
        return CircuitBreaker(
            failure_threshold=3,
            recovery_timeout=1,  # Short timeout for testing
            name="test_breaker"
        )

    @pytest.fixture
    def breaker_with_fallback(self):
        """Create a circuit breaker with fallback for testing."""
        async def test_fallback(*args, **kwargs):
            return "fallback_result"

        return CircuitBreaker(
            failure_threshold=2,
            recovery_timeout=1,
            name="test_breaker_with_fallback",
            fallback_function=test_fallback
        )

    def test_initial_state(self, breaker):
        """Test circuit breaker initial state."""
        assert breaker.state == CircuitBreakerState.CLOSED.value
        assert breaker.failure_count == 0
        assert breaker.success_rate == 100.0
        assert not breaker.has_fallback

    def test_initial_state_with_fallback(self, breaker_with_fallback):
        """Test circuit breaker initial state with fallback."""
        assert breaker_with_fallback.has_fallback

    @pytest.mark.asyncio
    async def test_successful_call(self, breaker):
        """Test successful function call."""
        async def success_func():
            return "success"

        result = await breaker.call(success_func)
        assert result == "success"
        assert breaker.failure_count == 0
        assert breaker._success_count == 1
        assert breaker._total_requests == 1

    @pytest.mark.asyncio
    async def test_failure_handling(self, breaker):
        """Test failure handling."""
        async def failing_func():
            raise ValueError("Test error")

        # First two failures should not open circuit
        for i in range(2):
            with pytest.raises(ValueError):
                await breaker.call(failing_func)
            assert breaker.state == CircuitBreakerState.CLOSED.value
            assert breaker.failure_count == i + 1

        # Third failure should open circuit
        with pytest.raises(ValueError):
            await breaker.call(failing_func)
        assert breaker.state == CircuitBreakerState.OPEN.value
        assert breaker.failure_count == 3

    @pytest.mark.asyncio
    async def test_circuit_open_blocks_requests(self, breaker):
        """Test that open circuit blocks requests."""
        async def failing_func():
            raise ValueError("Test error")

        # Trigger circuit to open
        for _ in range(3):
            with pytest.raises(ValueError):
                await breaker.call(failing_func)

        assert breaker.state == CircuitBreakerState.OPEN.value

        # Next call should be blocked
        with pytest.raises(CircuitBreakerError) as exc_info:
            await breaker.call(failing_func)

        assert "OPEN" in str(exc_info.value)
        assert exc_info.value.circuit_name == "test_breaker"

    @pytest.mark.asyncio
    async def test_fallback_on_open_circuit(self, breaker_with_fallback):
        """Test fallback execution when circuit is open."""
        async def failing_func():
            raise ValueError("Test error")

        # First failure should raise ValueError
        with pytest.raises(ValueError):
            await breaker_with_fallback.call(failing_func)

        # Second failure should open circuit and use fallback
        result = await breaker_with_fallback.call(failing_func)
        assert result == "fallback_result"
        assert breaker_with_fallback.state == CircuitBreakerState.OPEN.value

        # Subsequent calls should also use fallback
        result2 = await breaker_with_fallback.call(failing_func)
        assert result2 == "fallback_result"
        assert breaker_with_fallback._fallback_calls == 2
        assert breaker_with_fallback._fallback_successes == 2

    @pytest.mark.asyncio
    async def test_half_open_recovery(self, breaker):
        """Test recovery through half-open state."""
        async def failing_func():
            raise ValueError("Test error")

        async def success_func():
            return "success"

        # Open the circuit
        for _ in range(3):
            with pytest.raises(ValueError):
                await breaker.call(failing_func)

        assert breaker.state == CircuitBreakerState.OPEN.value

        # Wait for recovery timeout
        await asyncio.sleep(1.1)

        # Next call should move to half-open and succeed
        result = await breaker.call(success_func)
        assert result == "success"
        assert breaker.state == CircuitBreakerState.CLOSED.value
        assert breaker.failure_count == 0

    @pytest.mark.asyncio
    async def test_half_open_failure_returns_to_open(self, breaker):
        """Test that failure in half-open state returns to open."""
        async def failing_func():
            raise ValueError("Test error")

        # Open the circuit
        for _ in range(3):
            with pytest.raises(ValueError):
                await breaker.call(failing_func)

        # Wait for recovery timeout
        await asyncio.sleep(1.1)

        # Failure in half-open should return to open
        with pytest.raises(ValueError):
            await breaker.call(failing_func)

        assert breaker.state == CircuitBreakerState.OPEN.value

    def test_manual_reset(self, breaker):
        """Test manual circuit breaker reset."""
        # Simulate some failures
        breaker._failure_count = 5
        breaker._change_state(CircuitBreakerState.OPEN)

        assert breaker.state == CircuitBreakerState.OPEN.value
        assert breaker.failure_count == 5

        # Reset manually
        breaker.reset()

        assert breaker.state == CircuitBreakerState.CLOSED.value
        assert breaker.failure_count == 0

    def test_force_open(self, breaker):
        """Test manually forcing circuit breaker open."""
        assert breaker.state == CircuitBreakerState.CLOSED.value

        breaker.force_open()

        assert breaker.state == CircuitBreakerState.OPEN.value
        assert breaker._next_attempt_time is not None

    def test_health_check(self, breaker):
        """Test circuit breaker health check."""
        # Healthy state
        assert breaker.is_healthy()

        # Add some failures but keep success rate high
        breaker._success_count = 95
        breaker._total_requests = 100
        breaker._failure_count = 5

        assert breaker.is_healthy()  # 95% success rate

        # Lower success rate
        breaker._success_count = 90
        breaker._total_requests = 100

        assert not breaker.is_healthy()  # 90% success rate

    def test_get_stats(self, breaker):
        """Test getting circuit breaker statistics."""
        stats = breaker.get_stats()

        assert stats["name"] == "test_breaker"
        assert stats["state"] == CircuitBreakerState.CLOSED.value
        assert stats["failure_count"] == 0
        assert stats["success_count"] == 0
        assert stats["total_requests"] == 0
        assert stats["success_rate"] == 100.0
        assert stats["has_fallback"] is False
        assert "state_duration_seconds" in stats
        assert "average_response_time" in stats



class TestFallbackFunctions:
    """Test built-in fallback functions."""

    @pytest.mark.asyncio
    async def test_empty_list_fallback(self):
        """Test empty list fallback."""
        result = await empty_list_fallback()
        assert result == []

    @pytest.mark.asyncio
    async def test_empty_dict_fallback(self):
        """Test empty dict fallback."""
        result = await empty_dict_fallback()
        assert result == {}

    @pytest.mark.asyncio
    async def test_none_fallback(self):
        """Test none fallback."""
        result = await none_fallback()
        assert result is None

    @pytest.mark.asyncio
    async def test_default_value_fallback(self):
        """Test default value fallback."""
        result = await default_value_fallback("default_value")
        assert result == "default_value"

        result = await default_value_fallback({"key": "value"})
        assert result == {"key": "value"}

    @pytest.mark.asyncio
    async def test_fallback_chain(self):
        """Test fallback chain functionality."""
        async def failing_fallback(*args, **kwargs):
            raise ValueError("Fallback failed")

        async def working_fallback(*args, **kwargs):
            return "success"

        # Test chain with failing then working fallback
        chain = FallbackChain(failing_fallback, working_fallback)
        result = await chain()
        assert result == "success"

        # Test chain with all failing fallbacks
        chain = FallbackChain(failing_fallback, failing_fallback)
        with pytest.raises(ValueError):
            await chain()

    @pytest.mark.asyncio
    async def test_create_service_fallback(self):
        """Test service-specific fallback creation."""
        # Test Algolia service fallback
        algolia_fallback = create_service_fallback("algolia")
        result = await algolia_fallback(["product1", "product2"])
        assert result == {}

        result = await algolia_fallback("single_product")
        assert result is None

        # Test CloudFront service fallback
        cloudfront_fallback = create_service_fallback("cloudfront")
        result = await cloudfront_fallback()
        assert result["status"] == "fallback"
        assert "unavailable" in result["message"]

        # Test with custom fallback data
        custom_fallback = create_service_fallback("custom", {"custom": "data"})
        result = await custom_fallback()
        assert result == {"custom": "data"}


class TestCircuitBreakerRegistry:
    """Test circuit breaker registry functionality."""

    def test_register_and_get_stats(self):
        """Test registering circuit breakers and getting stats."""
        breaker1 = CircuitBreaker(name="test_breaker_1")
        breaker2 = CircuitBreaker(name="test_breaker_2")

        register_circuit_breaker("test_breaker_1", breaker1)
        register_circuit_breaker("test_breaker_2", breaker2)

        stats = get_circuit_breaker_stats()

        assert "test_breaker_1" in stats
        assert "test_breaker_2" in stats
        assert stats["test_breaker_1"]["name"] == "test_breaker_1"
        assert stats["test_breaker_2"]["name"] == "test_breaker_2"

    def test_reset_all_circuit_breakers(self):
        """Test resetting all circuit breakers."""
        breaker1 = CircuitBreaker(name="test_breaker_1")
        breaker2 = CircuitBreaker(name="test_breaker_2")

        # Simulate failures
        breaker1._failure_count = 3
        breaker1._change_state(CircuitBreakerState.OPEN)
        breaker2._failure_count = 2

        register_circuit_breaker("test_breaker_1", breaker1)
        register_circuit_breaker("test_breaker_2", breaker2)

        # Reset all
        reset_all_circuit_breakers()

        assert breaker1.state == CircuitBreakerState.CLOSED.value
        assert breaker1.failure_count == 0
        assert breaker2.failure_count == 0

    def test_circuit_breaker_summary(self):
        """Test circuit breaker summary functionality."""
        # Create test breakers
        breaker1 = CircuitBreaker(name="test_summary_1")
        breaker2 = CircuitBreaker(name="test_summary_2")
        breaker3 = CircuitBreaker(name="test_summary_3")

        # Set different states
        breaker2._change_state(CircuitBreakerState.OPEN)
        breaker3._change_state(CircuitBreakerState.HALF_OPEN)

        register_circuit_breaker("test_summary_1", breaker1)
        register_circuit_breaker("test_summary_2", breaker2)
        register_circuit_breaker("test_summary_3", breaker3)

        summary = get_circuit_breaker_summary()

        assert summary["total_circuit_breakers"] >= 3
        assert summary["closed"] >= 1
        assert summary["open"] >= 1
        assert summary["half_open"] >= 1
        assert "overall_health_percentage" in summary

    def test_force_open_and_reset_circuit_breaker(self):
        """Test forcing circuit breaker open and resetting."""
        breaker = CircuitBreaker(name="test_force_reset")
        register_circuit_breaker("test_force_reset", breaker)

        # Test force open
        assert breaker.state == CircuitBreakerState.CLOSED.value
        success = force_open_circuit_breaker("test_force_reset")
        assert success is True
        assert breaker.state == CircuitBreakerState.OPEN.value

        # Test reset
        success = reset_circuit_breaker("test_force_reset")
        assert success is True
        assert breaker.state == CircuitBreakerState.CLOSED.value

        # Test with non-existent breaker
        success = force_open_circuit_breaker("non_existent")
        assert success is False

        success = reset_circuit_breaker("non_existent")
        assert success is False


@pytest.mark.asyncio
async def test_timeout_handling():
    """Test circuit breaker timeout handling."""
    breaker = CircuitBreaker(
        failure_threshold=2,
        recovery_timeout=1,
        timeout=0.1,  # 100ms timeout
        name="timeout_test"
    )

    async def slow_func():
        await asyncio.sleep(0.2)  # Slower than timeout
        return "success"

    # Should timeout and count as failure
    with pytest.raises(asyncio.TimeoutError):
        await breaker.call(slow_func)

    assert breaker.failure_count == 1

    # Another timeout should open circuit
    with pytest.raises(asyncio.TimeoutError):
        await breaker.call(slow_func)

    assert breaker.state == CircuitBreakerState.OPEN.value
