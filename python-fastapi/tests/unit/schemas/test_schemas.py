"""
Tests for Pydantic schemas.

This module tests all request and response schemas to ensure proper validation,
serialization, and error handling.
"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from app.schemas.requests.wishlist import (
    CreateWishlistRequest,
    UpdateWishlistRequest,
    GetWishlistsRequest
)
from app.schemas.requests.item import (
    AddItemRequest,
    BulkAddItemsRequest
)
from app.schemas.responses.wishlist import (
    WishlistResponse,
    WishlistItemResponse,
    ProductDataResponse
)
from app.schemas.base import (
    LanguageCode,
    CountryCode,
    validate_user_id,
    validate_product_id,
    validate_wishlist_name
)


class TestBaseValidators:
    """Test base validation functions."""
    
    def test_validate_user_id_valid(self):
        """Test valid user ID validation."""
        assert validate_user_id("user_12345") == "user_12345"
        assert validate_user_id("  user_12345  ") == "user_12345"
        assert validate_user_id("user-123_abc") == "user-123_abc"
    
    def test_validate_user_id_invalid(self):
        """Test invalid user ID validation."""
        with pytest.raises(ValueError, match="user_id cannot be empty"):
            validate_user_id("")
        
        with pytest.raises(ValueError, match="user_id cannot be empty"):
            validate_user_id("   ")
        
        with pytest.raises(ValueError, match="must be at least 3 characters"):
            validate_user_id("ab")
        
        with pytest.raises(ValueError, match="cannot exceed 50 characters"):
            validate_user_id("a" * 51)
        
        with pytest.raises(ValueError, match="contains invalid characters"):
            validate_user_id("user@123")
    
    def test_validate_product_id_valid(self):
        """Test valid product ID validation."""
        assert validate_product_id("product_123") == "product_123"
        assert validate_product_id("  product_123  ") == "product_123"
    
    def test_validate_product_id_invalid(self):
        """Test invalid product ID validation."""
        with pytest.raises(ValueError, match="product_id cannot be empty"):
            validate_product_id("")
        
        with pytest.raises(ValueError, match="cannot exceed 100 characters"):
            validate_product_id("a" * 101)
    
    def test_validate_wishlist_name_valid(self):
        """Test valid wishlist name validation."""
        assert validate_wishlist_name("My Wishlist") == "My Wishlist"
        assert validate_wishlist_name("  My Wishlist  ") == "My Wishlist"
    
    def test_validate_wishlist_name_invalid(self):
        """Test invalid wishlist name validation."""
        with pytest.raises(ValueError, match="name cannot be empty"):
            validate_wishlist_name("")
        
        with pytest.raises(ValueError, match="cannot exceed 255 characters"):
            validate_wishlist_name("a" * 256)
        
        with pytest.raises(ValueError, match="contains invalid characters"):
            validate_wishlist_name("Name with <script>")


class TestCreateWishlistRequest:
    """Test CreateWishlistRequest schema."""
    
    def test_valid_request(self):
        """Test valid wishlist creation request."""
        data = {
            "user_id": "user_12345",
            "name": "My Birthday Wishlist",
            "is_default": True,
            "country": "ae",
            "language": "en"
        }
        
        request = CreateWishlistRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.name == "My Birthday Wishlist"
        assert request.is_default is True
        assert request.country == CountryCode.UAE
        assert request.language == LanguageCode.ENGLISH
    
    def test_minimal_request(self):
        """Test minimal valid request with defaults."""
        data = {
            "user_id": "user_12345",
            "name": "My Wishlist"
        }
        
        request = CreateWishlistRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.name == "My Wishlist"
        assert request.is_default is False
        assert request.country == CountryCode.UAE
        assert request.language == LanguageCode.ENGLISH
    
    def test_invalid_user_id(self):
        """Test invalid user ID in request."""
        data = {
            "user_id": "",
            "name": "My Wishlist"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            CreateWishlistRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("user_id cannot be empty" in str(error) for error in errors)
    
    def test_invalid_name(self):
        """Test invalid name in request."""
        data = {
            "user_id": "user_12345",
            "name": ""
        }
        
        with pytest.raises(ValidationError) as exc_info:
            CreateWishlistRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("String should have at least 1 character" in str(error) for error in errors)
    
    def test_country_normalization(self):
        """Test country code normalization."""
        data = {
            "user_id": "user_12345",
            "name": "My Wishlist",
            "country": "UAE"
        }
        
        request = CreateWishlistRequest(**data)
        assert request.country == CountryCode.UAE
    
    def test_language_normalization(self):
        """Test language code normalization."""
        data = {
            "user_id": "user_12345",
            "name": "My Wishlist",
            "language": "ENGLISH"
        }
        
        request = CreateWishlistRequest(**data)
        assert request.language == LanguageCode.ENGLISH


class TestUpdateWishlistRequest:
    """Test UpdateWishlistRequest schema."""
    
    def test_valid_update(self):
        """Test valid wishlist update request."""
        data = {
            "user_id": "user_12345",
            "name": "Updated Name",
            "is_default": True
        }
        
        request = UpdateWishlistRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.name == "Updated Name"
        assert request.is_default is True
    
    def test_partial_update(self):
        """Test partial update with only name."""
        data = {
            "user_id": "user_12345",
            "name": "New Name"
        }
        
        request = UpdateWishlistRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.name == "New Name"
        assert request.is_default is None
    
    def test_empty_update_fails(self):
        """Test that empty update fails validation."""
        data = {
            "user_id": "user_12345"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            UpdateWishlistRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("At least one field must be provided" in str(error) for error in errors)


class TestAddItemRequest:
    """Test AddItemRequest schema."""
    
    def test_valid_request(self):
        """Test valid add item request."""
        data = {
            "user_id": "user_12345",
            "product_id": "product_abc123",
            "notes": "Size M preferred",
            "quantity": 2,
            "priority": 4
        }
        
        request = AddItemRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.product_id == "product_abc123"
        assert request.notes == "Size M preferred"
        assert request.quantity == 2
        assert request.priority == 4
    
    def test_minimal_request(self):
        """Test minimal valid request with defaults."""
        data = {
            "user_id": "user_12345",
            "product_id": "product_abc123"
        }
        
        request = AddItemRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.product_id == "product_abc123"
        assert request.notes is None
        assert request.quantity == 1
        assert request.priority == 1
    
    def test_invalid_quantity(self):
        """Test invalid quantity values."""
        data = {
            "user_id": "user_12345",
            "product_id": "product_abc123",
            "quantity": 0
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AddItemRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("quantity must be between 1 and 99" in str(error) for error in errors)
    
    def test_invalid_priority(self):
        """Test invalid priority values."""
        data = {
            "user_id": "user_12345",
            "product_id": "product_abc123",
            "priority": 6
        }
        
        with pytest.raises(ValidationError) as exc_info:
            AddItemRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("priority must be between 1 and 5" in str(error) for error in errors)


class TestBulkAddItemsRequest:
    """Test BulkAddItemsRequest schema."""
    
    def test_valid_bulk_request(self):
        """Test valid bulk add items request."""
        data = {
            "user_id": "user_12345",
            "items": [
                {
                    "product_id": "product_1",
                    "notes": "Size M",
                    "quantity": 1,
                    "priority": 3
                },
                {
                    "product_id": "product_2",
                    "notes": "Blue color",
                    "quantity": 2,
                    "priority": 1
                }
            ],
            "skip_duplicates": True
        }
        
        request = BulkAddItemsRequest(**data)
        
        assert request.user_id == "user_12345"
        assert len(request.items) == 2
        assert request.items[0].product_id == "product_1"
        assert request.items[1].product_id == "product_2"
        assert request.skip_duplicates is True
    
    def test_empty_items_fails(self):
        """Test that empty items list fails validation."""
        data = {
            "user_id": "user_12345",
            "items": []
        }
        
        with pytest.raises(ValidationError) as exc_info:
            BulkAddItemsRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("items list cannot be empty" in str(error) for error in errors)
    
    def test_duplicate_product_ids_fails(self):
        """Test that duplicate product IDs fail validation."""
        data = {
            "user_id": "user_12345",
            "items": [
                {"product_id": "product_1"},
                {"product_id": "product_1"}  # Duplicate
            ]
        }
        
        with pytest.raises(ValidationError) as exc_info:
            BulkAddItemsRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("duplicate product_ids found" in str(error) for error in errors)


class TestGetWishlistsRequest:
    """Test GetWishlistsRequest schema."""
    
    def test_valid_request(self):
        """Test valid get wishlists request."""
        data = {
            "user_id": "user_12345",
            "include_items": True,
            "include_product_details": False,
            "page": 2,
            "page_size": 10,
            "sort_by": "name",
            "sort_order": "asc"
        }
        
        request = GetWishlistsRequest(**data)
        
        assert request.user_id == "user_12345"
        assert request.include_items is True
        assert request.include_product_details is False
        assert request.page == 2
        assert request.page_size == 10
        assert request.sort_by == "name"
        assert request.sort_order == "asc"
    
    def test_defaults(self):
        """Test default values."""
        data = {
            "user_id": "user_12345"
        }
        
        request = GetWishlistsRequest(**data)
        
        assert request.include_items is True
        assert request.include_product_details is True
        assert request.page == 1
        assert request.page_size == 20
        assert request.sort_by == "updated_at"
        assert request.sort_order == "desc"
    
    def test_invalid_sort_by(self):
        """Test invalid sort_by field."""
        data = {
            "user_id": "user_12345",
            "sort_by": "invalid_field"
        }
        
        with pytest.raises(ValidationError) as exc_info:
            GetWishlistsRequest(**data)
        
        errors = exc_info.value.errors()
        assert any("sort_by must be one of" in str(error) for error in errors)


class TestWishlistResponse:
    """Test WishlistResponse schema."""
    
    def test_valid_response(self):
        """Test valid wishlist response."""
        now = datetime.utcnow()
        
        data = {
            "user_id": "user_12345",
            "wishlist_id": "wishlist_abc123",
            "name": "My Wishlist",
            "is_default": True,
            "is_public": False,
            "share_hash": "abc123def456ghi789",
            "item_count": 2,
            "items": [
                {
                    "product_id": "product_1",
                    "notes": "Size M",
                    "quantity": 1,
                    "priority": 3,
                    "added_at": now.isoformat() + "Z"
                }
            ],
            "created_at": now.isoformat() + "Z",
            "updated_at": now.isoformat() + "Z"
        }
        
        response = WishlistResponse(**data)
        
        assert response.user_id == "user_12345"
        assert response.wishlist_id == "wishlist_abc123"
        assert response.name == "My Wishlist"
        assert response.is_default is True
        assert response.is_public is False
        assert response.share_hash == "abc123def456ghi789"
        assert response.item_count == 2
        assert len(response.items) == 1
        assert response.items[0].product_id == "product_1"
        assert response.share_url is not None
        assert "abc123def456ghi789" in response.share_url
    
    def test_share_url_generation(self):
        """Test automatic share URL generation."""
        now = datetime.utcnow()
        
        data = {
            "user_id": "user_12345",
            "wishlist_id": "wishlist_abc123",
            "name": "My Wishlist",
            "is_default": False,
            "is_public": True,
            "share_hash": "test_hash_123",
            "item_count": 0,
            "created_at": now.isoformat() + "Z",
            "updated_at": now.isoformat() + "Z"
        }
        
        response = WishlistResponse(**data)
        
        assert response.share_url == "https://api.example.com/api/v1/wishlists/shared/test_hash_123"


class TestProductDataResponse:
    """Test ProductDataResponse schema."""
    
    def test_valid_product_data(self):
        """Test valid product data response."""
        data = {
            "product_id": "product_abc123",
            "title": "Wireless Headphones",
            "description": "High-quality wireless headphones",
            "price": 99.99,
            "currency": "AED",
            "image_url": "https://example.com/image.jpg",
            "brand": "TechBrand",
            "category": "Electronics",
            "availability": "in_stock",
            "rating": 4.5,
            "review_count": 128,
            "url": "https://example.com/product"
        }
        
        response = ProductDataResponse(**data)
        
        assert response.product_id == "product_abc123"
        assert response.title == "Wireless Headphones"
        assert response.price == 99.99
        assert response.currency == "AED"
        assert response.rating == 4.5
        assert response.review_count == 128
    
    def test_minimal_product_data(self):
        """Test minimal product data with only required fields."""
        data = {
            "product_id": "product_abc123"
        }
        
        response = ProductDataResponse(**data)
        
        assert response.product_id == "product_abc123"
        assert response.title is None
        assert response.price is None
        assert response.currency is None


if __name__ == "__main__":
    pytest.main([__file__])