version: '3.8'

services:
  # FastAPI Application
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - .:/app
      - /app/.venv  # Exclude venv from volume mount
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - REDIS_URL=redis://redis:6379/0
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
      - ALGOLIA_APP_ID=${ALGOLIA_APP_ID:-test_app_id}
      - ALGOLIA_API_KEY=${ALGOLIA_API_KEY:-test_api_key}
    depends_on:
      - redis
      - dynamodb
    networks:
      - wishlist-network
    restart: unless-stopped

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    networks:
      - wishlist-network
    restart: unless-stopped

  # DynamoDB Local
  dynamodb:
    image: amazon/dynamodb-local:latest
    ports:
      - "8001:8000"
    volumes:
      - dynamodb_data:/home/<USER>/data
    command: ["-jar", "DynamoDBLocal.jar", "-sharedDb", "-dbPath", "/home/<USER>/data/"]
    networks:
      - wishlist-network
    restart: unless-stopped

  # DynamoDB Admin UI
  dynamodb-admin:
    image: aaronshaf/dynamodb-admin:latest
    ports:
      - "8002:8001"
    environment:
      - DYNAMO_ENDPOINT=http://dynamodb:8000
    depends_on:
      - dynamodb
    networks:
      - wishlist-network
    restart: unless-stopped

  # Redis Commander (Redis UI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    ports:
      - "8003:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - wishlist-network
    restart: unless-stopped

  # Celery Worker
  celery-worker:
    build:
      context: .
      dockerfile: Dockerfile.dev
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - ENVIRONMENT=development
      - DEBUG=true
      - REDIS_URL=redis://redis:6379/0
      - DYNAMODB_ENDPOINT=http://dynamodb:8000
      - ALGOLIA_APP_ID=${ALGOLIA_APP_ID:-test_app_id}
      - ALGOLIA_API_KEY=${ALGOLIA_API_KEY:-test_api_key}
    command: poetry run celery -A app.infrastructure.queue.celery_app worker --loglevel=info
    depends_on:
      - redis
      - dynamodb
    networks:
      - wishlist-network
    restart: unless-stopped

  # Celery Flower (Celery monitoring)
  celery-flower:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "5555:5555"
    volumes:
      - .:/app
      - /app/.venv
    environment:
      - ENVIRONMENT=development
      - REDIS_URL=redis://redis:6379/0
    command: poetry run celery -A app.infrastructure.queue.celery_app flower --port=5555
    depends_on:
      - redis
      - celery-worker
    networks:
      - wishlist-network
    restart: unless-stopped

volumes:
  redis_data:
  dynamodb_data:

networks:
  wishlist-network:
    driver: bridge