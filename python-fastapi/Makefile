.PHONY: help install dev-install format lint test test-cov clean run docker-build docker-run

help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

install: ## Install production dependencies
	poetry install --only=main

dev-install: ## Install all dependencies including dev
	poetry install
	poetry run pre-commit install

format: ## Format code with black and isort
	poetry run black .
	poetry run isort .

lint: ## Run linting tools
	poetry run black --check .
	poetry run isort --check-only .
	poetry run flake8 .
	poetry run mypy .

test: ## Run tests
	poetry run pytest

test-cov: ## Run tests with coverage
	poetry run pytest --cov=app --cov-report=html --cov-report=term

test-fast: ## Run tests without coverage
	poetry run pytest -x --tb=short

clean: ## Clean up build artifacts
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf htmlcov/
	find . -type d -name __pycache__ -delete
	find . -type f -name "*.pyc" -delete

run: ## Run the development server
	poetry run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

docker-build: ## Build Docker image
	docker build -t mumzworld-wishlist:latest .

docker-run: ## Run with Docker Compose
	docker-compose up --build

pre-commit: ## Run pre-commit hooks
	poetry run pre-commit run --all-files

setup: dev-install ## Complete development setup
	@echo "Development environment setup complete!"
	@echo "Run 'make run' to start the development server"