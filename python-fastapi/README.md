# Mumzworld Wishlist Service - FastAPI

High-performance wishlist microservice built with FastAPI, designed to replace the existing Laravel implementation with superior performance and modern Python ecosystem practices.

## Features

- **Ultra-high performance**: Built with FastAPI and async/await patterns
- **Modern Python**: Uses Python 3.11+ with type hints and modern tooling
- **AWS App Runner**: Serverless deployment with automatic scaling
- **Comprehensive testing**: Unit, integration, and performance tests
- **Production-ready**: Monitoring, logging, and observability built-in

## Development Setup

### Prerequisites

- Python 3.11+
- Poetry (for dependency management)
- Docker and Docker Compose (for local development)

### Quick Start

1. Install dependencies:
```bash
poetry install
```

2. Activate virtual environment:
```bash
poetry shell
```

3. Run development server:
```bash
uvicorn app.main:app --reload
```

### Using Docker

```bash
docker-compose up --build
```

## Project Structure

```
app/
├── api/           # API routes and endpoints
├── core/          # Core functionality (security, exceptions, etc.)
├── services/      # Business logic services
├── repositories/  # Data access layer
├── models/        # Data models (domain and database)
├── schemas/       # Pydantic schemas for validation
├── infrastructure/# External service integrations
└── utils/         # Utility functions
```

## Development Workflow

This project uses modern Python development practices:

- **Poetry**: Dependency management and virtual environments
- **Black**: Code formatting
- **isort**: Import sorting
- **mypy**: Static type checking
- **pre-commit**: Git hooks for code quality
- **pytest**: Testing framework

## Testing

Run tests:
```bash
pytest
```

Run with coverage:
```bash
pytest --cov=app
```

## Deployment

The service is designed to run on AWS App Runner with automatic scaling and zero-downtime deployments.

See `deployment/` directory for infrastructure configuration.